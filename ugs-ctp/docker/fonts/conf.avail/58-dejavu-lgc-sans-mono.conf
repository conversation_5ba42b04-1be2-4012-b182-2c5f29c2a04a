<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE fontconfig SYSTEM "../fonts.dtd">
<!-- /etc/fonts/conf.d/58-dejavu-lgc-sans-mono.conf

     Define aliasing and other fontconfig settings for
     DejaVu LGC Sans Mono.

     © 2006-2008 Nicolas Mailhot <nicolas.mailhot at laposte.net>
-->
<fontconfig>
  <!-- Font substitution rules -->
  <alias binding="same">
    <family>Bepa Mono</family>
    <accept>
      <family>DejaVu LGC Sans Mono</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bitstream Prima Sans Mono</family>
    <accept>
      <family>DejaVu LGC Sans Mono</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bitstream Vera Sans Mono</family>
    <accept>
      <family>DejaVu LGC Sans Mono</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>DejaVu Sans Mono</family>
    <accept>
      <family>DejaVu LGC Sans Mono</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Olwen Sans Mono</family>
    <accept>
      <family>DejaVu LGC Sans Mono</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>SUSE Sans Mono</family>
    <accept>
      <family>DejaVu LGC Sans Mono</family>
    </accept>
  </alias>
  <!-- Generic name assignment -->
  <alias>
    <family>DejaVu LGC Sans Mono</family>
    <default>
      <family>monospace</family>
    </default>
  </alias>
  <!-- Generic name aliasing -->
  <alias>
    <family>monospace</family>
    <prefer>
      <family>DejaVu LGC Sans Mono</family>
    </prefer>
  </alias>
</fontconfig>
