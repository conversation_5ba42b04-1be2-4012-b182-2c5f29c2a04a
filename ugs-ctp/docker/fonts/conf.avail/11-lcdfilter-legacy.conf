<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
<!--  Use lcdlegacy as default for LCD filter -->
  <match target="pattern">
    <!--
      This configuration is available on the major desktop environments.
      We shouldn't overwrite it with "assign" unconditionally.
      Most clients may picks up the first value only. so using "append"
      may simply works to avoid it.
    -->
    <edit mode="append" name="lcdfilter">
      <const>lcdlegacy</const>
    </edit>
  </match>
</fontconfig>
