<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
<!-- 
	The Bitstream Vera fonts have GASP entries suggesting that hinting be
	disabled below 8 ppem, but FreeType ignores those, preferring to use
	the data found in the instructed hints.  The initial Vera release
	didn't include the right instructions in the 'prep' table. Fix this
	by disabling hinting manually at smaller sizes (< 8ppem)
 -->

	<match target="font">
		<test name="family" compare="eq" ignore-blanks="true">
			<string>Bitstream Vera Sans</string>
		</test>
		<test name="pixelsize" compare="less">
			<double>7.5</double>
		</test>
		<edit name="hinting">
			<bool>false</bool>
		</edit>
	</match>

	<match target="font">
		<test name="family" compare="eq" ignore-blanks="true">
			<string>Bitstream Vera Serif</string>
		</test>
		<test name="pixelsize" compare="less">
			<double>7.5</double>
		</test>
		<edit name="hinting">
			<bool>false</bool>
		</edit>
	</match>

	<match target="font">
		<test name="family" compare="eq" ignore-blanks="true">
			<string>Bitstream Vera Sans Mono</string>
		</test>
		<test name="pixelsize" compare="less">
			<double>7.5</double>
		</test>
		<edit name="hinting">
			<bool>false</bool>
		</edit>
	</match>

</fontconfig>
