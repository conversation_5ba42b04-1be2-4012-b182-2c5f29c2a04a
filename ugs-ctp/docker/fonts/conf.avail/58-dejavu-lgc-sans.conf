<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE fontconfig SYSTEM "../fonts.dtd">
<!-- /etc/fonts/conf.d/58-dejavu-lgc-sans.conf

     Define aliasing and other fontconfig settings for
     DejaVu LGC Sans.

     © 2006-2008 Nicolas <PERSON>hot <nicolas.mailhot at laposte.net>
-->
<fontconfig>
  <!-- Font substitution rules -->
  <alias binding="same">
    <family>Arev Sans</family>
    <accept>
      <family>DejaVu LGC Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bepa</family>
    <accept>
      <family>DejaVu LGC Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bitstream Prima Sans</family>
    <accept>
      <family>DejaVu LGC Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bitstream Vera Sans</family>
    <accept>
      <family>DejaVu LGC Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>DejaVu Sans</family>
    <accept>
      <family>DejaVu LGC Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Hunky Sans</family>
    <accept>
      <family>DejaVu LGC Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Olwen Sans</family>
    <accept>
      <family>DejaVu LGC Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>SUSE Sans</family>
    <accept>
      <family>DejaVu LGC Sans</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Verajja</family>
    <accept>
      <family>DejaVu LGC Sans</family>
    </accept>
  </alias>
  <!-- In case VerajjaPDA stops declaring itself as Verajja -->
  <alias binding="same">
    <family>VerajjaPDA</family>
    <accept>
      <family>DejaVu LGC Sans</family>
    </accept>
  </alias>
  <!-- Generic name assignment -->
  <alias>
    <family>DejaVu LGC Sans</family>
    <default>
      <family>sans-serif</family>
    </default>
  </alias>
  <!-- Generic name aliasing -->
  <alias>
    <family>sans-serif</family>
    <prefer>
      <family>DejaVu LGC Sans</family>
    </prefer>
  </alias>
</fontconfig>
