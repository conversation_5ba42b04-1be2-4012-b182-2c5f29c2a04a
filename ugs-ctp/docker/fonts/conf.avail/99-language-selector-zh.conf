<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>

	<match target="font" >
		<test name="family" compare="contains" >
			<string>Song</string>
		</test> 
                <!-- check to see if the font is just regular -->
                <test name="weight" compare="less_eq">
                        <int>100</int>
		</test>
		<test compare="more_eq" target="pattern" name="weight" >
			<int>180</int>
		</test>
		<edit mode="assign" name="embolden" >
			<bool>true</bool>
		</edit>
	</match>

	<match target="font" >
		<test name="family" compare="contains" >
			<string>Sun</string>
		</test> 
                <!-- check to see if the font is just regular -->
                <test name="weight" compare="less_eq">
                        <int>100</int>
		</test>
		<test compare="more_eq" target="pattern" name="weight" >
			<int>180</int>
		</test>
		<edit mode="assign" name="embolden" >
			<bool>true</bool>
		</edit>
	</match>

	<match target="font" >
		<test name="family" compare="contains" >
			<string>Kai</string>
		</test> 
                <!-- check to see if the font is just regular -->
                <test name="weight" compare="less_eq">
                        <int>100</int>
		</test>
		<test compare="more_eq" target="pattern" name="weight" >
			<int>180</int>
		</test>
		<edit mode="assign" name="embolden" >
			<bool>true</bool>
		</edit>
	</match>

	<match target="font" >
		<test name="family" compare="contains" >
			<string>Ming</string>
		</test> 
                <!-- check to see if the font is just regular -->
                <test name="weight" compare="less_eq">
                        <int>100</int>
		</test>
		<test compare="more_eq" target="pattern" name="weight" >
			<int>180</int>
		</test>
		<edit mode="assign" name="embolden" >
			<bool>true</bool>
		</edit>
	</match>

</fontconfig>
