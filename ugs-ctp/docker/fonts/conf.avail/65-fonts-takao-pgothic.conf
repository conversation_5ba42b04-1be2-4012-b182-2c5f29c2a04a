<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
    <alias>
        <family>TakaoPGothic</family>
        <default>
            <family>sans-serif</family>
        </default>
    </alias>
    <match target="pattern">
        <test name="lang" compare="contains">
            <string>ja</string>
        </test>
         <test qual="any" name="family">
            <string>sans-serif</string>
        </test>
        <edit name="family" mode="prepend" binding="strong">
            <string>TakaoPGothic</string>
        </edit>
    </match>
    <match target="pattern">
        <test qual="any" name="family">
            <string>GothicBBB</string>
        </test>
        <edit name="family" mode="prepend" binding="strong">
            <string>TakaoPGothic</string>
        </edit>
    </match>
    <match target="font">
        <test name="family" compare="contains">
            <string>TakaoPGothic</string>
        </test>
        <test name="pixelsize" compare="less_eq">
            <double>18</double>
        </test>
        <edit name="hintstyle" mode="assign">
            <const>hintnone</const>
        </edit>
        <edit name="embeddedbitmap">
             <bool>false</bool>
        </edit>
    </match>
</fontconfig>
