<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
<!--
  URW provides metric and shape compatible fonts for some Adobe families.
  Most of these are handled in 30-metric-aliases.conf.
  -->
	<alias binding="same">
	  <family>Zapf Dingbats</family>
	  <accept><family>Dingbats</family></accept>
	</alias>
	<alias binding="same">
	  <family>ITC Zapf Dingbats</family>
	  <accept><family>Dingbats</family></accept>
	</alias>
	<match target="pattern">
	  <test name="family" compare="eq" ignore-blanks="true">
	    <string>Symbol</string>
	  </test>
	  <edit name="family" mode="append" binding="same">
	    <string>Standard Symbols L</string>
	  </edit>
	</match>
</fontconfig>
