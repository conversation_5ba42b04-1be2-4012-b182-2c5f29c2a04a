version: "3"
services:
  ugs-ctp:
    container_name: "ugs-ctp"
    image: harbor.unionstrongtech.com/ugs/ctp_a4000:1.3.4.rc0
    volumes:
      - ./dist:/ugs_ctp/dist
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    working_dir: /ugs_ctp/
    env_file:
      - .env
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    command: bash -c "pip install --force-reinstall dist/*.whl && ugsctp"
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true
