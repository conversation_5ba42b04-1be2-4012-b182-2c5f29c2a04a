import SimpleITK as sitk



def boing(img, brain_mask, cta_vessel, other_value):
    img = sitk.GetArrayFromImage(img)
    img_mask_array = sitk.GetArrayFromImage(sitk.ReadImage(cta_vessel))
    img[img_mask_array == 1] = other_value  # 去掉骨头
    img[(brain_mask == 0) & (img_mask_array == 0)] = other_value  # 非脑组织和非血管+非骨头区 求交集, 去掉多余的杂质
    # 对于CTP来说，每个序列都得做相应的去骨，去骨模型中label=2为血管，若只使用第一个序列做 交集操作，则许多血管会被删掉
    return img
