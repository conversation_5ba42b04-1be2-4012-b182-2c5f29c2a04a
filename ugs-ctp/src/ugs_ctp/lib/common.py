#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : common.py
@Project : uguard_ctp
<AUTHOR> mingxing
@Date    : 2022/9/15 16:46
"""
import copy
import datetime
import json
import os
import random
import shutil
import traceback

import pika
import pydicom
import requests
from pydicom import uid, Dataset
from pydicom.dataset import FileMetaDataset, FileDataset
from requests.auth import HTTPBasicAuth
import numpy as np
from PIL import Image, ImageDraw, ImageFont

from lib.const import Config, Consts
from lib.logger import MyLogger

log = MyLogger()


class StringUtils:
    __BOOL_VALID = {"true": True, "1": True, "false": False, "0": False}

    @staticmethod
    def to_bool(value):
        if isinstance(value, bool):
            return value
        if not isinstance(value, str):
            raise ValueError("invalid literal for boolean, Not a string.")
        lower_value = value.lower()
        if lower_value not in StringUtils.__BOOL_VALID:
            raise ValueError("invalid literal for boolean: {}".format(value))
        return StringUtils.__BOOL_VALID[lower_value]

    @staticmethod
    def delete_extra_zero(num):
        """
        删除小数点后多余的0

        :param num: 数字
        :return:
        """

        if isinstance(num, str):
            return num
        num = '{:g}'.format(num)
        # 含小数点转float否则int
        num = float(num) if '.' in num else int(num)
        return num


class RabbitProducer:
    ALGORITHM_RESULT = "algorithm_ctp_aspects_result"
    ALGORITHM_STATUS = "algorithm_working_status"

    @staticmethod
    def send(message: dict, queue_name: str = ALGORITHM_RESULT):
        """
        发送消息（提供统一方法）
        RabbitMQ消息生产者

        :param message: 消息
        :param queue_name: 队列名称（默认发送算法结果）
        :return:
        """

        connection = pika.BlockingConnection(
            pika.ConnectionParameters(Config.MQ_HOST, Config.MQ_PORT, "/",
                                      pika.PlainCredentials(Config.MQ_USERNAME, Config.MQ_PASSWORD),
                                      heartbeat=0))
        chanel = connection.channel()
        chanel.queue_declare(queue=queue_name, durable=True)
        body = json.dumps(message)
        chanel.basic_publish(exchange="", routing_key=queue_name,
                             properties=pika.BasicProperties(
                                 content_type="application/json",
                                 delivery_mode=2),
                             body=body)
        log.info("MQ send > queue:{}, message:{}".format(queue_name, body))
        connection.close()


class FileUtils:

    @staticmethod
    def rebuild_dirs(dir_path):
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
        os.makedirs(dir_path)

    @staticmethod
    def get_filename_without_suffix(path):
        return os.path.splitext(path)[0]

    @staticmethod
    def get_all_file(path, suffix=""):
        """
        获取指定目录下的所有文件

        :param suffix:
        :param path: 文件路径
        :return: 文件列表
        """
        file_list = []
        sub_dir_list = []
        if os.path.isfile(path):
            file_list.append(path)
            return file_list
        sub_dir_list.append(path)
        while len(sub_dir_list):
            dir_path = sub_dir_list.pop()
            for name in sorted(os.listdir(dir_path)):
                file_path = os.path.join(dir_path, name)
                if os.path.isdir(file_path):
                    sub_dir_list.append(file_path)
                    continue
                if not suffix or (suffix and name.endswith(suffix)):
                    file_list.append(file_path)
        return file_list

    @staticmethod
    def get_one(dir_path):
        """
        获取目录下任意一个文件

        :param dir_path: 目录路径
        :return:
        """
        for root, dirs, files in os.walk(dir_path):
            if not files:
                return None
            for file in files:
                return os.path.join(dir_path, file)

    @staticmethod
    def copy_file(source_file, target_file):
        shutil.copy(source_file, target_file)

    @staticmethod
    def save_json(content: dict, file_path: str, sort_keys: bool = True, indent: int = 4):
        with open(file_path, "w", encoding="utf8") as f:
            json.dump(content, f, sort_keys=sort_keys, indent=indent)

    @staticmethod
    def read_json(file_path):
        with open(file_path, "r", encoding="utf8") as f:
            return json.load(f)


class DicomUtils:

    @staticmethod
    def read_dcm(file_path):
        return pydicom.dcmread(file_path, force=True)

    @staticmethod
    def generate_uid():
        """
        生成唯一标识

        :return:
        """
        return pydicom.uid.generate_uid()

    @staticmethod
    def generate_child_uid(parent_uid):
        """
        根据父标识，生成子标识

        :param parent_uid: 父标识
        :return:
        """
        tem_uid = parent_uid.split(".")[:-1]
        tem_uid.append(str(random.randint(10 ** 37, 10 ** 38 - 1)))
        return ".".join(tem_uid)

    @staticmethod
    def get_tags_from_dir(dcm_dir):
        """
        获取DICOM标签

        :param dcm_dir: DICOM文件目录
        :return:
        """
        for root, dir, files in os.walk(dcm_dir):
            if not files:
                return False, None
            for file in files:
                ds = DicomUtils.read_dcm(os.path.join(dcm_dir, file))
                if ds:
                    return ds

    @staticmethod
    def dcm2png(dcm_path, output_path):
        ds = pydicom.dcmread(dcm_path)
        img = ds.pixel_array.astype(float)
        if hasattr(ds, "WindowCenter") and hasattr(ds, "WindowWidth"):
            window_level = int(ds.WindowCenter[0]) if isinstance(ds.WindowCenter, list) else int(ds.WindowCenter)
            window_width = int(ds.WindowWidth[0]) if isinstance(ds.WindowWidth, list) else int(ds.WindowWidth)
            img = (img - window_level + 0.5 * window_width) / window_width
            img[img < 0] = 0
            img[img > 1] = 1
        scaled_image = (np.maximum(img, 0) / (1 if img.max() == 0 else img.max())) * 255.0
        scaled_image = np.uint8(scaled_image)
        final_image = Image.fromarray(scaled_image)
        final_image.save(output_path)

    @staticmethod
    def jpg2dcm(dataset, jpg_file, series_instance_uid, series_number, series_description, sop_instance_uid, instance_number,
                output_path, modality=Consts.DEFAULT_ALGORITHM_RESULT_MODALITY):
        ds = copy.deepcopy(dataset)
        img = np.asarray(Image.open(jpg_file))
        ds.Rows, ds.Columns, ds.SamplesPerPixel = img.shape
        ds.PixelData = img.tostring()
        ds.SeriesInstanceUID = series_instance_uid
        ds.SeriesNumber = series_number
        ds.SeriesDescription = series_description
        ds.SOPInstanceUID = sop_instance_uid
        ds.InstanceNumber = instance_number
        now = datetime.datetime.now()
        create_date = now.strftime("%Y%m%d")
        create_time = now.strftime("%H%M%S")
        ds.SeriesDate = create_date
        ds.SeriesTime = create_time
        ds.ContentDate = create_date
        ds.ContentTime = create_time
        ds.Modality = modality
        ds.Manufacturer = "UnionStrong"
        ds.ManufacturerModelName = "UGuard"
        ds.StationName = "USC-UGuard"
        ds.PixelRepresentation = 0
        ds.BitsAllocated = 8
        ds.HighBit = 7
        ds.BitsStored = 8
        ds.PlanarConfiguration = 0
        ds.PhotometricInterpretation = "RGB"
        ds.is_implicit_VR = False
        ds.LossyImageCompression = "01"
        ds.LossyImageCompressionRatio = 10  # default jpeg
        ds.LossyImageCompressionMethod = "ISO_10918_1"
        ds.file_meta.TransferSyntaxUID = uid.ExplicitVRLittleEndian
        ds.save_as(output_path)

    @staticmethod
    def png2dcm(template_dataset, png_path, series_instance_uid, series_number, series_description, modality,
                instance_number, output_path):
        sop_instance_uid = DicomUtils.generate_child_uid(series_instance_uid)
        ds = Dataset()
        ds.preamble = b"\0" * 128
        ds.file_meta = FileMetaDataset()
        ds.file_meta.TransferSyntaxUID = uid.ExplicitVRLittleEndian
        ds.file_meta.MediaStorageSOPClassUID = uid.SecondaryCaptureImageStorage
        ds.file_meta.MediaStorageSOPInstanceUID = sop_instance_uid
        image = Image.open(png_path)
        ds.Rows = image.height
        ds.Columns = image.width
        np_image = np.asarray(image.getdata(), dtype=np.uint8)[:, :3]
        ds.PixelData = np_image.tobytes()
        ds.SamplesPerPixel = 3
        ds.PixelRepresentation = 0
        ds.BitsAllocated = 8
        ds.HighBit = 7
        ds.BitsStored = 8
        ds.PlanarConfiguration = 0
        ds.PhotometricInterpretation = "RGB"
        ds.PatientID = template_dataset.get("PatientID")
        ds.PatientName = template_dataset.get("PatientName")
        if "PatientSex" in template_dataset:
            ds.PatientSex = template_dataset.get("PatientSex")
        if "PatientBirthDate" in template_dataset:
            ds.PatientBirthDate = template_dataset.get("PatientBirthDate")
        ds.StudyInstanceUID = template_dataset.get("StudyInstanceUID")
        ds.Modality = modality
        ds.StationName = "USC-UGuard"
        ds.Manufacturer = "UnionStrong"
        ds.ManufacturerModelName = "UGuard"
        if "StudyDate" in template_dataset:
            ds.StudyDate = template_dataset.get("StudyDate")
        if "StudyTime" in template_dataset:
            ds.StudyTime = template_dataset.get("StudyTime")
        if "StudyDescription" in template_dataset:
            ds.StudyDescription = template_dataset.get("StudyDescription")
        if "AccessionNumber" in template_dataset:
            ds.AccessionNumber = template_dataset.get("AccessionNumber")
        ds.SeriesInstanceUID = series_instance_uid
        ds.SeriesNumber = series_number
        ds.SeriesDescription = series_description
        now = datetime.datetime.now()
        create_date = now.strftime("%Y%m%d")
        create_time = now.strftime("%H%M%S")
        ds.SeriesDate = create_date
        ds.SeriesTime = create_time
        ds.SOPInstanceUID = sop_instance_uid
        ds.InstanceNumber = instance_number
        ds.SOPClassUID = uid.SecondaryCaptureImageStorage
        ds.ContentDate = create_date
        ds.ContentTime = create_time
        ds.save_as(output_path)


class OrthancApi:

    _BASE_URL = F"http://{Config.PACS_HOST}:{Config.PACS_DOCKER_PORT}"
    _AUTH = HTTPBasicAuth(Config.PACS_USERNAME, Config.PACS_PASSWORD)

    @staticmethod
    def upload_image(file_path):
        url = F"{OrthancApi._BASE_URL}/instances"
        log.info("Orthanc[request] > url:{}, file:{}".format(url, file_path))
        files = {"files": (FileUtils.get_filename_without_suffix(file_path), open(file_path, "rb"),
                           "application/octet-stream", {"Expires": "0"})}
        response = requests.post(url=url, files=files, auth=OrthancApi._AUTH)
        log.info("Orthanc[response] > code:{}".format(response.status_code))
        return response.status_code == 200

    @staticmethod
    def move(data):
        url = F"{OrthancApi._BASE_URL}/modalities/local/move"
        log.info("Orthanc[request] > url:{}, data:{}".format(url, data))
        response = requests.post(url=url, json=data, auth=OrthancApi._AUTH)
        log.info("Orthanc[response] > code:{}".format(response.status_code))
        return response.status_code == 200


class WebApi:
    """平台接口服务"""
    _BASE_URL = F"http://{Config.WEBAPI_HOST}:{Config.WEBAPI_PORT}/api/v1"

    @staticmethod
    def find_pacs_server():
        """
        PACS Server查询

        :return:
        """
        url = F"{WebApi._BASE_URL}/async/pacsserver/"
        try:
            log.info("WebApi[get] > url:{}".format(url))
            response = requests.get(url=url)
            response_body = response.json()
            log.info("WebApi[get] > response[{}]: {}".format(response.status_code, response_body))
            return WebApi.__get_data(response.json())
        except Exception:
            log.error("failed to call WebApi: {}".format(traceback.format_exc()))
            return []

    @staticmethod
    def get_config(code_list, category="", tag=""):
        """
        配置查询

        :param code_list: 配置编码列表
        :param category: 配置分类
        :param tag: 配置标签
        :return:
        """
        codes = ""
        if code_list and len(code_list) > 0:
            codes = "&codes=" + "&codes=".join(code_list)
        url = F"{WebApi._BASE_URL}/systemsettings?type={category}&tag={tag}{codes}"
        try:
            log.info("WebApi[get] > url:{}".format(url))
            response = requests.get(url=url)
            response_body = response.json()
            log.info("WebApi[get] > response[{}]: {}".format(response.status_code, response_body))
            return WebApi.__get_data(response_body)
        except Exception:
            log.error("failed to call WebApi: {}".format(traceback.format_exc()))
            return []

    @staticmethod
    def __get_data(response_body):
        if not response_body or "data" not in response_body:
            return dict()
        return response_body.get("data")


class ImageUtils:
    FONT_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/font/Arial.ttf")

    @staticmethod
    def generate_text(template_path, text_list, output):
        temp_img = Image.open(template_path)
        temp_width, temp_height = temp_img.size
        width = temp_width
        height = 350
        target = Image.new("RGB", (width, height))
        draw = ImageDraw.Draw(target)
        font_size = int((temp_width+temp_height) / 40)
        font = ImageFont.truetype(ImageUtils.FONT_PATH, font_size)
        desc_color = (160, 160, 164)
        value_color = (255, 255, 255)
        line_height = font_size * len(text_list)
        for i in range(len(text_list)):
            y = (height - line_height) / 2 + i * font_size
            item_dict = text_list[i]
            desc = item_dict["desc"]
            value = item_dict["value"]
            word_width, word_height = font.getsize(desc+value)
            desc_x = (width - word_width) / 2
            draw.text((desc_x, y), desc, font=font, fill=desc_color)
            desc_width, desc_height = font.getsize(desc)
            draw.text((desc_x + desc_width, y), value, font=font, fill=value_color)
        target.save(output, quality=100)

    @staticmethod
    def splice_vertical(files, output):
        image_file = []
        max_width = 0
        height = 0
        for image_path in files:
            img = Image.open(image_path)
            img_size = img.size
            max_width = img_size[0] if img_size[0] >= max_width else max_width
            height += img_size[1] + 1
            log.debug(img.size, img.format)
            image_file.append(img)
        height -= 1
        target = Image.new("RGB", (max_width, height))
        left = 0
        right = 0
        index = 0
        for img in image_file:
            index += 1
            right += img.size[1]
            target.paste(img, (0, left, img.size[0], right))
            if index < len(image_file):
                left += 1
                right += 1
                draw = ImageDraw.Draw(target)
                draw.line((0, right, max_width, right), fill="#A0A0A0", width=10)
            left += img.size[1]
        target.save(output, quality=100)
