#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : run
@Project : uguard_ctp
<AUTHOR> mingxing
@Date    : 2022/9/15 16:31
"""
import json
import os
import time

from lib.common import RabbitProducer, FileUtils, WebApi
from lib.const import Config, RetCode, Consts
from lib.ctp import <PERSON>tp<PERSON>unner
from lib.logger import MyLogger
from lib.service import PostProcessor

log = MyLogger()


def handle_ctp(channel, method, study_instance_uid, series, algorithm_type, **kwargs):
    """
    CTP处理

    :param channel:
    :param method:
    :param study_instance_uid:
    :param series:
    :param algorithm_type:
    :param kwargs:
    :return:
    """
    start_time = time.time()
    # 动脉点
    artery = kwargs.get("artery", [])
    # 静脉点
    vein = kwargs.get("vein", [])
    # 东芝灌注
    toshiba = kwargs.get("toshiba", False)
    # 是否回传
    callback = kwargs.get("callback", True)
    # GPU
    gpu_id = kwargs.get("gpu_id", 0)
    percent_message = dict(studyInstanceUID=study_instance_uid, algorithmType=algorithm_type, toshiba=toshiba)
    percent_message["seriesInstanceUIDs" if toshiba else "seriesInstanceUID"] = series
    # 检查图像
    _dicom_dir = os.path.join(Config.DIR_DCM, study_instance_uid if toshiba else series)
    if not os.path.exists(_dicom_dir):
        log.info("CTP > series dcm not found")
        percent_message.update({"percent": 500, "errorCode": RetCode.PLATFORM_SERIES_NOT_FOUND.code})
        RabbitProducer.send(percent_message)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return False
    RabbitProducer.send(dict(percent=10, **percent_message))
    _output_dir = os.path.join(Config.DIR_CTP, study_instance_uid, ("" if toshiba else series))
    FileUtils.rebuild_dirs(_output_dir)
    _manual_inputs = {}
    if artery and isinstance(artery, list) and len(artery) == 3:
        _manual_inputs["aif"] = artery
    if vein and isinstance(vein, list) and len(vein) == 3:
        _manual_inputs["vof"] = vein
    config_data = WebApi.get_config([Consts.CODE_ALGORITHM_RESULT_MODALITY])
    result_modality = config_data[0]["value"] if config_data else Consts.DEFAULT_ALGORITHM_RESULT_MODALITY
    ctp_runner = CtpRunner(RabbitProducer.send, percent_message, _dicom_dir, _output_dir, _manual_inputs, toshiba,
                           gpu_id, result_modality)
    result = ctp_runner.get_result()
    log.info("CTP[study:{}, series:{}] > result: {}".format(study_instance_uid, series, result))
    if not result:
        log.error("CTP[study:{}, series:{}] > failed to get ctp result".format(study_instance_uid, series))
        percent_message.update({"percent": 500, "errorCode": RetCode.CTP_ERROR.code})
        RabbitProducer.send(percent_message)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return False
    code = result.get("code", RetCode.CTP_ERROR.code)
    # CTP计算失败
    if code != RetCode.CTP_OK.code:
        log.error("CTP[study:{}, series:{}] > error: {}".format(study_instance_uid, series, code))
        percent_message.update({"percent": 500, "errorCode": code})
        # 自动选点失败，生成空MIP序列
        if code == RetCode.CTP_DETECTED_AVP_FAILED.code:
            report_dir = result.get("report")
            uploader = PostProcessor(study_instance_uid, report_dir, need_parse_dcm=False, need_read_config=False)
            flag = uploader.upload()
            percent_message["reportPath"] = report_dir
            log.info("CTP[study:{}, series:{}] > send mip series to orthanc: {}".format(
                study_instance_uid, series, "success" if flag else "fail"))
        RabbitProducer.send(percent_message)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return False
    # 发送算法处理时间
    run_time = result.get("runRime")
    time_message = dict(studyInstanceUID=study_instance_uid, algorithmType=algorithm_type, toshiba=toshiba,
                        consumerTime=run_time, percent=95)
    time_message["seriesInstanceUIDs" if toshiba else "seriesInstanceUID"] = series
    time_message["featureMap"] = result.get("featureMap")
    RabbitProducer.send(time_message)
    # 同步报告
    report_dir = result.get("report")
    uploader = PostProcessor(study_instance_uid, report_dir)
    if not toshiba:
        uploader.merge_summary(FileUtils.get_one(_dicom_dir), result_modality)
    RabbitProducer.send(dict(percent=97, reportPath=report_dir, **percent_message))
    # 回传pacs
    RabbitProducer.send(dict(percent=98, BackReport=True, **percent_message))
    # ctp算法成功
    results = result.get("results")
    RabbitProducer.send(dict(percent=100, result=json.dumps(results), **percent_message))
    channel.basic_ack(delivery_tag=method.delivery_tag)
    end_time = time.time()
    log.info("CTP[study:{}, series:{}] > CTP: {:.2f}s, Platform: {:.2f}s, Total: {:.2f}s".format(
        study_instance_uid, series, run_time, (end_time - start_time - run_time), (end_time - start_time)))
    log.info("********************** success **********************")
    return True
