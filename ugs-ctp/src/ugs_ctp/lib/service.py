#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : uguard_ctp
<AUTHOR> mingxing
@Date    : 2022/9/16 11:01
"""
import os
import time
import traceback
from concurrent.futures import ProcessPoolExecutor, as_completed

from lib.common import FileUtils, OrthancApi, DicomUtils, WebApi, ImageUtils, StringUtils
from lib.const import Config, Consts
from lib.logger import MyLogger

log = MyLogger()


class PostProcessor:
    def __init__(self, study_instance_uid, report_dir, need_parse_dcm=True, need_read_config=True):
        self.study_instance_uid = study_instance_uid
        self.report_dir = report_dir
        self.series_uid_desc = {}
        self.file_list = []
        file_count = 0
        for name in os.listdir(report_dir):
            series_dir = os.path.join(report_dir, name)
            file_list = FileUtils.get_all_file(series_dir)
            file_size = len(file_list)
            if file_size == 0:
                continue
            file_count = file_count + file_size
            self.file_list.extend(file_list)
            if need_parse_dcm:
                dataset = DicomUtils.read_dcm(file_list[0])
                series_instance_uid = dataset.get("SeriesInstanceUID", "")
                series_description = dataset.get("SeriesDescription", "")
                if series_instance_uid not in self.series_uid_desc:
                    self.series_uid_desc[series_instance_uid] = series_description
        log.info("find {} files in {}".format(file_count, report_dir))
        self.conf = self.__get_config() if need_read_config else {}

    def merge_summary(self, template_dcm, modality):
        can_merge = self.conf.get(Consts.CODE_REPORT_CTP_MERGE, 2) == 1
        log.info("summary[study: {}] > merge: {}".format(self.study_instance_uid, can_merge))
        if can_merge:
            jpg_list = self.__generate_total_summary()
            log.info("total summary: {}".format(jpg_list))
            self.__convert_total_summary(template_dcm, jpg_list, modality)

    def __generate_total_summary(self):
        dir_study = os.path.join(Config.DIR_CTP, self.study_instance_uid)
        series_list = os.listdir(dir_study)
        series_number = len(series_list)
        log.info("series number: {}".format(series_number))
        if series_number != 2:
            log.info("ignore summary merge")
            return []
        summary_list = []
        total_tmax = 0
        total_rcbf = 0
        for series_instance_uid in series_list:
            summary_dir = os.path.join(dir_study, F"{series_instance_uid}/png/USC_UGuard_CTP_Summary")
            result_path = os.path.join(dir_study, F"{series_instance_uid}/text.json")
            if not os.path.exists(summary_dir) or not os.path.exists(result_path):
                log.info("summary not found: {}".format(summary_dir))
                return []
            summary_list.append(FileUtils.get_all_file(summary_dir, ".png"))
            list_size = len(summary_list)
            if list_size > 0 and len(summary_list[list_size - 1]) != len(summary_list[list_size - 2]):
                log.info("Inconsistent number of summary: {}".format(summary_list))
                return []
            text_dict = FileUtils.read_json(result_path)
            tmax = round(text_dict.get("v_TMax", "")[2], 2) if text_dict.get("v_TMax", 0) else 0
            rcbf = round(text_dict.get("v_cbf", "")[0], 2) if text_dict.get("v_cbf", "") else 0
            total_rcbf = total_rcbf + rcbf
            total_tmax = total_tmax + tmax
        total_rcbf = StringUtils.delete_extra_zero(total_rcbf)
        total_tmax = StringUtils.delete_extra_zero(total_tmax)
        total_mismatch = round(total_tmax - total_rcbf, 2)
        total_mismatch = StringUtils.delete_extra_zero(total_mismatch)
        total_mismatch_ratio = "Inf"
        if total_tmax and total_rcbf:
            try:
                total_mismatch_ratio = StringUtils.delete_extra_zero(round(total_tmax / total_rcbf, 2))
            except Exception:
                log.error(traceback.format_exc())
        msg_list = [{"desc": "rCBF<30% volume: ", "value": "{} ml".format(total_rcbf)},
                    {"desc": "TMax>6.0s volume: ", "value": "{} ml".format(total_tmax)},
                    {"desc": "Mismatch volume: ", "value": "{} ml".format(total_mismatch)},
                    {"desc": "Mismatch ratio: ", "value": str(total_mismatch_ratio)}]
        merge_dir = os.path.abspath(os.path.join(self.report_dir, "../summary_merge"))
        FileUtils.rebuild_dirs(merge_dir)
        text_merge_image = os.path.join(merge_dir, "text_0.png")
        ImageUtils.generate_text(summary_list[0][0], msg_list, text_merge_image)
        total_summary_dir = os.path.join(self.report_dir, "../png/USC_UGuard_CTP_Total_Summary")
        FileUtils.rebuild_dirs(total_summary_dir)
        total_summary_list = []
        for index in range(len(summary_list[0])):
            image_list = []
            for series_num in range(len(summary_list)):
                image_list.append(summary_list[series_num][index])
            if index == 0:
                image_list.append(text_merge_image)
            path = os.path.join(total_summary_dir, "img_{}.png".format(index))
            ImageUtils.splice_vertical(image_list, path)
            total_summary_list.append(path)
        return total_summary_list

    def __convert_total_summary(self, template_dcm, file_list, modality):
        if not file_list or len(file_list) == 0:
            return
        dataset = DicomUtils.read_dcm(template_dcm)
        series_instance_uid = DicomUtils.generate_uid()
        series_description = "USC-UGuard CTP Total Summary"
        output_dir = os.path.join(self.report_dir, "USC_UGuard_CTP_Total_Summary")
        FileUtils.rebuild_dirs(output_dir)
        total_list = []
        series_number = 100+len(os.listdir(self.report_dir))
        for index in range(len(file_list)):
            sop_instance_uid = DicomUtils.generate_child_uid(series_instance_uid)
            instance_number = index + 1
            output_path = os.path.join(output_dir, "{}.dcm".format(instance_number))
            DicomUtils.jpg2dcm(dataset, file_list[index], series_instance_uid, series_number, series_description,
                               sop_instance_uid, instance_number, output_path, modality)
            total_list.append(output_path)
        log.info("total summary[study: {}]: {}".format(self.study_instance_uid, output_dir))
        self.file_list.extend(total_list)
        self.series_uid_desc[series_instance_uid] = series_description

    def upload(self):
        start_time = time.time()
        file_count = len(self.file_list)
        log.info("upload {} files".format(file_count))
        with ProcessPoolExecutor(max_workers=int(os.cpu_count() / 2)) as executor:
            task_list = []
            for dcm_file in self.file_list:
                task_list.append(executor.submit(OrthancApi.upload_image, dcm_file))
            operator_flag = True
            for task in as_completed(task_list):
                if not task.result():
                    operator_flag = False
                    log.warning("task: {}".format(task.result()))
            task_list.clear()
            log.info("upload {} files in {:.5}s".format(file_count, (time.time() - start_time)))
            return operator_flag

    # def postback(self):
    #     pacs_host = self.conf.get("pacsHost", "")
    #     pacs_port = self.conf.get("pacsPort", "")
    #     pacs_aet = self.conf.get("pacsAet", "")
    #     method = self.conf[Consts.CODE_REPORT_BACK_METHOD]
    #     can_send_summary = self.conf[Consts.CODE_REPORT_CTP_SUMMARY]
    #     can_send_colormap = self.conf[Consts.CODE_REPORT_CTP_COLORMAP]
    #     can_send_mip = self.conf[Consts.CODE_REPORT_CTP_MIP]
    #     log.info("CTP > study[{}], pacs[{}:{}({})], method[{}], summary[{}], colormap[{}], mip[{}]".format(
    #         self.study_instance_uid, pacs_host, pacs_port, pacs_aet, method, can_send_summary, can_send_colormap,
    #         can_send_mip))
    #
    #     if not pacs_host or not pacs_port or not pacs_aet:
    #         log.info("cancel report back, target pacs not found")
    #         return
    #     if not can_send_summary and not can_send_colormap and not can_send_mip:
    #         log.info("nothing can be returned")
    #         return
    #     if method == Consts.BACK_METHOD_DEFAULT:
    #         Thread(target=self.__c_move,
    #                args=(self.study_instance_uid, self.series_uid_desc, can_send_summary,
    #                      can_send_colormap, can_send_mip, pacs_aet)).start()
    #         return
    #     Thread(target=self.__c_store,
    #            args=(self.report_dir, pacs_host, pacs_port, pacs_aet, can_send_summary,
    #                  can_send_colormap, can_send_mip)).start()

    # @staticmethod
    # def __c_move(study_instance_uid, series_uid_desc, can_send_summary, can_send_colormap, can_send_mip, pacs_aet):
    #     resources = []
    #     log.info("result series: {}".format(series_uid_desc))
    #     for series_instance_uid in series_uid_desc:
    #         series_desc = series_uid_desc.get(series_instance_uid)
    #         if (series_desc in Consts.RESULT_CATEGORY.get("summary") and not can_send_summary) or (
    #                 series_desc in Consts.RESULT_CATEGORY.get("colorMap") and not can_send_colormap) or (
    #                 series_desc in Consts.RESULT_CATEGORY.get("mip") and not can_send_mip):
    #             continue
    #         resources.append({"StudyInstanceUID": study_instance_uid, "SeriesInstanceUID": series_instance_uid})
    #     if len(resources) == 0:
    #         log.info("nothing to send, summary:{}, colorMap:{}, mip:{}".format(
    #             can_send_summary, can_send_colormap, can_send_mip))
    #         return
    #     data = {"Level": "SERIES", "Resources": resources, "TargetAet": pacs_aet, "Timeout": 60}
    #     OrthancApi.move(data)
    #
    # @staticmethod
    # def __c_store(report_dir, pacs_host, pacs_port, pacs_aet, can_send_summary, can_send_colormap, can_send_mip):
    #     files = []
    #     for dir_name in sorted(os.listdir(report_dir)):
    #         if (dir_name in Consts.DIR_CATEGORY.get("summary") and not can_send_summary) or (
    #                 dir_name in Consts.DIR_CATEGORY.get("colorMap") and not can_send_colormap) or (
    #                 dir_name in Consts.DIR_CATEGORY.get("mip") and not can_send_mip):
    #             continue
    #         files.extend(FileUtils.get_all_file(os.path.join(report_dir, dir_name)))
    #     file_size = len(files)
    #     log.info("cstore {} files".format(file_size))
    #     if file_size <= 0:
    #         return
    #     DicomUtils.send_cstore(files, pacs_host, pacs_port, pacs_aet)

    @staticmethod
    def __get_config():
        back_conf = dict()
        # back_conf[Consts.CODE_REPORT_BACK_METHOD] = Consts.BACK_METHOD_DEFAULT
        back_conf[Consts.CODE_REPORT_CTP_MERGE] = 2
        # back_conf[Consts.CODE_REPORT_CTP_SUMMARY] = True
        # back_conf[Consts.CODE_REPORT_CTP_COLORMAP] = True
        # back_conf[Consts.CODE_REPORT_CTP_MIP] = True

        # config_codes = [Consts.CODE_REPORT_BACK_METHOD, Consts.CODE_REPORT_CTP_MERGE, Consts.CODE_REPORT_CTP_SUMMARY,
        #                 Consts.CODE_REPORT_CTP_COLORMAP, Consts.CODE_REPORT_CTP_MIP]
        config_list = WebApi.get_config([Consts.CODE_REPORT_CTP_MERGE])
        if len(config_list) > 0:
            for conf in config_list:
                back_conf[conf.get("code")] = PostProcessor.__parse_config_value(conf)

        # pacs server
        # pacs_list = WebApi.find_pacs_server()
        # if len(pacs_list) > 0:
        #     target_pacs = next((pacs for pacs in pacs_list if pacs.get("alie_name", "") == Consts.PACS_TARGET), None)
        #     if not target_pacs:
        #         return back_conf
        #     back_conf["pacsHost"] = target_pacs.get("ip_address")
        #     back_conf["pacsPort"] = int(target_pacs.get("port"))
        #     back_conf["pacsAet"] = target_pacs.get("aet")
        return back_conf

    @staticmethod
    def __parse_config_value(conf_dict):
        _format = conf_dict.get("format")
        _value = conf_dict.get("value")
        if _format == "text":
            return str(_value)
        if _format == "bool":
            return StringUtils.to_bool(_value)
        if _format == "int":
            return int(_value)
        if _format == "float":
            return float(_value)
        return _value
