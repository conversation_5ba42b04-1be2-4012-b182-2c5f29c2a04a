#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : ctp
@Project : uguard_ctp
<AUTHOR> mingxing
@Date    : 2022/9/15 16:58
"""
import datetime
import os
import glob
import shutil
import sys
import re
import time
import traceback
from multiprocessing import Manager, Process
import subprocess
import tempfile

import SimpleITK as sitk
import pandas as pd
import numpy as np
import usia.pyctp as pyctp
import usia.pymipf as pymipf
import usia.pyitk as pyitk
from usal_apps.cta_head_vessels_seg.app import CTAHeadVesselSegApp
import imgkit
from ctp_vessel_segmentation import *


from lib.common import DicomUtils, FileUtils, StringUtils, WebApi
from lib.const import RetCode, Config, Consts
from lib.logger import MyLogger, CtpLogger
from lib.ctp_utils import boing

log = MyLogger()


class CtpRunner:

    def __init__(self, producer, message_template, dcm_dir, output_dir, manual_inputs, is_toshiba, gpu_id=0,
                 modality=Consts.DEFAULT_ALGORITHM_RESULT_MODALITY):
        self.producer = producer
        self.message_template = message_template
        self.dcm_dir = dcm_dir
        self.output_dir = output_dir
        self.output_report = os.path.join(output_dir, "report")
        self.manual_inputs = manual_inputs
        self.is_toshiba = is_toshiba
        self.gpu_id = gpu_id
        self.modality = modality

    def get_result(self):
        mgr = Manager()
        mgr_jobs = mgr.dict()
        p = Process(target=self.__start, args=(mgr_jobs,))
        p.start()
        p.join()
        return mgr_jobs

    def __start(self, mgr_dict):
        """
        算法计算

        :param mgr_dict:
        :return:
        """
        # 初始化mipf环境
        pymipf.InitializeSession("PyCTP")
        # 获得环境上下文
        session = pymipf.GetSession()
        # 创建回调函数
        callback = MyCallback(self.producer, self.message_template)
        # 设置回调函数
        session.SetProgressCallBackFunction(callback)
        # 设置算法日志
        log_path = CtpLogger().get_logger()
        session.SetLogFile("ctp", log_path)
        session.SetLogLevel("info")
        session.SetLogPattern("[%D %T][%n][%l][%P - %t] %v")
        # 更新配置
        log.info("CTP > config: {}".format(session.GetConfigPath()))
        CtpUtils.load_config(session)
        # GPU
        # os.environ["TF_XLA_FLAGS"] = "--tf_xla_enable_xla_devices"
        # physical_devices = tf.config.list_physical_devices("GPU")
        # tf.config.experimental.set_memory_growth(physical_devices[0], True)
        log.info("CTP > input:{}, output:{}".format(self.dcm_dir, self.output_dir))
        ctp_tools, ret_code, run_time = CtpUtils.process_ctp_series(
            self.dcm_dir, session, auto=False, manual_inputs=self.manual_inputs, result_dir=self.output_dir,
            gpu_id=self.gpu_id)
        log.info("CTP > process ctp series, retCode:{}, runTime:{}".format(ret_code, run_time))
        ctp_handler = CtpHandler(ctp_tools, self.dcm_dir)
        if ret_code != RetCode.CTP_OK:
            log.error("CTP > failed to process ctp series")
            mgr_dict["code"] = ret_code.code
            if ret_code == RetCode.CTP_DETECTED_AVP_FAILED:
                ctp_handler.generate_mip(self.output_report, self.modality)
                mgr_dict["report"] = self.output_report
            return
        # 获得计算结果
        ctp_results = ctp_tools.GetResults()
        if not ctp_results:
            log.error("CTP > result is empty")
            mgr_dict["code"] = RetCode.CTP_ERROR.code
            return
        mgr_dict["runRime"] = run_time
        # 输出图片报告
        png_report_dir = os.path.join(self.output_dir, "png")
        ctp_tools.ExportReports2Png(png_report_dir)
        # 生成DICOM报告
        ctp_handler.generate_dcm_report(png_report_dir, self.output_report, self.modality)
        # ctp_tools.ExportReports2Dicom(self.output_report)
        # if self.modality != Consts.DEFAULT_ALGORITHM_RESULT_MODALITY:
        #     ctp_handler.update_report(self.output_report, self.modality)
        mgr_dict["report"] = self.output_report

        # 生成MIP
        ctp_handler.generate_mip(self.output_report, self.modality)
        desc_uid_map = ctp_handler.get_series_info(self.output_report)
        # 生成参数图
        feature_maps = ctp_handler.generate_grayscale(self.is_toshiba, desc_uid_map)
        mgr_dict["featureMap"] = feature_maps
        log.info("CTP > feature maps:{}".format(feature_maps))
        # 生成TDC图像
        ctp_tools.SaveTDCImage(os.path.join(self.output_dir, "tdc.nii.gz"))
        time_interval = ctp_tools.GetTimeInterval()
        tdc_info = dict(timeInterval=str(time_interval))
        FileUtils.save_json(tdc_info, os.path.join(self.output_dir, "tdc.json"))
        log.info("CTP > TDC timeInterval:{}".format(time_interval))
        ctp_handler.backup_result(self.output_dir, session, self.dcm_dir, run_time, ctp_results)
        # 获取文本结果
        log.info("CTP > parsing text results")
        text_results = ctp_handler.parse_ctp_results(ctp_results)
        # log.info("CTP > AffectedSide:{}(0-Left;1-Right)".format(ctp_tools.GetAffectedSide()))
        FileUtils.save_json(text_results, os.path.join(self.output_dir, "text.json"))
        mgr_dict["results"] = text_results
        mgr_dict["code"] = RetCode.CTP_OK.code
        ctp_tools.Finalize()


class MyCallback(pymipf.Callback):
    """
    ctp 进度获取回调接口，把进度通过pika 传到结果消费端
    """

    def __init__(self, produce, content):
        super(MyCallback, self).__init__()
        self.produce = produce
        self.content = content
        self.tem = 0

    def Progress(self, progress, totalProgress, message):
        con = self.content
        try:
            if progress <= totalProgress:
                percent = int((progress / totalProgress) * 85)
            else:
                percent = 85
        except ZeroDivisionError:
            percent = 0
        if percent > self.tem:
            self.tem = percent
            con.update(percent=(10 + percent))
            self.produce(con)


class CtpUtils:

    @staticmethod
    def load_config(session):
        config_dir = session.GetConfigPath()
        conf_list = WebApi.get_config([], category="ctp", tag="init")
        if not conf_list:
            log.warning("ctp config not found")
            return
        conf_size = len(conf_list)
        log.info("find {} configurations".format(conf_size))
        for conf in conf_list:
            _code = conf.get("code", "")
            _value = conf.get("value", "")
            _format = conf.get("format", "")
            if len(_code) == 0 or len(_value) == 0 or len(_format) == 0:
                log.info("invalid config: {}".format(conf))
                continue
            if _code == "ctpFalseColor":
                lut_time = ""
                lut_value = ""
                if _value == "siemens":
                    lut_time = os.path.join(config_dir, "siemens-time.lut")
                    lut_value = os.path.join(config_dir, "siemens-value.lut")
                session.SetStringConfig("MTT-Color-LookupTable-File", lut_time)
                session.SetStringConfig("Tmax-Color-LookupTable-File", lut_time)
                session.SetStringConfig("TTP-Color-LookupTable-File", lut_time)
                session.SetStringConfig("CBF-Color-LookupTable-File", lut_value)
                session.SetStringConfig("CBV-Color-LookupTable-File", lut_value)
                session.SetStringConfig("PS-Color-LookupTable-File", lut_value)
                continue
            if _code == "Threshold-Segmentation-Range":
                value_list = _value.split(",")
                CtpUtils.__set_config(session, "Csf-Threshold", value_list[0], _format)
                CtpUtils.__set_config(session, "Vessel-Threshold", value_list[1], _format)
                continue
            CtpUtils.__set_config(session, _code, _value, _format)
        log.info("{} configurations imported successfully".format(conf_size))

    @staticmethod
    def __set_config(session, _key, _value, _format):
        if _format == "text":
            session.SetStringConfig(_key, str(_value))
            return
        if _format == "bool":
            session.SetBoolConfig(_key, StringUtils.to_bool(_value))
            return
        if _format == "int":
            session.SetIntConfig(_key, int(_value))
            return
        if _format == "float":
            session.SetDoubleConfig(_key, float(_value))
            return
        log.info("invalid config format, key:{}, value:{}, format:{}".format(_key, _value, _format))

    @staticmethod
    def process_ctp_series(ctp_dir, session, auto=True, ventricle_model=True, partition_model=True,
                           manual_inputs=None, result_dir=None, ctp_vessel_model=True, gpu_id=0):
        ctp_tools = pyctp.CTPTools()
        start = time.perf_counter()
        try:
            if auto:
                ctp_tools.ProcessCTPImages(ctp_dir)
            else:
                if not ctp_tools.Initialize(pymipf.GetSession()):
                    log.error("pyctp[CTPTools] > Initialize environment failed")
                    return ctp_tools, RetCode.CTP_INITIALIZE_FAILED, CtpUtils.get_processed_time(start)

                if result_dir:
                    ctp_tools.SetInternelImageSaveDir(result_dir)
                    # True保存调试结果
                    ctp_tools.SetSaveInternelImage(True)

                if not ctp_tools.ParseCTPImages(ctp_dir):
                    log.error("pyctp[CTPTools] > Parse ctp series failed")
                    return ctp_tools, RetCode.CTP_PARSE_FAILED, CtpUtils.get_processed_time(start)

                if not ctp_tools.LoadCTPImages():
                    log.error("pyctp[CTPTools] > Load ctp series failed")
                    return ctp_tools, RetCode.CTP_LOAD_FAILED, CtpUtils.get_processed_time(start)

                if not ctp_tools.RegisterCTPImages():
                    pass

                if not ctp_tools.ExtractCTPBrain():
                    log.error("pyctp[CTPTools] > Extract brain failed")
                    return ctp_tools, RetCode.CTP_EXTRACT_BRAIN_FAILED, CtpUtils.get_processed_time(start)
                
                CtpUtils.save_4D_CTP_result(session, ctp_tools, result_dir)

                if not ctp_tools.PrepareCTPImages():
                    log.error("pyctp[CTPTools] > Preprocess failed")
                    return ctp_tools, RetCode.CTP_PREPROCESS_FAILED, CtpUtils.get_processed_time(start)

                symmetry_extracted = False
                if ventricle_model:
                    log.info("Partition brain using model!")
                    pyItkImage = ctp_tools.GetImage(0)
                    sitkImage = CtpUtils.itk_to_sitk_image(pyItkImage)
                    partition_label = CtpUtils.partition_brain(sitkImage)
                    ctp_tools.SetMaskImage(CtpUtils.sitk_to_itk_image(partition_label, np.uint8), "brain-partition")

                    # prepare_dir(result_dir,False)
                    # sitk.WriteImage(partition_label, os.path.join(result_dir, "brain_seg.nii.gz"))
                    if session.GetIntConfig("Symmetry-Method", 2) == 2:
                        log.info("Extract Symmetry Using SVM")
                        center, normal, success = CtpUtils.extract_brain_symmetry_plane_with_svm(partition_label)
                        if success:
                            log.info("partition symmetry:{}, {}".format(center, normal))
                            symmetry_extracted = ctp_tools.UpdateSymmetryPlane(center, normal)
                        else:
                            log.info("Extract Symmetry Using SVM Failed!")

                    array_label = sitk.GetArrayFromImage(partition_label)
                    array_label[array_label < 35] = 0
                    array_label[array_label > 34] = 1
                    venticle_label = sitk.GetImageFromArray(array_label)
                    venticle_label.SetOrigin(partition_label.GetOrigin())
                    venticle_label.SetSpacing(partition_label.GetSpacing())
                    venticle_label.SetDirection(partition_label.GetDirection())
                    ctp_tools.SetMaskImage(CtpUtils.sitk_to_itk_image(venticle_label, np.uint8), "csf-origin")

                    # print("csf stats:", pyctp.GetImageStatistics(ctp_tools.GetInternelImage("base_smoothed"),
                    #                                              sitk_to_itk_image(venticle_label, np.uint16),1),
                    #       result_dir)

                    venticle_label = sitk.BinaryMorphologicalClosing(venticle_label, [4, 2, 1])
                    venticle_label = sitk.BinaryDilate(venticle_label, [1, 1, 1])
                    ctp_tools.SetMaskImage(CtpUtils.sitk_to_itk_image(venticle_label, np.uint8), "csf")
                    log.info("Extract csf using threshold!")
                    ctp_tools.ExtractCSF()
                else:
                    log.info("Extract csf using threshold!")
                    ctp_tools.ExtractCSF()

                if not ((session.GetIntConfig("Symmetry-Method", 2) == 2) and ventricle_model and symmetry_extracted):
                    log.info("Extract Symmetry Using Registration!")
                    if not ctp_tools.ExtractSymmetryPlane():
                        return ctp_tools, RetCode.SYMMETRY_FAILED, CtpUtils.get_processed_time(start)

                if ctp_tools.ComputeImageStatistics():
                    statics = ctp_tools.GetImageStatistics()
                    log.info("pyctp[CTPTools] > Image Statistics: {}".format(statics))

                if not ctp_tools.GammaFit():
                    log.error("pyctp[CTPTools] > Gamma fit failed")
                    return ctp_tools, RetCode.CTP_GAMMA_FIT_FAILED, CtpUtils.get_processed_time(start)

                if session.GetBoolConfig("Remove-ICH", True):
                    log.info("Extract ICH!")
                    pyItkImage = ctp_tools.GetImage(0)
                    sitkImage = CtpUtils.itk_to_sitk_image(pyItkImage)
                    ich_label = CtpUtils.extract_ich(sitkImage)
                    if ich_label:
                        ctp_tools.SetMaskImage(CtpUtils.sitk_to_itk_image(ich_label, np.uint8), "ich")
                    else:
                        empty_img = CtpUtils.itk_to_sitk_image(ctp_tools.GetMaskImage("mask"))
                        empty_img = sitk.Multiply(empty_img, 0)
                        ctp_tools.SetMaskImage(CtpUtils.sitk_to_itk_image(empty_img, np.uint8), "ich")

                if ctp_vessel_model:
                    log.info("Extract ctp vessel using model!")
                    pyItkImage = ctp_tools.GetInternelImage("mip")
                    sitkImage = CtpUtils.itk_to_sitk_image(pyItkImage)
                    vessel_label = mip_vessel_seg(sitkImage)
                    itk_image = pyitk.UChar3DImage()
                    itk_image.FromPyArray(sitk.GetArrayFromImage(vessel_label).astype(np.int8),
                                          pyItkImage.GetOrigin(),
                                          pyItkImage.GetSpacing(),
                                          pyItkImage.GetDirection())
                    ctp_tools.SetMaskImage(itk_image, "vessel")

                if not manual_inputs:
                    if not ctp_tools.DetectAifVofPoints():
                        log.error("pyctp[CTPTools] > Detect aif and vof point failed")
                        return ctp_tools, RetCode.CTP_DETECTED_AVP_FAILED, CtpUtils.get_processed_time(start)
                else:
                    log.info("pyctp[CTPTools] > Using manual inputs: {}".format(manual_inputs))
                    if "aif" in manual_inputs.keys():
                        ctp_tools.SetAIFIndex(manual_inputs["aif"])
                    if "vof" in manual_inputs.keys():
                        ctp_tools.SetVOFIndex(manual_inputs["vof"])
                    if "aif_data" in manual_inputs.keys():
                        ctp_tools.SetAIFData(manual_inputs["aif_data"])
                    if "vof_data" in manual_inputs.keys():
                        ctp_tools.SetVOFData(manual_inputs["vof_data"])

                if not ctp_tools.GenerateFeatureMaps():
                    log.error("pyctp[CTPTools] > Generate feature map failed")
                    return ctp_tools, RetCode.CTP_GENERATE_FEATURE_MAP_FAILED, CtpUtils.get_processed_time(start)

                if not ctp_tools.FeatureCompare():
                    log.error("pyctp[CTPTools] > Generate feature map failed")
                    return ctp_tools, RetCode.CTP_FEATURE_COMPARE_FAILED, CtpUtils.get_processed_time(start)

                if not ctp_tools.GenerateResults():
                    log.error("pyctp[CTPTools] > Generate results failed")
                    return ctp_tools, RetCode.CTP_GENERATE_RESULTS_FAILED, CtpUtils.get_processed_time(start)

                log.info("pyctp[CTPTools] > Multi Threshold Summary: {}".format(ctp_tools.GetMultiThresholdSummary()))

                if partition_model:
                    log.info("pyctp[CTPTools] > Partition brain using model!")
                    mean_res = ctp_tools.GetBrainPartition()
                    if bool(mean_res):
                        with tempfile.TemporaryDirectory() as temp_dir:
                            table_png_path = CtpUtils.get_brain_partition_table(
                                mean_res, session.GetConfigPath(), temp_dir)
                            ctp_tools.SetBrainPartitionTable(table_png_path)
                            log.info("pyctp[CTPTools] > Partition brain finish!")
                    else:
                        log.info("pyctp[CTPTools] > Partition brain Failed!")

                ctp_tools.ExtractResults()
                ctp_tools.PaintReport()

            return ctp_tools, RetCode.CTP_OK, CtpUtils.get_processed_time(start)
        except Exception:
            log.error(traceback.format_exc())
            return ctp_tools, RetCode.CTP_ERROR, CtpUtils.get_processed_time(start)

    @staticmethod
    def get_processed_time(start):
        return time.perf_counter() - start

    @staticmethod
    def prepare_dir(dir, overwrite=True):
        if os.path.exists(dir):
            if overwrite:
                try:
                    shutil.rmtree(dir)
                except OSError as Argument:
                    print(Argument)
                os.makedirs(dir)
        else:
            os.makedirs(dir)

    @staticmethod
    def itk_to_sitk_image(itk_image):
        sitk_image = sitk.GetImageFromArray(itk_image.ToPyArray())
        sitk_image.SetSpacing(itk_image.GetSpacing())
        sitk_image.SetOrigin(itk_image.GetOrigin())
        sitk_image.SetDirection(itk_image.GetDirection())
        return sitk_image

    @staticmethod
    def sitk_to_itk_image(sitk_image, dtype=np.float32, contiguous="F"):
        itk_image = None
        if dtype == np.float32:
            itk_image = pyitk.Float3DImage()
        elif dtype == np.uint8:
            itk_image = pyitk.UChar3DImage()
        elif dtype == np.int8:
            itk_image = pyitk.Char3DImage()
        elif dtype == np.int16:
            itk_image = pyitk.Short3DImage()
        elif dtype == np.uint16:
            itk_image = pyitk.UShort3DImage()
        elif dtype == np.int32:
            itk_image = pyitk.Int3DImage()
        elif dtype == np.uint32:
            itk_image = pyitk.UInt3DImage()
        else:
            raise TypeError("Invalid image pixel type {}".format(dtype))

        itk_image.FromPyArray(sitk.GetArrayFromImage(sitk_image).astype(dtype),
                              sitk_image.GetOrigin(),
                              sitk_image.GetSpacing(),
                              sitk_image.GetDirection(),
                              contiguous="F")
        return itk_image

    @staticmethod
    def get_result_dir_name(in_dir, out_dir, parents=1, skip=False):
        out_names = re.split("['\\\\/']+", in_dir)
        out_names = list(filter(lambda x: x != '', out_names))
        if parents > len(out_names):
            print("Wrong parents {} for input dir {}!".format(parents, in_dir))
            return None
        out_names.reverse()
        filter(None, out_names)
        out_name = []
        if skip:
            out_name.append(out_names[parents - 1])
        else:
            for i in range(parents):
                out_name.insert(0, out_names[i])
        result_dir = os.path.join(out_dir, os.sep.join(out_name))
        return result_dir

    @staticmethod
    def extract_brain_symmetry_plane_with_svm(brain_segment: sitk.Image, sample_rate=0.1, left_labels=[33, 34, 31],
                                              right_labels=[26, 27, 24]):
        start = time.perf_counter()

        from sklearn.svm import SVC
        from random import sample

        ppis = sitk.PhysicalPointImageSource()
        ppis.SetSpacing(brain_segment.GetSpacing())
        ppis.SetOrigin(brain_segment.GetOrigin())
        ppis.SetSize(brain_segment.GetSize())
        ppis.SetDirection(brain_segment.GetDirection())
        ppis_image = ppis.Execute()

        image_array = sitk.GetArrayFromImage(brain_segment)
        image_array = np.expand_dims(image_array, 3)
        ppis_array = sitk.GetArrayFromImage(ppis_image)

        left_points = []
        right_points = []
        for x, y in np.nditer([image_array, ppis_array], flags=["external_loop"]):
            if x[0] in left_labels:
                left_points.append(y)
            elif x[0] in right_labels:
                right_points.append(y)

        if len(left_points) < 10 or len(right_points) < 10:
            return None, None, False

        sample_num = int(min(len(left_points) * sample_rate, len(right_points) * sample_rate))
        print("Sample number:", sample_num)

        x_left = sample(left_points, sample_num)
        x_right = sample(right_points, sample_num)

        y_left = [1] * len(x_left)
        y_right = [0] * len(x_right)

        x_train = x_left + x_right
        y_train = y_left + y_right

        model = SVC(kernel="linear")
        model.fit(x_train, y_train)

        weight = model.coef_[0]  # 取出权重矩阵
        bias = model.intercept_[0]

        k0 = -weight[0] / weight[2]
        k1 = -weight[1] / weight[2]
        b = -bias / weight[2]

        arr = np.array([k0, k1, -1])
        normal = arr / np.linalg.norm(arr)
        center = model.support_vectors_.mean(axis=0)

        end = time.perf_counter()
        log.info("Extract Symmetry takes: {:.2f}s".format(end - start))
        return center, normal, True

    @staticmethod
    def GenerateMultiThresholdResultFile(results, filename, session):
        dfs = {}
        for k, v in results.items():
            items_str = session.GetStringConfig(k, '')
            item_list = items_str.split(',')
            value_list = v
            assert len(item_list) == len(value_list), "Uncomparable list for results {}".format(k)
            df = pd.DataFrame({'parameter': item_list, "volume/ml": value_list})
            dfs[k] = df
        with pd.ExcelWriter(filename) as writer:
            for k, v in dfs.items():
                v.to_excel(writer, sheet_name=k, index=False)

    @staticmethod
    def get_statistics(results, others=None):
        CBF_Volumes = results.GetCBFVolumes()
        Tmax_Volumes = results.GetTMaxVolumes()
        CBV_Volumes = results.GetCBVVolumes()

        statistics = {}
        statistics["CBF<30%"] = CBF_Volumes[0]
        statistics["CBF<34%"] = CBF_Volumes[1]
        statistics["CBF<38%"] = CBF_Volumes[2]
        statistics["CBF<50%"] = CBF_Volumes[3]
        statistics["CBF<70%"] = CBF_Volumes[4]

        statistics["CBV<34%"] = CBV_Volumes[0]
        statistics["CBV<38%"] = CBV_Volumes[1]
        statistics["CBV<42%"] = CBV_Volumes[2]
        statistics["CBV<50%"] = CBV_Volumes[3]
        statistics["CBV<70%"] = CBV_Volumes[4]

        statistics["Tmax>10s"] = Tmax_Volumes[0]
        statistics["Tmax>8s"] = Tmax_Volumes[1]
        statistics["Tmax>6s"] = Tmax_Volumes[2]
        statistics["Tmax>4s"] = Tmax_Volumes[3]

        if others:
            statistics.update(others)

        return statistics

    @staticmethod
    def extract_index(filename):
        with open(filename, encoding="utf-8") as file_obj:
            contents = file_obj.read()
            return [int(x) for x in contents.split(",")]

    @staticmethod
    def extract_ventricle(input_image: sitk.Image, gpu_id=0) -> sitk.Image:
        result_image = None
        with tempfile.TemporaryDirectory() as temp_dir:
            input_filename = os.path.join(temp_dir, "input.nii")
            ventricle_filename = os.path.join(temp_dir, "ventricle.nii")
            sitk.WriteImage(input_image, input_filename)
            # command = "{} extract_ventricle.py".format(sys.executable) + " -i " + '\"' + input_filename + '\"' + " -o " \
            #           + '\"' + ventricle_filename + '\"' + " -g {}".format(gpu_id)
            py_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/py/extract_ventricle.py")
            command = '{} {} -i "{}" -o "{}" -g {}'.format(
                sys.executable, py_path, input_filename, ventricle_filename, gpu_id)
            log.info(command)
            p = subprocess.Popen(command, shell=True)
            p.wait()
            result_image = sitk.ReadImage(ventricle_filename)
            result_image.CopyInformation(input_image)
        return result_image

    @staticmethod
    def extract_ich(input_image: sitk.Image, gpu_id=0) -> sitk.Image:
        result_image = None
        with tempfile.TemporaryDirectory() as temp_dir:
            input_filename = os.path.join(temp_dir, "input.nii")
            ich_filename = os.path.join(temp_dir, "ich.nii")
            sitk.WriteImage(input_image, input_filename)
            # command = "{} extract_ich.py".format(sys.executable) + " -i " + '\"' + input_filename + '\"' + " -o " \
            #           + '\"' + ich_filename + '\"' + " -g {}".format(gpu_id)
            py_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/py/extract_ich.py")
            command = '{} {} -i "{}" -o "{}" -g {}'.format(
                sys.executable, py_path, input_filename, ich_filename, gpu_id)
            log.info(command)
            p = subprocess.Popen(command, shell=True)
            p.wait()
            result_image = sitk.ReadImage(ich_filename)
            result_image.CopyInformation(input_image)
        return result_image

    @staticmethod
    def partition_brain(input_image: sitk.Image) -> sitk.Image:
        result_image = None
        with tempfile.TemporaryDirectory() as temp_dir:
            input_filename = os.path.join(temp_dir, "input.nii")
            partition_filename = os.path.join(temp_dir, "partition_label.nii")
            sitk.WriteImage(input_image, input_filename)
            # command = "{} partition_brain.py".format(sys.executable) + " -i " + '\"' + input_filename + '\"' + " -o " \
            #           + '\"' + partition_filename + '\"'
            py_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/py/partition_brain.py")
            command = "{} {} -i '{}' -o '{}'".format(sys.executable, py_path, input_filename, partition_filename)
            log.info(command)
            p = subprocess.Popen(command, shell=True)
            p.wait()
            result_image = sitk.ReadImage(partition_filename)
            result_image.CopyInformation(input_image)
        return result_image

    @staticmethod
    def is_warning(vs):
        left_to_right = vs[0] / (vs[1] + 1e-5)
        right_to_left = vs[1] / (vs[0] + 1e-5)
        reduce = min(left_to_right, right_to_left)
        return abs(reduce) < 0.7

    @staticmethod
    def get_brain_partition_table(mean_res, table_template_dir, temp_dir):
        table_template = [os.path.join(table_template_dir, "ctp-before.html"),
                          os.path.join(table_template_dir, "ctp-after.html")]
        out_before_path = ""
        out_after_path = ""
        with open(table_template[0], "r", encoding='utf-8') as f:
            htmlfile_before = f.read()
            for k, values in mean_res.items():
                for index, value in enumerate(values):
                    if index < 10:
                        jump = 10
                    elif index == 25:
                        jump = 7
                    else:
                        continue
                    vs = [value, values[index + jump]]
                    olds = ["S" + str(index + 1) + k, "S" + str(index + 1 + jump) + k]
                    news = []
                    warning = CtpUtils.is_warning(vs)
                    for v in vs:
                        if not np.isnan(v) and v > 0.0:
                            if warning:
                                news.append('<b style="color: red">{}</b>'.format(round(v, 1)))
                            else:
                                news.append(str(round(v, 1)))
                    for old, new in zip(olds, news):
                        htmlfile_before = htmlfile_before.replace(old, new)
            out_before_path = os.path.join(temp_dir, "ctp-before.png")
            imgkit.from_string(htmlfile_before, out_before_path, options={"width": 890})

        with open(table_template[1], "r", encoding='utf-8') as f:
            htmlfile_after = f.read()
            for k, values in mean_res.items():
                for index, value in enumerate(values):
                    if index >= 20 and index != 20 or index != 21 or index != 22:
                        old = "S" + str(index + 1) + k
                        if not np.isnan(value) and value > 0.0:
                            new = str(round(value, 1))
                        else:
                            new = "N/A"
                        htmlfile_after = htmlfile_after.replace(old, new)
            out_after_path = os.path.join(temp_dir, "ctp-after.png")
            imgkit.from_string(htmlfile_after, os.path.join(temp_dir, out_after_path), options={"width": 890})
        return [out_before_path, out_after_path]
    
    @staticmethod
    def save_4D_CTP_result(session, ctp_tools, result_dir):
        if session.GetBoolConfig("4D-CTA", False):
            itkimage = ctp_tools.GetImageOri(0)  # 执行完4D-cta，删除
            think = CtpUtils.itk_to_sitk_image(itkimage).GetSpacing()[2]
            # think = 0.5
            log.info(f"The thinknes is {think} ")
            if think <= session.GetIntConfig("thinkness", 1):
                # save img
                save_path = os.path.join(result_dir, "img")
                os.makedirs(save_path, exist_ok=True)
                if not os.path.exists(save_path): os.makedirs(save_path)
                for i in range(ctp_tools.GetNumberOfSeries()):
                    ctp_tools.GetImageOri(i).Write(os.path.join(save_path, 'img{}.nii.gz'.format(i)))
                log.info("save img done")

                # save mask
                save_mask_path = os.path.join(result_dir, "brain-mask.nii.gz")
                ctp_tools.GetMaskImage("orimask").Write(save_mask_path)
                log.info("save mask done")

                # get debone mask using cta-head-model
                bone_mask = os.path.join(result_dir, "bone_mask")
                os.makedirs(bone_mask, exist_ok=True)
                # get 4d-cta by debone mask, and brain mask
                save_boning = os.path.join(result_dir, "4D_CTA")  # 最终的结果保存路径
                os.makedirs(save_boning, exist_ok=True)

                debone_app = CTAHeadVesselSegApp()
                other_value = 0  # 其他组织CT值
                brain_mask = sitk.GetArrayFromImage(sitk.ReadImage(save_mask_path))
                for i in glob.glob(save_path + "/*.nii.gz"):  # img0.nii.gz
                    o = os.path.join(bone_mask, os.path.basename(i).split(".nii")[0])
                    s = time.time()
                    debone_app.run(input=i, output=o)  # debone
                    log.info(f"debone takes: {time.time() - s} s")
                    img = sitk.ReadImage(i)
                    debone_mask_path = os.path.join(o, "cta_head_vessels_seg", "cta_head_vessels_seg.nii.gz")
                    img_boning = boing(img, brain_mask, debone_mask_path, other_value)
                    img_itk = sitk.GetImageFromArray(img_boning)
                    img_itk.SetOrigin(img.GetOrigin())
                    img_itk.SetSpacing(img.GetSpacing())
                    img_itk.SetDirection(img.GetDirection())
                    sitk.WriteImage(img_itk, os.path.join(save_boning, os.path.basename(i)))
                    log.info(f"boning {os.path.basename(i)} done")
                log.info("boning done")
            else:
                log.info(f"Not Show 4D-CTA! And the thinkness = {think}")


class CtpHandler:
    __FEATURE_MAPS = ["cbf", "cbv", "mtt", "tmax", "ps", "ttp"]
    FOLDER_DESC_MAP = {
        "USC_UGuard_CTP_AIF_VOF_CSF_Location": "USC-UGuard AIF-VOF Location",
        "USC_UGuard_CTP_Summary": "USC-UGuard CTP Summary",
        "USC_UGuard_CTP_Total_Summary": "USC-UGuard CTP Total Summary",
        "USC_UGuard_CTP_Parameter_Colored_Map_CBF": "USC-UGuard Perfusion Parameter Maps Colored CBF",
        "USC_UGuard_CTP_Parameter_Colored_Map_CBV": "USC-UGuard Perfusion Parameter Maps Colored CBV",
        "USC_UGuard_CTP_Parameter_Colored_Map_MTT": "USC-UGuard Perfusion Parameter Maps Colored MTT",
        "USC_UGuard_CTP_Parameter_Colored_Map_PS": "USC-UGuard Perfusion Parameter Maps Colored PS",
        "USC_UGuard_CTP_Parameter_Colored_Map_TTP": "USC-UGuard Perfusion Parameter Maps Colored TTP",
        "USC_UGuard_CTP_Parameter_Colored_Map_Tmax": "USC-UGuard Perfusion Parameter Maps Colored Tmax",
        "USC_UGuard_CTP_MIP": "USC-UGuard CTP MIP"
    }
    DATA_TYPE_MAP = {
        "2": np.int8,
        "4": np.int16,
        "8": np.int32,
        "256": np.uint8,
        "512": np.uint16,
        "768": np.uint32,
    }

    def __init__(self, ctp_tools, dcm_dir):
        self.ctp_tools = ctp_tools
        self.original_dataset = DicomUtils.read_dcm(FileUtils.get_one(dcm_dir))

    def generate_dcm_report(self, png_dir, dcm_dir, modality):
        series_number = 99
        FileUtils.rebuild_dirs(dcm_dir)
        for subdir in sorted(os.listdir(png_dir)):
            subdir_path = os.path.join(png_dir, subdir)
            series_instance_uid = DicomUtils.generate_uid()
            series_description = self.FOLDER_DESC_MAP.get(subdir)
            series_number += 1
            instance_number = 0
            output_dir = os.path.join(dcm_dir, subdir)
            os.makedirs(output_dir)
            file_list = os.listdir(subdir_path)
            file_list.sort(key=lambda x: int(x.replace("img_", "").replace(".png", "")))
            for filename in file_list:
                file_path = os.path.join(subdir_path, filename)
                instance_number += 1
                output_path = os.path.join(output_dir, "{}.dcm".format(instance_number))
                DicomUtils.png2dcm(self.original_dataset, file_path, series_instance_uid, series_number,
                                   series_description, modality, instance_number, output_path)
                log.debug("CTP > series:{}, series desc:{}, series number:{}, convert {} to {}".format(
                    series_instance_uid, series_description, series_number, file_path, output_path))
            log.info("CTP > series:{}(no:{},desc:{}) generate {} dcm".format(
                series_instance_uid, series_number, series_description, len(file_list)))

    def generate_mip(self, output, modality):
        mip_series_dir = os.path.join(output, "USC_UGuard_CTP_MIP")
        # self.ctp_tools.ExportImageToDicom(self.ctp_tools.GetInternelImage("mip"), mip_series_dir)
        mip_path = os.path.join(os.path.dirname(output), "mip.nii.gz")
        self.ctp_tools.GetInternelImage("mip").Write(mip_path)
        self.generate_mip_series(mip_path, mip_series_dir, modality)
        png_dir = os.path.join(output, "../png/USC_UGuard_CTP_MIP")
        os.makedirs(png_dir, exist_ok=True)
        for filename in sorted(os.listdir(mip_series_dir)):
            dcm_path = os.path.join(mip_series_dir, filename)
            dest_path = os.path.join(png_dir, "img_{}.png".format(os.path.splitext(filename)[0]))
            DicomUtils.dcm2png(dcm_path, dest_path)
        return mip_series_dir

    @staticmethod
    def get_series_info(report_dir):
        desc_uid_map = {}
        for dir_name in os.listdir(report_dir):
            dir_path = os.path.join(report_dir, dir_name)
            dataset = DicomUtils.read_dcm(FileUtils.get_one(dir_path))
            desc_uid_map[dataset.SeriesDescription] = dataset.SeriesInstanceUID
        return desc_uid_map

    def generate_grayscale(self, is_toshiba, desc_uid_mapping):
        grayscale_info = dict()
        study_instance_uid = self.original_dataset.StudyInstanceUID
        root_path = os.path.join(Config.DIR_STATIC, study_instance_uid)
        if not is_toshiba:
            series_instance_uid = self.original_dataset.SeriesInstanceUID
            root_path = os.path.join(root_path, series_instance_uid)
        root_path = os.path.join(root_path, "feature_maps")
        FileUtils.rebuild_dirs(root_path)
        for fm in CtpHandler.__FEATURE_MAPS:
            fm_path = os.path.join(root_path, "{}.nii.gz".format(fm))
            self.ctp_tools.GetFeatureImage("feature_{}".format(fm)).Write(fm_path)
            # 第一个参数是窗宽，第二个参数是窗位
            ww_wl = self.ctp_tools.GetWWWL(fm)
            log.info("series:{}, WW-WL:{}".format(fm, ww_wl))
            _desc = "USC-UGuard Perfusion Parameter Maps Colored {}".format("Tmax" if fm == "tmax" else fm.upper())
            grayscale_info[fm] = dict(path=fm_path.replace(Config.DIR_ROOT, ""),
                                      seriesInstanceUID=desc_uid_mapping.get(_desc),
                                      windowWidth=ww_wl[0], windowLevel=ww_wl[1])
        return grayscale_info

    def update_report(self, series_dir, modality):
        for dir_path, dir_names, filenames in os.walk(series_dir):
            for filename in filenames:
                file_path = os.path.join(dir_path, filename)
                ds = DicomUtils.read_dcm(file_path)
                ds.Modality = modality
                ds.save_as(file_path)
                log.debug("update report: {}".format(file_path))

    def generate_mip_series(self, nii_image, dcm_dir, modality):
        series_number = 100 + len(os.listdir(os.path.dirname(dcm_dir)))
        os.makedirs(dcm_dir)
        itk_img = sitk.ReadImage(nii_image)
        img_array = sitk.GetArrayFromImage(itk_img)
        img_num = img_array.shape[0]
        pixel_spacing = itk_img.GetMetaData("pixdim[1]")
        slice_thickness = itk_img.GetMetaData("pixdim[3]")
        bits_allocated = itk_img.GetMetaData("bitpix")
        columns = itk_img.GetMetaData("dim[1]")
        rows = itk_img.GetMetaData("dim[2]")
        nii_type = itk_img.GetMetaData("datatype")
        dtype = self.DATA_TYPE_MAP.get(nii_type, np.uint16)
        patient_id = self.original_dataset.PatientID
        patient_name = self.original_dataset.PatientName
        study_instance_uid = self.original_dataset.StudyInstanceUID
        series_instance_uid = DicomUtils.generate_uid()
        for i in range(img_num):
            instance_number = i + 1
            new_img = sitk.GetImageFromArray(img_array[i, :, :].astype(dtype))
            now = datetime.datetime.now()
            create_date = now.strftime("%Y%m%d")
            create_time = now.strftime("%H%M%S")
            new_img.SetMetaData('0008|0021', create_date)
            new_img.SetMetaData('0008|0023', create_date)
            new_img.SetMetaData('0008|0031', create_time)
            new_img.SetMetaData('0008|0033', create_time)
            new_img.SetMetaData('0008|0060', str(modality))
            new_img.SetMetaData('0008|0070', "UnionStrong")
            new_img.SetMetaData('0008|1010', "USC-UGuard")
            new_img.SetMetaData('0008|103e', "USC-UGuard CTP MIP")
            new_img.SetMetaData('0008|1090', "UGuard")
            new_img.SetMetaData('0010|0010', str(patient_name))
            new_img.SetMetaData('0010|0020', str(patient_id))
            new_img.SetMetaData('0020|0013', str(instance_number))
            new_img.SetMetaData('0018|0050', slice_thickness)
            new_img.SetMetaData('0028|0030', pixel_spacing)
            new_img.SetMetaData('0028|0100', str(bits_allocated))
            new_img.SetMetaData('0028|0011', columns)
            new_img.SetMetaData('0028|0010', rows)
            new_img.SetMetaData('0028|1050', "80")
            new_img.SetMetaData('0028|1051', "150")
            dcm_path = os.path.join(dcm_dir, "{}.dcm".format(instance_number))
            sitk.WriteImage(new_img, dcm_path)
            ds = DicomUtils.read_dcm(dcm_path)
            ds.StudyInstanceUID = study_instance_uid
            ds.SeriesInstanceUID = series_instance_uid
            ds.SeriesNumber = series_number
            ds.save_as(dcm_path)
        log.info("CTP > series:{}(no:{},desc:USC-UGuard CTP MIP) generate {} dcm".format(
            series_instance_uid, series_number, img_num))

    @staticmethod
    def parse_ctp_results(results):
        # 获得CBF下降的体积，分别为<30%,<34%,<38%,<50%,<70%
        v_cbf = results.GetCBFVolumes()
        log.info("CBF Volume(<30%,<34%,<38%,<50%,<70%): {}".format(v_cbf))
        # #获得CBV下降的体积，分别为<34%,<38%,<42%,<50%,<70%
        v_cbv = results.GetCBVVolumes()
        log.info("CBV Volume(<34%,<38%,<42%,<50%,<70%): {}".format(v_cbv))
        # #获得TMax延长的体积，分别为>10s,>8s,>6s,>4s
        v_tmax = results.GetTMaxVolumes()
        log.info("TMax Volume(>10s,>8s,>6s,>4s): {}".format(v_tmax))
        # 获取左右脑，分别是0是左脑，1是右脑
        affected_side = results.GetAffectedSide()
        log.info("Affected Side(0-left brain, 1-right brain): {}".format(affected_side))
        return dict(v_cbf=v_cbf, v_cbv=v_cbv, v_TMax=v_tmax, affectedSide=affected_side)

    def backup_result(self, output, session, dcm_dir, run_time, ctp_results):
        # 生成多阈值结果
        multi_threshold_summary = self.ctp_tools.GetMultiThresholdSummary()
        if len(multi_threshold_summary.items()) > 0:
            mt_path = os.path.join(output, "multi_thresholds.xlsx")
            CtpUtils.GenerateMultiThresholdResultFile(multi_threshold_summary, mt_path, session)
        # 生成统计信息
        statistics = {"Path": dcm_dir}
        statistics.update({"Time": str(run_time)})
        statistics.update(CtpUtils.get_statistics(ctp_results))
        statistics.update(self.ctp_tools.GetImageStatistics())
        affected_side = "None"
        if self.ctp_tools.GetAffectedSide() == 0:
            affected_side = "Left"
        elif self.ctp_tools.GetAffectedSide() == 1:
            affected_side = "Right"
        affected_region = "UKnown"
        if self.ctp_tools.GetAffectedRegion() == 1:
            affected_region = "AC"
        elif self.ctp_tools.GetAffectedRegion() == 2:
            affected_region = "PC"
        elif self.ctp_tools.GetAffectedRegion() == 3:
            affected_region = "None"
        statistics.update({"AffectedSide": affected_side, "AffectedRegion": affected_region})
        np.save(os.path.join(output, "statistics.npy"), statistics)
        FileUtils.save_json(statistics, os.path.join(output, "statistics.json"))
        # 备份配置
        config_name = "config.ini"
        FileUtils.copy_file(os.path.join(pymipf.GetSession().GetConfigPath(), config_name),
                            os.path.join(output, config_name))
