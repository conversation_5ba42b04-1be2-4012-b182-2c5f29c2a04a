import SimpleITK as sitk
import os
from brain_seg.run import get_segmentation, get_model, get_nuclei_confidence
from brain_seg.image_IO.local_load import load_images
import argparse
import numpy as np
from batchgenerators.utilities.file_and_folder_operations import save_json
from brain_seg.utils.constant import ASPECTS_COLORS
from brain_seg.utils.visulization import get_images_with_masks
from brain_seg.utils.utils import to_one_hot, save_img
 
 
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-i", "--input_file", help="File of image to extract ventricle!", required=True)
    parser.add_argument("-o", "--output_file", help="Output file!", required=True)
    args = parser.parse_args()
 
    image = sitk.ReadImage(args.input_file)
    image_array = sitk.GetArrayFromImage(image)
    spacing = image.GetSpacing()
 
    model, spacing_nn, num_pool_per_axis = get_model()
    seg_array, box = get_segmentation(image_array, spacing, model, spacing_nn, num_pool_per_axis,
                                      max_brain_length=180, crop_skull=True, keep_largest_component=False,
                                      m1_m6_fixed=False)
    seg = sitk.GetImageFromArray(seg_array)
    seg.CopyInformation(image)
    sitk.WriteImage(seg, args.output_file)
