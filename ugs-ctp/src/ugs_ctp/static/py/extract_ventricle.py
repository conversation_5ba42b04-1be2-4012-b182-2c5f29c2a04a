import os

from ventricle_segmentation import *
import argparse
import GP<PERSON>til

def check_and_set_gpu(gpu_id):
    # Try 300 times at an interval of 10 seconds, then give up
    device_id_list = GPUtil.getFirstAvailable(order='first', attempts=300, interval=10)
    if device_id_list:
        os.environ['CUDA_DEVICE_ORDER'] = 'PCI_BUS_ID'
        os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
        print('Setting CUDA_VISIBLE_DEVICES to ' + str(gpu_id))
    else:
        raise Exception('No GPU device is available')

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("-i", "--input_file", help="File of image to extract ventricle!", required=True)
    parser.add_argument("-o", "--output_file", help="Output file!", required=True)
    parser.add_argument("-g", "--gpu", type=int, help="GPU id", default=0)
    args = parser.parse_args()

    check_and_set_gpu(args.gpu)

    is_cuda_gpu_available = tf.test.is_gpu_available(cuda_only=True)
    if not is_cuda_gpu_available:
        raise Exception('No GPU device is available')
    os.environ['TF_XLA_FLAGS'] = '--tf_xla_enable_xla_devices'
    physical_devices = tf.config.list_physical_devices('GPU')
    if args.gpu < len(physical_devices):
        tf.config.experimental.set_memory_growth(physical_devices[args.gpu], True)
    else:
        raise Exception('No GPU device is available')

    model = get_ventricle_model(True)
    sitkImage = sitk.ReadImage(args.input_file)
    venticle_label, results = ct_ventricle_seg(sitkImage, model)
    sitk.WriteImage(venticle_label,args.output_file)