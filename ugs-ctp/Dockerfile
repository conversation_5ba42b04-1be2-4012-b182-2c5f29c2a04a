# 基础镜像
FROM 172.16.30.132:808/uguard/ubuntu16-py38-cuda113:230916
# 设置工作目录和环境变量
WORKDIR /ugs_ctp/
ENV DEBIAN_FRONTEND=noninteractive \
    LD_LIBRARY_PATH=/usr/local/mitk/lib:/usr/local/mitk/lib/MitkCore:/usr/local/opencv4.5.4/lib:/usr/local/cuda/lib64:/usr/local/cuda/targets/x86_64-linux/lib:/usr/lib/usia:$LD_LIBRARY_PATH \
    QT_QPA_PLATFORM_PLUGIN_PATH=/usr/local/mitk/lib/plugins \
    QT_QPA_PLATFORM=offscreen

# 1. 安装系统依赖和wkhtmltopdf
RUN apt-get update && apt-get install -y wget \
    && wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox_0.12.6-1.bionic_amd64.deb \
    && apt install -y ./wkhtmltox_0.12.6-1.bionic_amd64.deb \
    && rm wkhtmltox_0.12.6-1.bionic_amd64.deb \
    && rm -rf /var/lib/apt/lists/*


COPY ./docker/libpng12-0_1.2.54-1ubuntu1.1_amd64.deb ./
RUN dpkg-deb -x libpng12-0_1.2.54-1ubuntu1.1_amd64.deb libpng12 && cp libpng12/lib/x86_64-linux-gnu/libpng12.so.0 /usr/local/lib/

# 安装 imgkit
RUN pip install --no-cache-dir imgkit nnunet==1.7.1


# 2. 安装深度学习模型相关包
# 2.1 安装 PyTorch (从官方源)
RUN pip install --no-cache-dir \
    torch==1.11.0+cu113 \
    torchvision==0.12.0+cu113 \
    torchaudio==0.11.0 \
    --extra-index-url https://download.pytorch.org/whl/cu113

# 2.2 安装内部包
RUN pip install --no-cache-dir \
    brain_seg==1.1.0b1 \
    ctp_vessel_segmentation==1.2.0 \
    ich_seg==1.0.0 \
    usal_torch==1.2.1rc0 \
    usal_apps==0.2.5rc0 \
    -i http://************/simple/ \
    --trusted-host ************

# 3. 安装运行依赖
RUN wget http://************:18080/mitk/mitk-3.0.0-linux_x86_64.deb \
    && wget http://************:18080/opencv/opencv-4.5.4-a4000-linux_x86_64.deb \
    && dpkg -i mitk-3.0.0-linux_x86_64.deb \
    && dpkg -i opencv-4.5.4-a4000-linux_x86_64.deb \
    && rm mitk-3.0.0-linux_x86_64.deb opencv-4.5.4-a4000-linux_x86_64.deb

# 4. 安装算法包
RUN pip install --no-cache-dir \
    usia_a4000==1.3.4rc0 \
    -i http://************/simple/ \
    --trusted-host ************
# 5. 安装平台依赖
COPY ./src/ugs_ctp/* ./
COPY ./requirements.txt requirements.txt
RUN pip install -r /ugs_ctp/requirements.txt
RUN apt update && apt install libfreetype6-dev -y
# 6. libCOPY
RUN rm -rf /etc/fonts
COPY ./docker/fonts /etc/
RUN rm -rf /lib/x86_64-linux-gnu/libfontconfig.so*
RUN rm -rf /lib/x86_64-linux-gnu/libfreetype.so*
COPY ./docker/libfontconfig.so.1.9.0  /lib/x86_64-linux-gnu/
COPY ./docker/libfreetype.so.6.12.1  /lib/x86_64-linux-gnu/
# COPY ./docker/libfreetype.so.6  /lib/x86_64-linux-gnu/
# COPY ./docker/libfontconfig.so.1  /lib/x86_64-linux-gnu/
RUN rm -f /lib/x86_64-linux-gnu/libfontconfig.so.1 && \
    ln -s /lib/x86_64-linux-gnu/libfontconfig.so.1.9.0 /lib/x86_64-linux-gnu/libfontconfig.so.1
RUN rm -f /lib/x86_64-linux-gnu/libfreetype.so.6 && \
    ln -s /lib/x86_64-linux-gnu/libfreetype.so.6.12.1 /lib/x86_64-linux-gnu/libfreetype.so.6
# 清理缓存
RUN apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*