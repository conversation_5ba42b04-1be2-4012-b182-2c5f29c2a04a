#!/bin/bash
# 监控修复效果脚本

set -e

echo "=== UGS-Transport 连接修复监控脚本 ==="

CONTAINER_NAME="ugs-transport"
LOG_FILE="/tmp/transport-monitor-$(date +%Y%m%d_%H%M%S).log"

# 检查容器状态
check_container_status() {
    echo "检查容器状态..."
    if sudo docker ps | grep -q $CONTAINER_NAME; then
        echo "✓ 容器正在运行"
        sudo docker ps | grep $CONTAINER_NAME
    else
        echo "✗ 容器未运行"
        return 1
    fi
}

# 检查队列状态
check_queue_status() {
    echo "检查RabbitMQ队列状态..."
    echo "transport_algorithm_task 队列:"
    sudo docker exec -it ugs-rabbitmq rabbitmqctl list_queues name messages | grep transport_algorithm_task || echo "队列不存在或无消息"
    
    echo "algorithm_working_status 队列:"
    sudo docker exec -it ugs-rabbitmq rabbitmqctl list_queues name messages | grep algorithm_working_status || echo "队列不存在或无消息"
}

# 检查服务日志
check_service_logs() {
    echo "检查服务日志 (最近50行)..."
    sudo docker logs $CONTAINER_NAME --tail 50 | tee -a $LOG_FILE
}

# 发送测试消息
send_test_message() {
    echo "发送测试重新计算请求..."
    
    # 使用curl发送POST请求
    STUDY_UID="1.3.46.670589.33.1.63809726481737673700001.5221061529557846939"
    API_URL="http://172.16.50.114:4224/api/v1/studies/$STUDY_UID/recalculate/"
    
    echo "发送请求到: $API_URL"
    
    RESPONSE=$(curl -s -w "\n%{http_code}" -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -d '{}' || echo "请求失败")
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    BODY=$(echo "$RESPONSE" | head -n -1)
    
    echo "HTTP状态码: $HTTP_CODE"
    echo "响应内容: $BODY"
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✓ 测试消息发送成功"
        return 0
    else
        echo "✗ 测试消息发送失败"
        return 1
    fi
}

# 监控消息处理
monitor_message_processing() {
    echo "监控消息处理 (30秒)..."
    
    # 记录开始时间
    START_TIME=$(date)
    echo "开始时间: $START_TIME"
    
    # 监控30秒
    timeout 30s sudo docker logs -f $CONTAINER_NAME | while read line; do
        echo "$(date '+%H:%M:%S') $line" | tee -a $LOG_FILE
        
        # 检查是否收到消息
        if echo "$line" | grep -q "receive message"; then
            echo "✓ 检测到消息接收" | tee -a $LOG_FILE
        fi
        
        # 检查连接状态
        if echo "$line" | grep -q "connection established"; then
            echo "✓ 检测到连接建立" | tee -a $LOG_FILE
        fi
        
        # 检查错误
        if echo "$line" | grep -q "ERROR\|error\|Error"; then
            echo "⚠ 检测到错误日志" | tee -a $LOG_FILE
        fi
    done
    
    echo "监控结束时间: $(date)"
}

# 生成报告
generate_report() {
    echo ""
    echo "=== 监控报告 ==="
    echo "时间: $(date)"
    echo "日志文件: $LOG_FILE"
    echo ""
    
    # 统计日志中的关键信息
    if [ -f "$LOG_FILE" ]; then
        echo "日志统计:"
        echo "- 连接建立次数: $(grep -c "connection established" $LOG_FILE 2>/dev/null || echo 0)"
        echo "- 消息接收次数: $(grep -c "receive message" $LOG_FILE 2>/dev/null || echo 0)"
        echo "- 错误次数: $(grep -c -i "error" $LOG_FILE 2>/dev/null || echo 0)"
        echo "- 重连次数: $(grep -c "reconnection" $LOG_FILE 2>/dev/null || echo 0)"
    fi
    
    echo ""
    echo "建议:"
    echo "1. 继续监控日志: sudo docker logs -f $CONTAINER_NAME"
    echo "2. 检查队列状态: sudo docker exec -it ugs-rabbitmq rabbitmqctl list_queues"
    echo "3. 查看完整日志: cat $LOG_FILE"
}

# 主函数
main() {
    echo "开始监控..."
    
    # 检查容器状态
    if ! check_container_status; then
        echo "容器状态异常，退出监控"
        exit 1
    fi
    
    echo ""
    
    # 检查队列状态
    check_queue_status
    
    echo ""
    
    # 检查当前日志
    echo "=== 当前服务状态 ==="
    check_service_logs
    
    echo ""
    echo "=== 开始测试 ==="
    
    # 发送测试消息
    if send_test_message; then
        echo ""
        echo "=== 监控消息处理 ==="
        monitor_message_processing
    else
        echo "测试消息发送失败，跳过消息处理监控"
    fi
    
    # 生成报告
    generate_report
}

# 运行主函数
main "$@"
