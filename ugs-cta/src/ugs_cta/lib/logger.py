#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : logger
@Project : uguard_cta
<AUTHOR> mingxing
@Date    : 2022/4/15 17:31
"""
import logging
import os
from concurrent_log_handler import ConcurrentRotatingFileHandler

_env = os.environ
LOG_DEBUG = _env.get("LOG_DEBUG", "1")
LOG_FILE_SIZE = _env.get("LOG_FILE_SIZE")

try:
    if LOG_FILE_SIZE and isinstance(LOG_FILE_SIZE, str):
        LOG_FILE_SIZE = int(LOG_FILE_SIZE)
    else:
        LOG_FILE_SIZE = 30
except:
    LOG_FILE_SIZE = 30


class MyLogger:
    """单例日志类"""
    _logger = None

    def __new__(cls, *args, **kwargs):
        if cls._logger is None:
            cls._logger = super().__new__(cls, *args, **kwargs)
            cls._logger = logging.getLogger("ugs-cta")
            cls._logger.setLevel(logging.DEBUG if LOG_DEBUG == "1" else logging.INFO)
            formatter = logging.Formatter(
                "%(asctime)s[%(name)s][%(levelname)s][%(process)d-%(thread)d][%(filename)s-%(lineno)s]: %(message)s",
                "%Y-%m-%d %H:%M:%S")
            # console
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            cls._logger.addHandler(console_handler)
            # rolling file
            path = os.path.join("/data/ctpdata/log/ugs-cta")
            if not os.path.exists(path):
                os.makedirs(path, exist_ok=True)
            log_path = F"{path}/ugs-cta.log"
            rotating_file_handler = ConcurrentRotatingFileHandler(
                filename=log_path, maxBytes=1024 * 1024 * LOG_FILE_SIZE, backupCount=3, encoding='utf-8'
            )
            rotating_file_handler.setFormatter(formatter)
            cls._logger.addHandler(rotating_file_handler)
        return cls._logger


class CtaLogger:
    __LOG_DIR = "/data/ctpdata/log/alg-cta"
    __LOG_NAME = "alg-cta.log"

    def __init__(self, max_bytes=1024*1024*10, backup_count=2):
        self.__max_bytes = max_bytes
        self.__backup_count = backup_count
        self.log = MyLogger()
        if not os.path.exists(self.__LOG_DIR):
            os.makedirs(self.__LOG_DIR)
        self.__log_file = os.path.join(self.__LOG_DIR, self.__LOG_NAME)
        self.__create_file(self.__log_file)

    def get_logger(self):
        file_size = os.path.getsize(self.__log_file)
        self.log.info("CTA > log bytes:{}, max bytes:{}".format(file_size, self.__max_bytes))
        if file_size < self.__max_bytes:
            return self.__log_file
        for file_name in sorted(os.listdir(self.__LOG_DIR), reverse=True):
            file_path = os.path.join(self.__LOG_DIR, file_name)
            if file_name == self.__LOG_NAME:
                new_name = "{}.1".format(self.__LOG_NAME)
                os.renames(file_path, os.path.join(self.__LOG_DIR, new_name))
                self.log.info("CTA > rename {} to {}".format(self.__LOG_NAME, new_name))
                self.__create_file(self.__log_file)
                continue
            backup_number = int(file_name[-1])
            if backup_number >= self.__backup_count:
                self.log.info("CTA > remove {} ".format(file_path))
                os.remove(file_path)
                continue
            new_name = "{}.{}".format(self.__LOG_NAME, backup_number + 1)
            os.renames(file_path, os.path.join(self.__LOG_DIR, new_name))
            self.log.info("CTA > rename {} to {}".format(file_name, new_name))
        return self.__log_file

    def __create_file(self, path):
        if not os.path.exists(path):
            with open(path, "w"):
                pass
            self.log.info("CTA > create {}".format(path))
