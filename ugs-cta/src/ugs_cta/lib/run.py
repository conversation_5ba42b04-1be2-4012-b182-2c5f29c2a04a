#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : run
@Project : uguard_cta
<AUTHOR> mingxing
@Date    : 2022/9/7 14:06
"""
import os
import time
from multiprocessing import Manager, Process
import json
import traceback

from lib.common import RabbitProducer, DicomUtils, FileUtils, NiiUtils, VtkUtils, WebApi
from lib.const import Config, RetCode, Consts
from lib.cta import calculate
from lib.logger import MyLogger
from lib.service import VrGenerator, JpgConverter, Cpr, CsMipHandler

log = MyLogger()


def handle_cta(channel, method, study_instance_uid, series_instance_uid, algorithm_type, **kwargs):
    """
    处理Aspects

    :param channel: callback channel
    :param method:  callback method
    :param study_instance_uid: 检查标识
    :param series_instance_uid: 序列标识
    :param algorithm_type: 算法类型
    :param kwargs:
    :return:
    """
    start_time = time.time()
    content = dict(studyInstanceUID=study_instance_uid, seriesInstanceUID=series_instance_uid,
                   algorithmType=algorithm_type)
    # 算法处理进度
    RabbitProducer.send(dict(percent=10, **content))
    # 原图序列
    series_dcm_dir = os.path.join(Config.DIR_DCM, series_instance_uid)
    if not os.path.exists(series_dcm_dir):
        content.update({"percent": 500, "errorCode": RetCode.PLATFORM_SERIES_NOT_FOUND.code})
        RabbitProducer.send(content)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return False
    # 结果输出目录
    output_dir = os.path.join(Config.DIR_ROOT, "cta", study_instance_uid, series_instance_uid)
    FileUtils.rebuild_dirs(output_dir)
    cpr_dir = os.path.join(Config.RESULT_DIR, "cpr", study_instance_uid, series_instance_uid)
    FileUtils.rebuild_dirs(cpr_dir)
    static_output_dir = os.path.join(Config.DIR_ROOT, "static", study_instance_uid, series_instance_uid)
    FileUtils.rebuild_dirs(static_output_dir)
    # SimpleITK Image
    sitk_start_time = time.time()
    origin_image_path = os.path.join(output_dir, "input.nii.gz")
    NiiUtils.dcm2nii(series_dcm_dir, origin_image_path)
    log.info("CTA[study:{}, series:{}] > generate simpleitk image in {:.5}s".format(
        study_instance_uid, series_instance_uid, (time.time()-sitk_start_time)))
    # CTA计算
    manager = Manager()
    result = manager.dict()
    cta_process = Process(target=calculate,
                          args=(RabbitProducer.send, dict(**content), origin_image_path, output_dir, result))
    cta_process.start()
    cta_process.join()
    log.info("CTA[study: {}, series: {}] > result:{}".format(study_instance_uid, series_instance_uid, result))
    code = result.get("code", RetCode.UNKNOWN.code)
    # 检查计算状态
    if code != RetCode.CTA_OK.code:
        content.update({"percent": 500, "errorCode": code})
        RabbitProducer.send(content)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return False
    vessel_nii = result.get("vesselNii", None)
    if not vessel_nii:
        content.update({"percent": 502, "errorCode": RetCode.PLATFORM_VESSEL_NOT_FOUND.code})
        RabbitProducer.send(content)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return
    # 保存头颈血管灰度图
    NiiUtils.save_nii_gz(vessel_nii, os.path.join(output_dir, "cta_vessel.nii.gz"))
    vessel_head_nii = result.get("vesselHeadNii", None)
    # 保存头部血管灰度图
    if vessel_head_nii:
        NiiUtils.save_nii_gz(vessel_head_nii, os.path.join(output_dir, "cta_vessel_head.nii.gz"))
    # 算法耗时
    run_time = result.get("runTime")
    RabbitProducer.send(dict(percent=81, consumer_time=round(run_time, 2), **content))
    # 拷贝dcm、保存患者信息
    one_dcm_path = FileUtils.get_one(series_dcm_dir)
    dcm_tar_path = os.path.join(output_dir, "dcm.dcm")
    FileUtils.copy_file(one_dcm_path, dcm_tar_path)
    # 保存患者信息
    dcm_dataset = DicomUtils.read_dataset(one_dcm_path)
    file_data = dict(patientId=dcm_dataset.PatientID, patientName=str(dcm_dataset.PatientName),
                     studyId=dcm_dataset.StudyID)
    FileUtils.save_json(file_data, os.path.join(output_dir, "patient.txt"))
    RabbitProducer.send(dict(percent=82, **content))
    # 获取模态配置
    config_data = WebApi.get_config(Consts.CODE_ALGORITHM_RESULT_MODALITY)
    result_modality = config_data[0]["value"] if config_data else Consts.DEFAULT_ALGORITHM_RESULT_MODALITY
    # cs mip 结果
    cs_results = result.get("cs_results", {})
    log.info("CTA[study: {}, series: {}] > cs result:{}".format(study_instance_uid, series_instance_uid, cs_results))
    cs_status = False
    try:
        # 侧枝循环处理
        cs_image_path = os.path.join(output_dir, "cta_cs_registered.nii.gz")
        if cs_results.get("status", False) and os.path.exists(cs_image_path):
            cs_mip_output = os.path.join(static_output_dir, "COLLATERAL_CIRCULATION_USC")
            mip_handler = CsMipHandler(cs_image_path, dcm_dataset, result_modality, 100, cs_mip_output)
            mip_handler.start()
            report_msg = dict(reportPath=static_output_dir, percent=83, **content)
            RabbitProducer.send(report_msg)
            cs_status = True
    except:
        cs_results = None
        log.error(f"CTA [study: {study_instance_uid}, series: {series_instance_uid}] > cs mip error: {traceback.format_exc()}")
    # 生成VR
    vr_dir = os.path.join(Config.RESULT_DIR, "jpg", study_instance_uid, series_instance_uid)
    FileUtils.rebuild_dirs(vr_dir)
    log.info("CTA[study: {}, series: {}] > generate vr to {}".format(study_instance_uid, series_instance_uid, vr_dir))
    generator = VrGenerator(itk_image=vessel_nii, output_dir=vr_dir, angle_offset=15)
    header_ret_code = generator.create()
    generator.destroy()
    del generator
    if vessel_head_nii:
        header_generator = VrGenerator(itk_image=vessel_head_nii, output_dir=vr_dir, angle_offset=15)
        header_generator.create(is_head=True)
        header_generator.destroy()
        del header_generator
    vr_status = False
    if not header_ret_code:
        # VR转DCM
        report_dcm_dir = os.path.join(output_dir, "dcm")
        FileUtils.rebuild_dirs(report_dcm_dir)
        converter = JpgConverter(dcm_dataset, vr_dir, report_dcm_dir, result_modality)
        series_number = 101 if cs_status else 100
        vr_status, series_uid_dict = converter.convert_and_send(series_number=series_number)
        report_msg = dict(reportPath=report_dcm_dir, percent=90, **content)
        RabbitProducer.send(report_msg)
    # cs_status: False 表示失败   ret_code： None表示成功 vr_status： False 表示失败
    if not (cs_status or (not header_ret_code and vr_status)):  # 表示cs mip 和vr vmip都没成功
        if not cs_status and (header_ret_code or not vr_status):
            content.update({"percent": 500, "errorCode": RetCode.CTP_ERROR.code})
        elif not cs_status:
            content.update({"percent": 500, "errorCode": RetCode.PLATFORM_GENERATE_CS_MIP_ERROR.code})
        else:
            content.update({"percent": 500, "errorCode": header_ret_code.code})
        RabbitProducer.send(content)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return
    RabbitProducer.send(dict(percent=93, **content))
    try:
        # CPR
        vessel_segs_flag = result.get("vesselSegsFlag", None)
        log.info("CTA[study: {}, series: {}] > vessel segmentation:{}".format(
            study_instance_uid, series_instance_uid, vessel_segs_flag))
        if vessel_segs_flag:
            for seg_name in vessel_segs_flag:
                if not vessel_segs_flag.get(seg_name, False):
                    continue
                nii_path = os.path.join(output_dir, "{}_straightened_cpr.nii.gz".format(seg_name))
                vti_path = os.path.join(cpr_dir, "{}.vti".format(seg_name))
                VtkUtils.nii2vti(nii_path, vti_path)
                # 拉伸cpr
                Cpr.generate_stretched_cpr_json(study_instance_uid, series_instance_uid, seg_name)
            RabbitProducer.send(dict(percent=95, **content))
    except:
        log.error(f"CTA[study: {study_instance_uid}, series: {series_instance_uid}] > generate cpr error: { traceback.format_exc()}")
    try:
        # 生成VTI
        vti_save_path = os.path.join(Config.RESULT_DIR, "vti", series_instance_uid)
        VtkUtils.itk2vti(origin_image_path, vti_save_path)
        json_file = os.path.join(vti_save_path, "index.json")
        vti_index_info = FileUtils.read_json(json_file)
        log.info(vti_index_info)
        vti_id = vti_index_info["pointData"]["arrays"][0]["data"]["ref"]["id"]
        file_path = os.path.join(vti_save_path, "data", vti_id)
        FileUtils.gzip_file(file_path, file_path + ".gz")
    except:
        log.error(f"CTA[study: {study_instance_uid}, series: {series_instance_uid}] > generate mpr error: {traceback.format_exc()}")

    # 回传通知
    RabbitProducer.send(dict(BackReport=True, percent=98, **content))
    # 完成通知
    done_msg = dict(percent=100, errorCode=RetCode.CTA_OK.code, **content)
    text_result = None
    if cs_results:
        text_result = dict(status=cs_results["status"], score=cs_results["score"], side=cs_results["side"])
    image_quality = result.get("imageQuality", {})
    log.info("CTA[study: {}, series: {}] > ImageQuality:{}".format(
        study_instance_uid, series_instance_uid, image_quality))
    if image_quality and not image_quality.get("status", True):
        message = image_quality.get("message", "")
        success_code = Consts.SUCCESS_CODE.get(message, 200)
        if not result:
            text_result = dict(successCode=success_code)
        else:
            text_result.update({"successCode": success_code})
    if text_result:
        done_msg["result"] = json.dumps(text_result)
    RabbitProducer.send(done_msg)
    channel.basic_ack(delivery_tag=method.delivery_tag)
    # 结束
    end_time = time.time()
    log.info("CTA[study: {}, series: {}] > CTA: {:.5}s, Platform: {:.5}s, Total: {:.5}s".format(
        study_instance_uid, series_instance_uid, run_time, (end_time - start_time - run_time), (end_time - start_time)))
    log.info("********************** success **********************")
    return True

