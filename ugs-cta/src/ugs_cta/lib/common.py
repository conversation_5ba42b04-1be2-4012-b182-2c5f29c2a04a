#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : common.py
@Project : uguard_cta
<AUTHOR> mingxing
@Date    : 2022/9/7 13:48
"""
import copy
import datetime
import gzip
import json
import os
import pathlib
import random
import shutil
import traceback
import zipfile

import pika
import pydicom.uid
import requests
from requests.auth import HTTPBasicAuth
import numpy as np
from PIL import Image
import SimpleITK as sitk
import vtkmodules.all as vtk
from vtk.util import numpy_support

from lib.const import Config, Consts
from lib.logger import MyLogger

log = MyLogger()


class StringUtils:
    __BOOL_VALID = {"true": True, "1": True, "false": False, "0": False}

    @staticmethod
    def to_bool(value):
        if isinstance(value, bool):
            return value
        if not isinstance(value, str):
            raise ValueError("invalid literal for boolean, Not a string.")
        lower_value = value.lower()
        if lower_value not in StringUtils.__BOOL_VALID:
            raise ValueError("invalid literal for boolean: {}".format(value))
        return StringUtils.__BOOL_VALID[lower_value]


class FileUtils:
    @staticmethod
    def create_file(file_path):
        if os.path.exists(file_path):
            return
        os.makedirs(os.path.dirname(file_path))
        with open(file_path, "w"):
            pass

    @staticmethod
    def rebuild_dirs(dir_path):
        """
        重建目录

        :param dir_path: 目录路径
        :return:
        """
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
        os.makedirs(dir_path)

    @staticmethod
    def get_one(dir_path):
        """
        获取目录下任意一个文件

        :param dir_path: 目录路径
        :return:
        """
        for root, dirs, files in os.walk(dir_path):
            if not files:
                return None
            for file in files:
                return os.path.join(dir_path, file)

    @staticmethod
    def copy_file(src_path, tar_path):
        """
        拷贝文件

        :param src_path: 原文件路径
        :param tar_path: 目标文件路径
        :return:
        """
        if not os.path.exists(src_path):
            log.info("file not found: {}".format(src_path))
            return
        shutil.copyfile(src_path, tar_path)

    @staticmethod
    def save_json(content: dict, file_path: str, sort_keys: bool = True, indent: int = 4):
        """
        保存JSON

        :param content: 字典
        :param file_path: 保存文件路径
        :param sort_keys: 排序
        :param indent: 间距
        :return:
        """
        with open(file_path, "w") as f:
            json.dump(content, f, sort_keys=sort_keys, indent=indent)

    @staticmethod
    def read_json(file_path):
        """
        读取JSON文件内容

        :param file_path:
        :return:
        """
        return json.load(open(file_path, 'r', encoding="utf-8"))

    @staticmethod
    def gzip_file(file_path, gz_path):
        """
        压缩VTI

        :param file_path: 文件路径
        :param gz_path: 压缩文件路径
        :return:
        """
        try:
            with open(file_path, 'rb') as f_in:
                with gzip.open(gz_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            return True
        except Exception:
            log.error(traceback.format_exc())
            return False

    @staticmethod
    def zip_by_list(dir_path, zip_file_path, match_list):
        """
        压缩指定目录下的特定文件

        :param dir_path: 被压缩的目录
        :param zip_file_path: 压缩文件路径
        :param match_list: 需要压缩的文件清单
        :return: True/False
        """
        if not os.path.exists(dir_path):
            print("{} does not exist".format(dir_path))
            return False
        if os.path.isfile(dir_path):
            print("{} is not a directory".format(dir_path))
            return False
        file_list = []
        for root, dirs, files in os.walk(dir_path):
            for name in files:
                if name in match_list:
                    file_path = os.path.join(root, name)
                    file_list.append(pathlib.PurePath(file_path).as_posix())
        log.info(file_list)
        if len(file_list) == 0:
            print("{} no matching file found".format(dir_path))
            return False
        os.makedirs(os.path.dirname(zip_file_path), exist_ok=True)
        zf = zipfile.ZipFile(zip_file_path, "w", zipfile.zlib.DEFLATED)
        for tar in file_list:
            zf_name = tar[len(dir_path):]
            zf.write(tar, zf_name)
        zf.close()
        print("generate {} > find {} files in {}".format(zip_file_path, len(file_list), dir_path))
        return True


class RabbitProducer:
    ALGORITHM_RESULT = "algorithm_ctp_aspects_result"
    ALGORITHM_STATUS = "algorithm_working_status"

    @staticmethod
    def send(message: dict, queue_name: str = ALGORITHM_RESULT):
        """
        发送消息（提供统一方法）
        RabbitMQ消息生产者

        :param message: 消息
        :param queue_name: 队列名称（默认发送算法结果）
        :return:
        """

        connection = pika.BlockingConnection(
            pika.ConnectionParameters(Config.MQ_HOST, Config.MQ_PORT, "/",
                                      pika.PlainCredentials(Config.MQ_USERNAME, Config.MQ_PASSWORD),
                                      heartbeat=0))
        chanel = connection.channel()
        chanel.queue_declare(queue=queue_name, durable=True)
        body_str = json.dumps(message)
        chanel.basic_publish(exchange="", routing_key=queue_name,
                             properties=pika.BasicProperties(content_type="application/json", delivery_mode=2),
                             body=body_str)
        log.info("MQ send > queue:{}, message:{}".format(queue_name, body_str))
        connection.close()


class DicomUtils:

    @staticmethod
    def generate_uid():
        """
        生成唯一标识

        :return:
        """
        return pydicom.uid.generate_uid()

    @staticmethod
    def generate_child_uid(parent_uid):
        """
        根据父标识，生成子标识

        :param parent_uid: 父标识
        :return:
        """
        tem_uid = parent_uid.split(".")[:-1]
        tem_uid.append(str(random.randint(10 ** 37, 10 ** 38 - 1)))
        return ".".join(tem_uid)

    @staticmethod
    def read_dataset(dcm_path):
        """
        读取DICOM数据集

        :param dcm_path:
        :return:
        """
        return pydicom.dcmread(dcm_path, force=True)

    @staticmethod
    def jpg2dcm(template_dataset, jpg_path, output, series_instance_uid, series_description, sop_instance_uid,
                instance_number, series_number, modality=Consts.DEFAULT_ALGORITHM_RESULT_MODALITY):
        img = np.asarray(Image.open(jpg_path))
        ds = copy.deepcopy(template_dataset)
        ds.Rows, ds.Columns, ds.SamplesPerPixel = img.shape
        ds.PixelData = img.tostring()
        ds.SeriesInstanceUID = series_instance_uid
        ds.SeriesDescription = series_description
        ds.SOPInstanceUID = sop_instance_uid
        ds.InstanceNumber = instance_number
        ds.SeriesNumber = series_number
        now = datetime.datetime.now()
        create_date = now.strftime("%Y%m%d")
        create_time = now.strftime("%H%M%S")
        ds.SeriesDate = create_date
        ds.SeriesTime = create_time
        ds.ContentDate = create_date
        ds.ContentTime = create_time
        ds.Modality = modality
        ds.Manufacturer = "UnionStrong"
        ds.ManufacturerModelName = "UGuard"
        ds.StationName = "USC-UGuard"
        ds.PixelRepresentation = 0
        ds.BitsAllocated = 8
        ds.HighBit = 7
        ds.BitsStored = 8
        ds.PlanarConfiguration = 0
        ds.PhotometricInterpretation = "RGB"
        ds.is_implicit_VR = False
        ds.LossyImageCompression = "01"
        ds.LossyImageCompressionRatio = 10  # default jpeg
        ds.LossyImageCompressionMethod = "ISO_10918_1"
        ds.file_meta.TransferSyntaxUID = pydicom.uid.ExplicitVRLittleEndian
        ds.WindowCenter = "128"
        ds.WindowWidth = "256"
        ds.RescaleIntercept = "0"
        ds.RescaleSlope = "1"
        ds.save_as(output)

    @staticmethod
    def dcm2png(dcm_path, output_path):
        ds = pydicom.dcmread(dcm_path)
        img = ds.pixel_array.astype(float)
        if hasattr(ds, "WindowCenter") and hasattr(ds, "WindowWidth"):
            window_level = int(ds.WindowCenter[0]) if isinstance(ds.WindowCenter, list) else int(ds.WindowCenter)
            window_width = int(ds.WindowWidth[0]) if isinstance(ds.WindowWidth, list) else int(ds.WindowWidth)
            img = (img - window_level + 0.5 * window_width) / window_width
            img[img < 0] = 0
            img[img > 1] = 1
        scaled_image = (np.maximum(img, 0) / (1 if img.max() == 0 else img.max())) * 255.0
        scaled_image = np.uint8(scaled_image)
        final_image = Image.fromarray(scaled_image)
        final_image.save(output_path)


class NiiUtils:
    __DATA_TYPE_MAP = {
        "2": np.int8,
        "4": np.int16,
        "8": np.int32,
        "16": np.float32,
        "256": np.uint8,
        "512": np.uint16,
        "768": np.uint32,
    }

    @staticmethod
    def dcm2nii(dcm_dir, nii_path):
        series_id = sitk.ImageSeriesReader.GetGDCMSeriesIDs(dcm_dir)
        series_file_names = sitk.ImageSeriesReader.GetGDCMSeriesFileNames(dcm_dir, series_id[0])
        series_reader = sitk.ImageSeriesReader()
        series_reader.SetFileNames(series_file_names)
        image3d = series_reader.Execute()
        image_array = sitk.GetArrayFromImage(image3d)
        image_out = sitk.GetImageFromArray(image_array)
        image_out.SetOrigin(image3d.GetOrigin())
        image_out.SetSpacing(image3d.GetSpacing())
        image_out.SetDirection(image3d.GetDirection())
        sitk.WriteImage(image_out, nii_path)

    @staticmethod
    def save_nii_gz(nii_image, target_path):
        """
        保存Nifti图像

        :param nii_image: Nifti图像
        :param target_path: 保存路径
        :return:
        """
        sitk.WriteImage(nii_image, target_path)

    @staticmethod
    def generate_dcm(image_path, dcm_dir, patient_id, patient_name, study_instance_uid, modality, series_number,
                     series_desc, ww, wl):
        os.makedirs(dcm_dir, exist_ok=True)
        itk_img = sitk.ReadImage(image_path)
        img_array = sitk.GetArrayFromImage(itk_img)
        img_num = img_array.shape[0]
        pixel_spacing = itk_img.GetMetaData("pixdim[1]")
        slice_thickness = itk_img.GetMetaData("pixdim[3]")
        bits_allocated = itk_img.GetMetaData("bitpix")
        columns = itk_img.GetMetaData("dim[1]")
        rows = itk_img.GetMetaData("dim[2]")
        nii_type = itk_img.GetMetaData("datatype")
        dtype = NiiUtils.__DATA_TYPE_MAP.get(nii_type, np.uint16)
        series_instance_uid = DicomUtils.generate_uid()
        for i in range(img_num):
            instance_number = i + 1
            new_img = sitk.GetImageFromArray(img_array[i, :, :].astype(dtype))
            now = datetime.datetime.now()
            create_date = now.strftime("%Y%m%d")
            create_time = now.strftime("%H%M%S")
            new_img.SetMetaData('0008|0021', create_date)
            new_img.SetMetaData('0008|0023', create_date)
            new_img.SetMetaData('0008|0031', create_time)
            new_img.SetMetaData('0008|0033', create_time)
            new_img.SetMetaData('0008|0060', str(modality))
            new_img.SetMetaData('0008|0070', "UnionStrong")
            new_img.SetMetaData('0008|103e', series_desc)
            new_img.SetMetaData('0008|1010', "USC-UGuard")
            new_img.SetMetaData('0008|1090', "UGuard")
            new_img.SetMetaData('0010|0010', str(patient_name))
            new_img.SetMetaData('0010|0020', str(patient_id))
            new_img.SetMetaData('0020|0013', str(instance_number))
            new_img.SetMetaData('0018|0050', slice_thickness)
            new_img.SetMetaData('0028|0030', pixel_spacing)
            new_img.SetMetaData('0028|0100', str(bits_allocated))
            new_img.SetMetaData('0028|0011', columns)
            new_img.SetMetaData('0028|0010', rows)
            new_img.SetMetaData('0028|1050', str(wl))
            new_img.SetMetaData('0028|1051', str(ww))
            dcm_path = os.path.join(dcm_dir, "{}.dcm".format(instance_number))
            sitk.WriteImage(new_img, dcm_path)
            ds = DicomUtils.read_dataset(dcm_path)
            ds.StudyInstanceUID = study_instance_uid
            ds.SeriesInstanceUID = series_instance_uid
            ds.SeriesNumber = series_number
            ds.save_as(dcm_path)
        log.info("CTA[study:{}] > series:{}(number:{},desc:{}) generate {} dcm".format(
            study_instance_uid, series_instance_uid, series_number, series_desc, img_num))

class OrthancApi:
    """Orthanc接口"""
    _BASE_URL = F"http://{Config.PACS_HOST}:{Config.PACS_DOCKER_PORT}"
    _AUTH = HTTPBasicAuth(Config.PACS_USERNAME, Config.PACS_PASSWORD)

    @staticmethod
    def upload_image(sop_instance_uid, file_path):
        """
        上传图像

        :param sop_instance_uid: 图像标识
        :param file_path: 文件路径
        :return:
        """
        url = F"{OrthancApi._BASE_URL}/instances/"
        try:
            log.info("Orthanc[post] > url:{}, file: {}".format(url, file_path))
            with open(file_path, 'rb') as f:
                files = {'files': (sop_instance_uid, f, 'application/octet-stream', {'Expires': '0'})}
                response = requests.post(url, files=files, auth=OrthancApi._AUTH)
                log.info("Orthanc[post] > response: {}".format(response.status_code))
                return response.status_code == 200
        except Exception:
            log.error("failed to call orthanc restful: {}".format(traceback.format_exc()))
            return False

    @staticmethod
    def move(data):
        """
        触发C-MOVE请求

        :param data: 请求参数
        :return:
        """
        url = F"{OrthancApi._BASE_URL}/modalities/local/move"
        try:
            log.info("Orthanc[post] > url:{}, data: {}".format(url, data))
            response = requests.post(url=url, json=data, auth=OrthancApi._AUTH)
            log.info("Orthanc[post] > response: {}".format(response.status_code))
            return response.status_code == 200
        except Exception:
            log.error("failed to call orthanc restful: {}".format(traceback.format_exc()))
            return False


class WebApi:
    """平台接口服务"""
    _BASE_URL = F"http://{Config.WEBAPI_HOST}:{Config.WEBAPI_PORT}/api/v1"

    @staticmethod
    def find_pacs_server():
        """
        PACS Server查询

        :return:
        """
        url = F"{WebApi._BASE_URL}/async/pacsserver/"
        try:
            log.info("WebApi[get] > url:{}".format(url))
            response = requests.get(url=url)
            response_body = response.json()
            log.info("WebApi[get] > response[{}]: {}".format(response.status_code, response_body))
            return WebApi.__get_data(response.json())
        except Exception:
            log.error("failed to call WebApi: {}".format(traceback.format_exc()))
            return []

    @staticmethod
    def get_config(config_code):
        """
        配置查询

        :param config_code: 配置编码
        :return:
        """
        url = F"{WebApi._BASE_URL}/systemsettings?code={config_code}"
        try:
            log.info("WebApi[get] > url:{}".format(url))
            response = requests.get(url=url)
            response_body = response.json()
            log.info("WebApi[get] > response[{}]: {}".format(response.status_code, response_body))
            return WebApi.__get_data(response_body)
        except Exception:
            log.error("failed to call WebApi: {}".format(traceback.format_exc()))
            return []

    @staticmethod
    def __get_data(response_body):
        if not response_body or "data" not in response_body:
            return dict()
        return response_body.get("data")


class VtkUtils:

    @staticmethod
    def itk2vtk(itk_image_data):
        """
        将itk图像转换vtk图像

        :param itk_image_data: itk图像
        :return: vtk图像
        """

        imageData = sitk.GetArrayFromImage(itk_image_data)
        origin = itk_image_data.GetOrigin()
        spacing = itk_image_data.GetSpacing()
        size = itk_image_data.GetSize()
        vtk_array = numpy_support.numpy_to_vtk(imageData.ravel(), deep=True, array_type=vtk.VTK_INT)
        vtkImageData = vtk.vtkImageData()
        vtkImageData.SetDimensions(size)
        vtkImageData.SetSpacing(spacing)
        vtkImageData.SetOrigin(origin)
        vtkImageData.SetDirectionMatrix(itk_image_data.GetDirection())
        vtkImageData.GetPointData().SetScalars(vtk_array)
        return vtkImageData

    @staticmethod
    def itk2vti(image_path, vti_save_path):
        """
        ITK image to VTI image

        :param image_path: ITK image
        :return:
        """
        itk_image = sitk.ReadImage(image_path)
        array = sitk.GetArrayFromImage(itk_image)
        shape = array.shape[::-1]
        vtk_data = numpy_support.numpy_to_vtk(array.ravel(), 1, vtk.VTK_SHORT)
        vtkImage = vtk.vtkImageData()
        vtkImage.SetDimensions(shape)
        vtkImage.SetSpacing(itk_image.GetSpacing())
        vtkImage.SetOrigin(itk_image.GetOrigin())
        vtkImage.GetPointData().SetScalars(vtk_data)
        vti_save_dir = os.path.dirname(vti_save_path)
        if not os.path.exists(vti_save_dir):
            os.makedirs(vti_save_dir, exist_ok=True)
        writer = vtk.vtkJSONDataSetWriter()
        writer.SetInputData(vtkImage)
        writer.SetFileName(vti_save_path)
        writer.Write()
        return True

    @staticmethod
    def nii2vti(nii_path, vti_path):
        """
        Nifti转VTK图像

        :param nii_path: nii文件路径
        :param vti_path: vti文件路径
        :return:
        """

        try:
            if not os.path.exists(nii_path):
                log.info("file not found: {}".format(nii_path))
                return
            reader = vtk.vtkNIFTIImageReader()
            reader.SetFileName(nii_path)
            reader.Update()
            writer = vtk.vtkXMLImageDataWriter()
            writer.SetInputConnection(reader.GetOutputPort())
            writer.SetFileName(vti_path)
            writer.Write()
            return True
        except Exception as e:
            log.error(traceback.format_exc())
            return False

    @staticmethod
    def dcm2vti(dcm_path, vti_save_path):
        """
        dcm 转 vti

        :param dcm_path: dcm 路径
        :param vti_save_path: 保存路径
        :return:
        """
        try:
            if not os.path.exists(dcm_path):
                return False
            vti_save_dir = os.path.dirname(vti_save_path)
            if not os.path.exists(vti_save_dir):
                os.makedirs(vti_save_dir, exist_ok=True)
            reader = vtk.vtkDICOMImageReader()
            reader.SetDirectoryName(dcm_path)
            reader.Update()
            # writer = vtk.vtkXMLImageDataWriter()
            writer = vtk.vtkJSONDataSetWriter()
            writer.SetInputConnection(reader.GetOutputPort())
            writer.SetFileName(vti_save_path)
            writer.Write()
            return True
        except Exception as e:
            log.error(traceback.format_exc())
            return False
