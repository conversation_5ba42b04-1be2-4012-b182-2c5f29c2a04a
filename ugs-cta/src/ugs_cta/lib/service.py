#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : uguard_cta
<AUTHOR> mingxing
@Date    : 2022/9/7 14:54
"""
import json
import os
import time
import traceback
from concurrent.futures import ProcessPoolExecutor, as_completed

import vtkmodules.all as vtk
from PIL import Image, ImageDraw, ImageFont
from xvfbwrapper import Xvfb
import <PERSON>IT<PERSON> as sitk

from lib.common import DicomUtils, VtkUtils, NiiUtils
from lib.const import RetCode, CameraOrientation, BodyOrientation
from lib.logger import MyLogger

log = MyLogger()

global VTK_IMAGE_DATA

try:
    import ugs_cta

    DEFAULT_FONT = os.path.join(os.path.dirname(ugs_cta.__file__), "static/font/UbuntuMono-B.ttf")
except:
    DEFAULT_FONT = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/font/UbuntuMono-B.ttf")


def retry(times, wait_time=5):
    def wrapper(func):
        def inner_wrapper(*args, **kwargs):
            i = 0
            while i < times:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    i += 1
                    time.sleep(i*wait_time)
                    log.info("retry:{}".format(str(i)))
                    if i == (times-1):
                        log.error(traceback.format_exc())
        return inner_wrapper
    return wrapper


def write_orientation(file_path, angle, camera_orientation=CameraOrientation.ELEVATION, custom_font=DEFAULT_FONT):
    """
    为生成的图像写方位信息

    :param file_path: 图像文件路径
    :param angle: 相机偏移角度
    :param camera_orientation: 相机偏移方位
    :param custom_font: 字体
    :return:
    """
    img = Image.open(file_path)
    width, height = img.size
    draw = ImageDraw.Draw(img)
    margin = 30
    half_width = round(width / 2)
    half_height = round(height / 2)
    coordinate_up = (half_width, margin)
    coordinate_down = (half_width, height - margin)
    coordinate_left = (margin, half_height)
    coordinate_right = (width - margin, half_height)
    font = ImageFont.truetype(custom_font, 26)
    font_color = (255, 255, 255)
    # camera 向上旋转
    if camera_orientation == CameraOrientation.ELEVATION:
        up_text = BodyOrientation.HEAD.value
        down_text = BodyOrientation.FEET.value
        if 0 < angle < 90:
            up_text = BodyOrientation.HEAD.value+BodyOrientation.POSTERIOR.value
            down_text = BodyOrientation.FEET.value+BodyOrientation.ANTERIOR.value
        elif angle == 90:
            up_text = BodyOrientation.POSTERIOR.value
            down_text = BodyOrientation.ANTERIOR.value
        elif 90 < angle < 180:
            up_text = BodyOrientation.POSTERIOR.value+BodyOrientation.FEET.value
            down_text = BodyOrientation.ANTERIOR.value+BodyOrientation.HEAD.value
        elif angle == 180:
            up_text = BodyOrientation.FEET.value
            down_text = BodyOrientation.HEAD.value
        elif 180 < angle < 270:
            up_text = BodyOrientation.FEET.value+BodyOrientation.ANTERIOR.value
            down_text = BodyOrientation.HEAD.value+BodyOrientation.POSTERIOR.value
        elif angle == 270:
            up_text = BodyOrientation.ANTERIOR.value
            down_text = BodyOrientation.POSTERIOR.value
        elif 270 < angle < 360:
            up_text = BodyOrientation.ANTERIOR.value+BodyOrientation.HEAD.value
            down_text = BodyOrientation.POSTERIOR.value+BodyOrientation.FEET.value
        draw.text(coordinate_up, up_text, font_color, font=font)
        draw.text(coordinate_down, down_text, font_color, font=font)
        draw.text(coordinate_left, BodyOrientation.RIGHT.value, font_color, font=font)
        draw.text(coordinate_right, BodyOrientation.LEFT.value, font_color, font=font)
    # camera 顺时针旋转
    if camera_orientation == CameraOrientation.AZIMUTH:
        left_text = BodyOrientation.RIGHT.value
        right_text = BodyOrientation.LEFT.value
        if 0 < angle < 90:
            left_text = BodyOrientation.RIGHT.value+BodyOrientation.ANTERIOR.value
            right_text = BodyOrientation.LEFT.value+BodyOrientation.POSTERIOR.value
        elif angle == 90:
            left_text = BodyOrientation.ANTERIOR.value
            right_text = BodyOrientation.POSTERIOR.value
        elif 90 < angle < 180:
            left_text = BodyOrientation.ANTERIOR.value+BodyOrientation.LEFT.value
            right_text = BodyOrientation.POSTERIOR.value+BodyOrientation.RIGHT.value
        elif angle == 180:
            left_text = BodyOrientation.LEFT.value
            right_text = BodyOrientation.RIGHT.value
        elif 180 < angle < 270:
            left_text = BodyOrientation.LEFT.value+BodyOrientation.POSTERIOR.value
            right_text = BodyOrientation.RIGHT.value+BodyOrientation.ANTERIOR.value
        elif angle == 270:
            left_text = BodyOrientation.POSTERIOR.value
            right_text = BodyOrientation.ANTERIOR.value
        elif 270 < angle < 360:
            left_text = BodyOrientation.POSTERIOR.value+BodyOrientation.RIGHT.value
            right_text = BodyOrientation.ANTERIOR.value+BodyOrientation.LEFT.value
        draw.text(coordinate_left, left_text, font_color, font=font)
        draw.text(coordinate_right, right_text, font_color, font=font)
        draw.text(coordinate_up, BodyOrientation.HEAD.value, font_color, font=font)
        draw.text(coordinate_down, BodyOrientation.FEET.value, font_color, font=font)
    img.save(file_path)


def generate_vr(camera_orientation: CameraOrientation, angle: int, file_path: str):
    """
    生成VR图像

    :param camera_orientation: 相机方位
    :param angle: 相机移动角度
    :param file_path: 文件路径
    :return: True/False
    """
    try:
        vdisplay = Xvfb(width=1440, height=900)
        vdisplay.start()

        mapper = vtk.vtkGPUVolumeRayCastMapper()
        global VTK_IMAGE_DATA
        mapper.SetInputData(VTK_IMAGE_DATA)

        volume = vtk.vtkVolume()
        volume.SetMapper(mapper)

        popacity = vtk.vtkPiecewiseFunction()
        popacity.AddPoint(-3024, 0)
        popacity.AddPoint(-155.407, 0)
        popacity.AddPoint(217.641, 0.676471)
        popacity.AddPoint(419.736, 0.833333)
        popacity.AddPoint(3071, 0.803922)

        gradientOpacity = vtk.vtkPiecewiseFunction()
        gradientOpacity.AddPoint(0, 1)
        gradientOpacity.AddPoint(255, 1)

        color = vtk.vtkColorTransferFunction()
        color.AddRGBPoint(-3024, 0, 0, 0)
        color.AddRGBPoint(-155.407, 1, 0.850980, 0.450980)
        color.AddRGBPoint(217.641, 0.882353, 0.603922, 0.290196)
        color.AddRGBPoint(419.736, 0.972549, 0.592157, 0.243137)
        color.AddRGBPoint(3071, 0.827451, 0.658824, 1)

        property = vtk.vtkVolumeProperty()
        property.SetScalarOpacity(popacity)

        property.ShadeOn()
        property.SetInterpolationTypeToLinear()
        property.SetShade(0, 1)
        property.SetAmbient(0.2)
        property.SetDiffuse(0.8)
        property.SetSpecular(0.25)
        property.SetSpecularPower(4.0)

        property.SetComponentWeight(0, 1)
        # property.SetDisableGradientOpacity(0)
        property.DisableGradientOpacityOff()
        property.SetGradientOpacity(gradientOpacity)
        property.SetScalarOpacityUnitDistance(0.891927)
        # property.SetEffectiveRange(52.19, 952)
        property.SetInterpolationTypeToLinear()
        volume.SetProperty(property)

        vtk_renderer = vtk.vtkRenderer()
        vtk_renderer.AddActor(volume)
        property.SetColor(color)
        vtk_renderer.SetBackground(0.0, 0.0, 0.0)

        vtk_renderer.GetActiveCamera().SetPosition(0, -0.5, 0)
        # ren.GetActiveCamera().SetFocalPoint(0, 0, 0)
        vtk_renderer.GetActiveCamera().SetViewUp(0, 0, 1)
        # ren.GetActiveCamget_largest_regionera().Azimuth(0)
        # ren.GetActiveCamera().Elevation(0)
        # ren.GetActiveCamera().SetViewAngle(30)
        vtk_renderer.ResetCamera()
        vtk_renderer.GetActiveCamera().SetViewAngle(15)

        if camera_orientation == CameraOrientation.ELEVATION:
            vtk_renderer.GetActiveCamera().Elevation(angle)
        elif camera_orientation == CameraOrientation.AZIMUTH:
            vtk_renderer.GetActiveCamera().Azimuth(angle)
        else:
            log.error("unknown camera_orientation: {}".format(camera_orientation))
            return False
        vtk_renderer.GetActiveCamera().ParallelProjectionOn()
        vtk_renderer.GetActiveCamera().OrthogonalizeViewUp()
        vtk_renderer.GetActiveCamera().SetParallelScale(180)
        # vtk_renderer.ResetCamera()

        ren_win = vtk.vtkRenderWindow()
        ren_win.AddRenderer(vtk_renderer)

        iren = vtk.vtkRenderWindowInteractor()
        iren.SetRenderWindow(ren_win)
        # ren_win.SetSize(600, 600)
        ren_win.SetSize(512, 512)
        ren_win.Render()
        # iren.Start()

        # screenshot code:
        w2if = vtk.vtkWindowToImageFilter()
        w2if.SetInput(ren_win)
        w2if.SetInputBufferTypeToRGB()
        w2if.ReadFrontBufferOff()
        w2if.Update()

        writer = vtk.vtkJPEGWriter()
        writer.SetFileName(file_path)
        writer.SetInputConnection(w2if.GetOutputPort())
        writer.Write()

        write_orientation(file_path, angle, camera_orientation)
        vdisplay.stop()
        log.info("{} success".format(os.path.basename(file_path)))
        return True
    except Exception as e:
        log.error("failed to generate vr: {} - {}".format(os.path.basename(file_path), traceback.format_exc()))
        return False


def generate_mip(camera_orientation: CameraOrientation, angle: int, file_path: str):
    """
    生成MIP图像

    :param camera_orientation: 相机方位
    :param angle: 相机偏移角度
    :param file_path: 文件路径
    :return:
    """
    try:
        vdisplay = Xvfb(width=1440, height=900)
        vdisplay.start()
        mapper = vtk.vtkGPUVolumeRayCastMapper()
        mapper.SetBlendModeToMaximumIntensity()
        global VTK_IMAGE_DATA
        mapper.SetInputData(VTK_IMAGE_DATA)

        volume = vtk.vtkVolume()
        volume.SetMapper(mapper)

        property = vtk.vtkVolumeProperty()
        property.ShadeOff()
        property.SetInterpolationTypeToLinear()
        property.SetShade(0, 1)
        property.SetDiffuse(1.0)
        property.SetAmbient(0.2)
        property.SetSpecular(0.0)
        property.SetSpecularPower(1.0)
        property.SetComponentWeight(0, 1)
        # property.SetDisableGradientOpacity(0)
        property.DisableGradientOpacityOff()
        # property.SetGradientOpacity(gradientOpacity)
        property.SetScalarOpacityUnitDistance(0.891927)
        # property.SetEffectiveRange(52.19, 952)
        property.SetInterpolationTypeToLinear()
        volume.SetProperty(property)

        vtk_renderer = vtk.vtkRenderer()
        vtk_renderer.AddActor(volume)
        vtk_renderer.SetBackground(0.0, 0.0, 0.0)
        vtk_renderer.GetActiveCamera().SetPosition(0, -0.5, 0)
        # vtk_renderer.GetActiveCamera().SetFocalPoint(0, 0, 0)
        vtk_renderer.GetActiveCamera().SetViewUp(0, 0, 1)
        # vtk_renderer.GetActiveCamget_largest_regionera().Azimuth(0)
        # vtk_renderer.GetActiveCamera().Elevation(0)
        # vtk_renderer.GetActiveCamera().SetViewAngle(30)
        vtk_renderer.ResetCamera()
        vtk_renderer.GetActiveCamera().SetViewAngle(15)

        if camera_orientation == CameraOrientation.ELEVATION:
            vtk_renderer.GetActiveCamera().Elevation(angle)
        elif camera_orientation == CameraOrientation.AZIMUTH:
            vtk_renderer.GetActiveCamera().Azimuth(angle)
        else:
            log.error("unknown camera_orientation: {}".format(camera_orientation))
            return False
        vtk_renderer.GetActiveCamera().ParallelProjectionOn()
        vtk_renderer.GetActiveCamera().OrthogonalizeViewUp()
        vtk_renderer.GetActiveCamera().SetParallelScale(180)
        # vtk_renderer.ResetCamera()

        ren_win = vtk.vtkRenderWindow()
        ren_win.AddRenderer(vtk_renderer)

        iren = vtk.vtkRenderWindowInteractor()
        iren.SetRenderWindow(ren_win)
        # ren_win.SetSize(600, 600)
        ren_win.SetSize(512, 512)
        ren_win.Render()
        # iren.Start()

        # screenshot code:
        w2if = vtk.vtkWindowToImageFilter()
        w2if.SetInput(ren_win)
        w2if.SetInputBufferTypeToRGB()
        w2if.ReadFrontBufferOff()
        w2if.Update()

        writer = vtk.vtkJPEGWriter()
        writer.SetFileName(file_path)
        writer.SetInputConnection(w2if.GetOutputPort())
        writer.Write()

        write_orientation(file_path, angle, camera_orientation)
        vdisplay.stop()
        log.info("{} success".format(os.path.basename(file_path)))
        return True
    except Exception as e:
        log.error("failed to generate mip: {} - {}".format(os.path.basename(file_path), traceback.format_exc()))
        return False


class VrGenerator:
    """VR生成器"""

    def __init__(self, itk_image, output_dir, angle_offset):
        """
        加载ITK图像

        :param itk_image: itk图像
        """
        try:
            log.info("load itk image")
            stime = time.time()
            global VTK_IMAGE_DATA
            VTK_IMAGE_DATA = VtkUtils.itk2vtk(itk_image)
            log.info("nii to vtk image, used {:.5}s".format((time.time() - stime)))
            self.image_loaded = True
            self.output_dir = output_dir
            self.angle_offset = angle_offset
        except Exception as e:
            log.error("failed to convert itk to vtk image: {}".format(traceback.format_exc()))
            self.image_loaded = False

    @retry(3)
    def __start(self, task_fun, file_prefix="screenshot", angle_offset=30, is_head=None):
        """
        VR、MIP多进程处理

        :param task_fun: 任务函数
        :param file_prefix: 文件名前缀
        :param angle_offset: 相机偏移角度
        :return: True/False
        """
        log.info("number of CPUs: {}".format(os.cpu_count()))
        stime = time.time()
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        with ProcessPoolExecutor(max_workers=6) as executor:
            task_list = []
            log.info("start {}".format(task_fun.__name__))
            file_num = int(360 / angle_offset)
            for idx in range(file_num):
                angle = idx * angle_offset
                if is_head:  # 当只有头部的时候才会有上下
                    # AP
                    ap_file_path = os.path.join(self.output_dir, file_prefix + "_AP_" + str(angle) + ".jpg")
                    if not os.path.exists(ap_file_path):
                        log.debug("{} - angle: {}, file:{}".format(file_prefix, angle, ap_file_path))
                        task_list.append(executor.submit(task_fun, CameraOrientation.ELEVATION,
                                                        angle, ap_file_path))
                # RL
                rl_file_path = os.path.join(self.output_dir, file_prefix + "_LR_" + str(angle) + ".jpg")
                if not os.path.exists(rl_file_path):
                    log.debug("{} - angle: {}, file:{}".format(file_prefix, angle, rl_file_path))
                    task_list.append(executor.submit(task_fun, CameraOrientation.AZIMUTH,
                                                     angle, rl_file_path))
            fail_flag = False
            for task in as_completed(task_list):
                if not task.result():
                    fail_flag = True
                    log.warning("task: {}".format(task.result()))
            task_list.clear()
            if fail_flag:
                log.info("failed to {}".format(task_fun.__name__))
                return False
            log.info("{} success in {:.5}s".format(task_fun.__name__, (time.time() - stime)))
            return True

    def create(self, is_head=None):
        if not self.image_loaded:
            return RetCode.PLATFORM_VESSEL_LOAD_FAILED
        vr_file_prefix = "VR_Head" if is_head else "VR"
        mip_file_prefix = "MIP_Head" if is_head else "MIP"
        if not self.__start(generate_vr, vr_file_prefix, self.angle_offset, is_head):
            return RetCode.PLATFORM_GENERATE_VR_ERROR
        if not self.__start(generate_mip, mip_file_prefix, self.angle_offset, is_head):
            return RetCode.PLATFORM_GENERATE_MIP_ERROR
        return None

    @staticmethod
    def destroy():
        if globals()["VTK_IMAGE_DATA"]:
            del globals()["VTK_IMAGE_DATA"]
        log.info("vr & v-mip done")


class JpgConverter:
    DESC_MAPPING = {
        "VR_AP": "USC-UGuard CTA Volume Rendering AP",
        "VR_LR": "USC-UGuard CTA Volume Rendering LR",
        "MIP_AP": "USC-UGuard CTA Volume MIP AP",
        "MIP_LR": "USC-UGuard CTA Volume MIP LR",
        "VR_Head_AP": "USC-UGuard CTA Volume Rendering Head AP",
        "VR_Head_LR": "USC-UGuard CTA Volume Rendering Head LR",
        "MIP_Head_AP": "USC-UGuard CTA Volume MIP Head AP",
        "MIP_Head_LR": "USC-UGuard CTA Volume MIP Head LR",
        "cs_mip": "collateral circulation USC",
    }

    def __init__(self, template_dataset, jpg_dir, save_dir, modality):
        self.template_dataset = template_dataset
        self.jpg_dir = jpg_dir
        self.save_dir = save_dir
        self.modality = modality

    def convert_and_send(self, series_number=None):
        series_uid_dict = {}
        series_number_dict = {}
        for root, dirs, files in os.walk(self.jpg_dir):
            for file in sorted(files):
                if os.path.splitext(file)[1] != '.jpg':
                    log.info("file format error: {}".format(file))
                    continue
                file_type, instance_number = self.__parse_filename(file)
                series_number_dict.setdefault(file_type, series_number)
                series_number_list = series_number_dict.values()
                max_series_number = max(series_number_list)
                original_seris_number = series_number_dict.get(file_type)
                if original_seris_number:
                    series_number = max_series_number + 1
                if file_type not in series_uid_dict:
                    series_uid_dict[file_type] = DicomUtils.generate_uid()
                series_instance_uid = series_uid_dict[file_type]
                series_desc = JpgConverter.DESC_MAPPING[file_type]
                sop_instance_uid = DicomUtils.generate_child_uid(series_instance_uid)
                jpg_path = os.path.join(root, file)
                try:
                    output_dir = os.path.join(self.save_dir, file_type)
                    os.makedirs(output_dir, exist_ok=True)
                    path = os.path.join(output_dir, "{}.dcm".format(instance_number))
                    DicomUtils.jpg2dcm(self.template_dataset, jpg_path, path, series_instance_uid, series_desc,
                                       sop_instance_uid, instance_number, original_seris_number, self.modality)
                    series_uid_dict.setdefault(series_instance_uid, [])
                    series_uid_dict[series_instance_uid].append(path)
                    # upload_status = OrthancApi.upload_image(sop_instance_uid, path)
                    # if not upload_status:
                    #     log.warn("failed to upload {}".format(path))
                    #     return False, None
                except Exception:
                    log.error(traceback.format_exc())
                    return False, None
        return True, series_uid_dict

    @staticmethod
    def __parse_filename(file_name):
        file_name_without_suffix = file_name.split(".")[0]
        split_list = file_name_without_suffix.split("_")
        # file_type = "{}_{}".format(split_list[0], split_list[1])
        file_type = "_".join(split_list[:-1]) if len(split_list) > 2 else file_name_without_suffix
        angle_num = int(split_list[-1]) if str(split_list[-1]).isdigit() else 0
        return file_type, int(angle_num/15)

    def __get_instance_number(self, file_name):
        offset = 1 if file_name.startswith("VR") else 49
        if "AP" in file_name:
            status, num = self.__get_angle(file_name)
            if status:
                return int((num / 15) + 24 + offset)
        if "LR" in file_name:
            status, num = self.__get_angle(file_name)
            if status:
                return int((num / 15) + offset)

    @staticmethod
    def __get_angle(file_name):
        file_name_without_suffix = file_name.split(".")[0]
        angle_num = file_name_without_suffix.split("_")[-1]
        if str(angle_num).isdigit():
            return True, int(angle_num)
        return False, file_name


class Cpr:

    @staticmethod
    def generate_stretched_cpr_json(study_instance_uid, series_instance_uid, vessel_segs):
        image_path = f'/data/ctpdata/cta/{study_instance_uid}/{series_instance_uid}/{vessel_segs}_stretched_cpr_centerline.nii.gz'
        if not os.path.exists(image_path):
            return
        itk_img = sitk.ReadImage(image_path)
        image = sitk.GetArrayFromImage(itk_img)

        result = []
        for image_list in image.tolist():
            column_list = []
            for row in image_list:
                column_list.append(row.index(1))
            result.append(column_list)
        with open(f"/static/cpr/{study_instance_uid}/{series_instance_uid}/{vessel_segs}.json", mode="w") as file:
            file.write(json.dumps(result))


class CsMipHandler:

    def __init__(self, image_path, original_dataset, modality, series_number, output):
        self.__image_path = image_path
        self.__original_dataset = original_dataset
        self.__modality = modality
        self.__series_number = series_number
        self.dcm_output_dir = os.path.join(output, "dcm")
        self.pic_output_dir = os.path.join(output, "pic")

    def start(self):
        self.__generate_dcm()
        self.__generate_png()

    def __generate_dcm(self):
        patient_id = self.__original_dataset.PatientID
        patient_name = self.__original_dataset.PatientName
        study_instance_uid = self.__original_dataset.StudyInstanceUID
        NiiUtils.generate_dcm(self.__image_path, self.dcm_output_dir, patient_id, patient_name, study_instance_uid,
                              self.__modality, self.__series_number, "collateral circulation USC", 800, 300)

    def __generate_png(self):
        file_list = os.listdir(self.dcm_output_dir)
        file_list.sort(key=lambda x: int(x.replace(".dcm", "")))
        os.makedirs(self.pic_output_dir, exist_ok=True)
        for index, filename in enumerate(file_list):
            file_path = os.path.join(self.dcm_output_dir, filename)
            pic_path = os.path.join(self.pic_output_dir, "{}.png".format(index+1))
            DicomUtils.dcm2png(file_path, pic_path)
