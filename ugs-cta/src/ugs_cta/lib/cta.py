#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : cta_interface
@Project : uguard_cta
<AUTHOR> mingxing
@Date    : 2022/9/7 14:11
"""
import time

from uguard_cta_alg.CTA_Interface import *

from lib.logger import <PERSON><PERSON><PERSON><PERSON>, CtaLogger

log = MyLogger()


def calculate(producer, message_template, nii_path, output_dir, results):
    """
    CTA计算

    :param producer: 消息生产者
    :param message_template: 消息模板
    :param nii_path: 原始灰度图像
    :param output_dir: 输出目录
    :param results: 多进程共享字典
    :return:
    """
    start_time = time.time()
    # setting
    log_file = CtaLogger().get_logger()
    cta_setting = dict(logfile=log_file)
    log.info("CTA > setting:{}".format(cta_setting))
    # CTA interface
    cta_interface = CTA_Interface(cta_setting=cta_setting)
    cta_result = cta_interface(producer=producer, content=message_template,
                               _dcm_path=nii_path, outputpath=output_dir, isDcm=False)
    run_time = time.time() - start_time
    log.info("CTA > result:{}".format(cta_result))
    ret_code = cta_result.get("retcode")
    results["code"] = ret_code.code
    results["runTime"] = run_time
    cta_status = cta_result.get("status", False)
    if cta_status:
        results["vesselNii"] = cta_result.get("cta_vessel_nii")
        results["vesselSegsFlag"] = cta_result.get("vessel_segs_flag")
        results["cs_results"] = cta_result.get("cs_results")
        results["vesselHeadNii"] = cta_result.get("cta_vessel_head_nii")
        results["imageQuality"] = cta_result.get("ImageQuality")
