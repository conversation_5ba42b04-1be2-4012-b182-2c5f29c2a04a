#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : const.py
@Project : uguard_cta
<AUTHOR> mingxing
@Date    : 2022/9/7 13:54
"""

import os
from enum import Enum, unique

_env = os.environ


class Consts:
    PACS_TARGET = "target_pacs"
    CODE_REPORT_CTA_VR = "reportCtaVr"
    CODE_REPORT_CTA_MIP = "reportCtaMip"
    CODE_REPORT_CTA_CS = "reportCtaCsMip"
    CODE_REPORT_BACK_METHOD = "reportBackMethod"
    CODE_REPORT_BACK_METHOD_DEFAULT = "orthanc"
    CODE_ALGORITHM_RESULT_MODALITY = "algorithmResultModality"
    DEFAULT_ALGORITHM_RESULT_MODALITY = "OT"
    SUCCESS_CODE = {"图像可能存在造影剂不足": 201}


class Config:
    # Common
    SERVICE_NAME = _env.get("SERVICE_NAME", "ugs-cta")
    SERVICE_VERSION = _env.get("SERVICE_VERSION", "2.1.0rc0")
    # RabbitMQ Configuration
    MQ_HOST = _env.get("MQ_HOST", "ugs-rabbitmq")
    MQ_PORT = _env.get("MQ_PORT", 5673)
    MQ_USERNAME = _env.get("MQ_USERNAME", "unionstrong")
    MQ_PASSWORD = _env.get("MQ_PASSWORD", "UnionStrong@2020")
    # DICOM Configuration`
    PACS_HOST = _env.get("PACS_HOST", "ugs-pacs")
    PACS_AET = _env.get("PACS_AET", "DockerOrthanc")
    PACS_PORT = int(_env.get("PACS_PORT", 4242))
    PACS_DOCKER_PORT = int(_env.get("PACS_DOCKER_PORT", 8042))
    PACS_USERNAME = _env.get("PACS_USERNAME", "unionstrong")
    PACS_PASSWORD = _env.get("PACS_PASSWORD", "UnionStrong@2020")
    LOCAL_AET = _env.get("LOCAL_AET", "UGUARD_ASPECTS")
    # WebApi Configuration
    WEBAPI_HOST = _env.get("WEBAPI_HOST", "ugs-api")
    WEBAPI_PORT = _env.get("WEBAPI_PORT", "4201")
    # Path Configuration
    DIR_ROOT = _env.get("ROOT_DIR", "/data/ctpdata")
    RESULT_DIR = _env.get("RESULT_DIR", "/static")
    DIR_DCM = os.path.join(DIR_ROOT, "dcm")


class RetCode(Enum):
    """返回码枚举类"""

    # 平台（40000~49999）
    PLATFORM_SERIES_NOT_FOUND = (47000, "Series not found")
    PLATFORM_VESSEL_NOT_FOUND = (47001, "Vessel not found")
    PLATFORM_VESSEL_LOAD_FAILED = (47002, "Load vessel error")
    PLATFORM_GENERATE_VR_ERROR = (47003, "Generate VR error")
    PLATFORM_GENERATE_MIP_ERROR = (47004, "Generate MIP error")
    PLATFORM_GENERATE_VR_MIP_ERROR = (47005, "Generate VR/MIP error")
    PLATFORM_UPLOAD_REPORT_ERROR = (47006, "Upload report error")
    PLATFORM_GENERATE_CS_MIP_ERROR = (47007, "Generate/Upload CS MIP error")
    # CTA算法（70000~79999）
    CTA_OK = (0, "Process successfully!")
    CTP_ERROR = (70000, "CTA error!")
    # IO
    LOAD_DATA_FAILED = (70001, "Load data failed!")
    # Segmentation module
    SEGMENTATION_MODULE_FAILED = (70100, "Segmentation module failed!")
    INITIALIZE_FAILED = (70101, 'Initialize failed!')
    LOAD_CONFIG_FAILED = (70102, "Load config file failed!")
    PARSE_CONFIG_FAILED = (70103, "Parse config failed!")
    LOAD_MODEL_FAILED = (70104, "Load model failed")
    PREPROCESS_DATA_FAILED = (70105, "Preprocess data failed!")
    INFERENCE_FAILED = (70106, "Inference failed!")
    RECOVERY_DATA_FAILED = (70108, "Recovery data failed!")
    # KP module
    KP_SERVER_FAILED = (70200, "Key points server failed!")
    KP_CLIENT_FAILED = (70201, "Key points module process failed!")
    VS_MODULE_FAILED = (70280, "Vessel Segs module process failed")
    # CPR module
    CPR_MODULE_FAILED = (70300, "CPR module failed!")
    CPR_SMOOTH_MESH_FAILED = (70301, "CPR smooth surface failed!")
    CPR_EXTRACTION_CENTERLINE_FAILED = (70302, "CPR extract centerline failed!")
    CPR_OPT_CENTERLINE_FAILED = (70303, "CPR resample and optimize centerline failed!")
    CPR_CENTERLINE_RADIUS_FILE_FAILED = (70304, "CPR centerline radius file written failed!")
    CPR_GENERATION_FAILED = (70305, "CPR generation failed!")
    FILE_NOT_EXIST = (70309, "File not exist!")
    UNKNOWN = (79999, "Unknown error!")

    @property
    def code(self):
        """
        获取状态码
        """
        return self.value[0]

    @property
    def msg(self):
        """
        获取状态码信息
        """
        return self.value[1]


@unique
class CameraOrientation(Enum):
    """相机方位枚举"""

    AZIMUTH = "Azimuth"
    ELEVATION = "Elevation"


@unique
class BodyOrientation(Enum):
    """体方位枚举"""

    ANTERIOR = "A"
    POSTERIOR = "P"
    HEAD = "H"
    FEET = "F"
    LEFT = "L"
    RIGHT = "R"
