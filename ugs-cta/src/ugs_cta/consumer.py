#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : consumer
@Project : uguard_cta
<AUTHOR> mingxing
@Date    : 2022/9/7 13:46
"""
from uguard_cta_alg.CTA_Interface import *
import json
import os
import sys
import time
import traceback
import socket

import pika

try:
    import ugs_cta
    sys.path.append(os.path.dirname(ugs_cta.__file__))
except:
    pass

from lib.common import RabbitProducer
from lib.const import Config
from lib.logger import MyLogger
from lib.run import handle_cta
import package_info as pi

log = MyLogger()


def callback(ch, method, properties, body):
    """
    消息监听回调

    :param ch: 回调参数channel
    :param method: 回调参数method
    :param properties: 回调参数properties
    :param body: 消息体
    :return:
    """
    log.info("CTA > receive message: {}".format(body.decode("utf-8")))
    body = json.loads(body)
    study_instance_uid = body.get("studyInstanceUID", "")
    series_instance_uid = body.get("seriesInstanceUID", "")
    algorithm_type = body.get("algorithmType", "")
    callback = body.get('callback', True)
    content = dict(studyInstanceUID=study_instance_uid, seriesInstanceUID=series_instance_uid,
                   algorithmType=algorithm_type, percent=5)
    try:
        RabbitProducer.send(content)
        res = handle_cta(ch, method, study_instance_uid, series_instance_uid, algorithm_type, callback=callback)
        log.info("CTA[study: {}, series: {}] > handle {}".format(study_instance_uid, series_instance_uid, ("successfully" if res else "failed")))
    except:
        log.error(traceback.format_exc())
    finally:
        task_type = body.get("taskType", "1")
        log.info("CTA > task type: {}".format(task_type))
        # 串行
        if task_type == "1":
            # 串行任务，结束后需要告知转发任务着任务已结束
            confirmation = {"is_working": False}
            RabbitProducer.send(confirmation, queue_name=RabbitProducer.ALGORITHM_STATUS)
            log.info("CTA[{}] > completion confirmation: {}".format(series_instance_uid, confirmation))


def main():
    queue_name = "algorithm_cta_task"
    try:
        version_info = dict(version=Config.SERVICE_VERSION, serverType=Config.SERVICE_NAME)
        RabbitProducer.send(version_info)
    except:
        log.error(f"update version error: {traceback.format_exc()}")
    while True:
        try:
            connection = pika.BlockingConnection(
                pika.ConnectionParameters(Config.MQ_HOST, Config.MQ_PORT, "/",
                                          pika.PlainCredentials(Config.MQ_USERNAME, Config.MQ_PASSWORD),
                                          heartbeat=0
                                          ))
            conn_socket = connection._impl.socket
            conn_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            channel = connection.channel()
            channel.queue_declare(queue=queue_name, durable=True)
            channel.basic_consume(callback, queue=queue_name, no_ack=False)
            log.info("waiting for message to exit press CTRL+C, {}:{}({})".format(
                Config.SERVICE_NAME, Config.SERVICE_VERSION, pi.version))
            channel.start_consuming()
        except:
            log.error(f"cta connection error: {traceback.format_exc()}")
        log.info(f"cta connection retry, please check connection")
        time.sleep(10)


if __name__ == "__main__":
    main()
