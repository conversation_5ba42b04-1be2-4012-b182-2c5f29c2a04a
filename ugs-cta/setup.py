#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : setup.py
@Project : ugs-cta
<AUTHOR> mingxing
@Date    : 2023/3/28 16:05
"""
from setuptools import setup, Extension
from setuptools.command.build_py import build_py as build_py_orig
from Cython.Build import cythonize
import src.ugs_cta.package_info as pi


ext_modules = [
    Extension("ugs_cta.*", ["src/ugs_cta/*.py"]),
    Extension("ugs_cta.lib.*", ["src/ugs_cta/lib/*.py"])
]


class BuildPy(build_py_orig):
    def build_packages(self):
        pass


setup(
    name=pi.name,
    author=pi.author,
    url=pi.url,
    cmdclass={"build_py": BuildPy},
    ext_modules=cythonize(ext_modules, language_level=3),
    version=pi.version,
    packages=[pi.name],
    package_dir={"": "src"},
    package_data={pi.name: ["static/font/*.ttf"]},
    platforms=pi.platforms,
    description=pi.description,
    long_description=pi.long_description,
    license="MIT",
    entry_points={
        "console_scripts": [
            "ugscta = ugs_cta.consumer:main"
        ]
    }
)
