version: "3"
services:
  ugs-cta:
    container_name: "ugs-cta"
    image: harbor.unionstrongtech.com/ugs/cta_a4000:2.1.0rc0
    volumes:
      - ./dist:/ugs_cta/dist
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
      - /home/<USER>/upixel-station-backend/server/static:/static
    working_dir: /ugs_cta/
    env_file:
      - .env
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    command: bash -c "pip install --force-reinstall dist/*.whl && ugscta"
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true
