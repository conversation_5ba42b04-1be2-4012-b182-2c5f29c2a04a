absl-py==0.12.0
aiohttp==3.8.1
aiosignal==1.2.0
alabaster==0.7.12
apex==0.1
appdirs==1.4.4
argon2-cffi==20.1.0
ascii-graph==1.5.1
async-generator==1.10
async-timeout==4.0.2
attrs @ file:///tmp/build/80754af9/attrs_1604765588209/work
audioread==2.1.9
Babel==2.9.0
backcall @ file:///home/<USER>/src/ci/backcall_1611930011877/work
batchgenerators==0.23
beautifulsoup4 @ file:///home/<USER>/recipes/ci/beautifulsoup4_1610988766420/work
bleach==3.3.0
blis @ file:///tmp/build/80754af9/cython-blis_1613319335612/work
boto3==1.17.32
botocore==1.20.32
brotlipy==0.7.0
catalogue==1.0.0
certifi==2020.12.5
cffi @ file:///tmp/build/80754af9/cffi_1605538068321/work
chardet @ file:///tmp/build/80754af9/chardet_1605303185383/work
charset-normalizer==2.0.12
click==7.1.2
codecov==2.1.11
conda==4.9.2
conda-build==3.21.4
conda-package-handling @ file:///tmp/build/80754af9/conda-package-handling_1603018141399/work
coverage==5.5
cryptography @ file:///tmp/build/80754af9/cryptography_1605544487601/work
cucim==22.2.1
cupy-cuda110==10.2.0
cupy-cuda112==10.2.0
cxxfilt==0.2.2
cycler==0.10.0
cymem @ file:///tmp/build/80754af9/cymem_1613319259039/work
Cython==0.28.4
DataProperty==0.50.0
decorator @ file:///home/<USER>/src/ci/decorator_1611930055503/work
defusedxml==0.7.1
dicom2nifti==2.3.2
DLLogger @ git+git://github.com/NVIDIA/dllogger.git@26a0f8f1958de2c0c460925ff6102a4d2486d6cc
docutils==0.16
entrypoints==0.3
fastrlock==0.8
filelock @ file:///home/<USER>/recipes/ci/filelock_1610993975404/work
flake8==3.7.9
Flask==1.1.2
frozenlist==1.3.0
future==0.18.2
glob2 @ file:///home/<USER>/recipes/ci/glob2_1610991677669/work
graphsurgeon @ file:///workspace/TensorRT-*******/graphsurgeon/graphsurgeon-0.4.5-py2.py3-none-any.whl
grpcio==1.36.1
h5py==3.2.1
html2text==2020.1.16
hypothesis==4.50.8
idna @ file:///tmp/build/80754af9/idna_1593446292537/work
imageio==2.9.0
imagesize==1.2.0
importlib-metadata @ file:///tmp/build/80754af9/importlib-metadata_1615900442896/work
inflect==5.3.0
iniconfig==1.1.1
ipdb==0.13.7
ipykernel==5.5.0
ipython @ file:///tmp/build/80754af9/ipython_1614615245721/work
ipython-genutils @ file:///tmp/build/80754af9/ipython_genutils_1606773439826/work
itk==5.2.1.post1
itk-core==5.2.1.post1
itk-filtering==5.2.1.post1
itk-io==5.2.1.post1
itk-numerics==5.2.1.post1
itk-registration==5.2.1.post1
itk-segmentation==5.2.1.post1
itsdangerous==1.1.0
jedi==0.17.0
Jinja2 @ file:///tmp/build/80754af9/jinja2_1612213139570/work
jmespath==0.10.0
joblib==1.0.1
json5==0.9.5
jsonschema @ file:///tmp/build/80754af9/jsonschema_1594303806266/work
jupyter-client==6.1.12
jupyter-core==4.7.1
jupyter-tensorboard @ git+https://github.com/cliffwoolley/jupyter_tensorboard.git@ffa7e26138b82549453306e06b535a9ac36db17a
jupyterlab==2.2.9
jupyterlab-pygments==0.1.2
jupyterlab-server==1.2.0
jupytext==1.11.0
kiwisolver==1.3.1
libarchive-c @ file:///home/<USER>/recipes/ci/python-libarchive-c_1610974153025/work
librosa==0.8.0
linecache2==1.0.0
llvmlite==0.35.0
lmdb==1.1.1
Mako==1.1.4
Markdown==3.3.4
markdown-it-py==0.6.2
MarkupSafe==1.1.1
maskrcnn-benchmark @ file:///opt/pytorch/examples/maskrcnn/pytorch
matplotlib==3.3.4
mbstrdecoder==1.0.1
mccabe==0.6.1
mdit-py-plugins==0.2.6
MedPy==0.4.0
mistune==0.8.4
mlperf-compliance==0.0.10
mock @ file:///tmp/build/80754af9/mock_1607622725907/work
msgfy==0.1.0
multidict==6.0.2
murmurhash @ file:///tmp/build/80754af9/murmurhash_1607456108764/work
nbclient==0.5.3
nbconvert==6.0.7
nbformat==5.1.2
nest-asyncio==1.5.1
networkx==2.0
nibabel==3.2.2
nltk==3.5
nnunet==1.7.0
notebook==6.2.0
numba @ file:///tmp/build/80754af9/numba_1614888130619/work
numpy @ file:///tmp/build/80754af9/numpy_and_numpy_base_1603479643886/work
nvidia-dali-cuda110==0.31.0
nvidia-dlprof-pytorch-nvtx @ file:///nvidia/opt/dlprof/bin/nvidia_dlprof_pytorch_nvtx-1.0.0-py3-none-any.whl
nvidia-pyprof @ git+https://github.com/NVIDIA/PyProf@5e3756a779aee4b673adbc614b3a0ac96dc94485
nvidia-tensorboard @ file:///nvidia/opt/tensorboard_install/nvidia_tensorboard-1.15.0%2Bnv21.03-py3-none-any.whl
nvidia-tensorboard-plugin-dlprof @ file:///nvidia/opt/tensorboard_install/nvidia_tensorboard_plugin_dlprof-0.12-py3-none-any.whl
onnx @ file:///opt/pytorch/pytorch/third_party/onnx
onnxruntime==1.7.0
opencv-python==4.5.5.64
packaging==20.9
pandas==1.1.4
pandocfilters==1.4.3
parso @ file:///tmp/build/80754af9/parso_1607623074025/work
pathvalidate==2.3.2
pexpect @ file:///tmp/build/80754af9/pexpect_1605563209008/work
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
Pillow==9.0.1
Pillow-SIMD @ file:///tmp/pillow-simd
pkginfo==1.7.0
plac @ file:///tmp/build/80754af9/plac_1594259967336/work
pluggy==0.13.1
polygraphy==0.25.1
pooch==1.3.0
preshed==3.0.2
prettytable==2.1.0
progressbar==2.5
prometheus-client==0.9.0
prompt-toolkit @ file:///tmp/build/80754af9/prompt-toolkit_1602688806899/work
protobuf==3.15.6
psutil @ file:///tmp/build/80754af9/psutil_1612298023621/work
ptyprocess @ file:///tmp/build/80754af9/ptyprocess_1609355006118/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
py==1.10.0
pybind11==2.6.2
pycocotools @ git+https://github.com/nvidia/cocoapi.git@6ac4a93058202603f36fd1ce47228e7d81119e5a#subdirectory=PythonAPI
pycodestyle==2.5.0
pycosat==0.6.3
pycparser @ file:///tmp/build/80754af9/pycparser_1594388511720/work
pycuda==2020.1
pydicom==2.3.0
pydot==1.4.2
pyflakes==2.1.1
Pygments @ file:///tmp/build/80754af9/pygments_1615143339740/work
pynvml==8.0.4
pyOpenSSL @ file:///tmp/build/80754af9/pyopenssl_1605545627475/work
pyparsing==2.4.7
pyrsistent @ file:///tmp/build/80754af9/pyrsistent_1600141720057/work
PySocks @ file:///tmp/build/80754af9/pysocks_1605305779399/work
pytablewriter==0.47.0
pytest==6.2.2
pytest-cov==2.11.1
pytest-pythonpath==0.7.3
python-dateutil==2.8.1
python-hostlist==1.21
python-nvd3==0.15.0
python-slugify==4.0.1
pytools==2021.2
pytorch-quantization==2.1.0
pytorch-transformers==1.1.0
pytz @ file:///tmp/build/80754af9/pytz_1612215392582/work
PyWavelets==1.1.1
PyYAML==5.4.1
pyzmq==22.0.3
regex==2021.3.17
requests @ file:///tmp/build/80754af9/requests_1592841827918/work
resampy==0.2.2
revtok @ git+git://github.com/jekbradbury/revtok.git@f1998b72a941d1e5f9578a66dc1c20b01913caab
ruamel-yaml==0.15.87
s3transfer==0.3.6
sacrebleu==1.2.10
sacremoses==0.0.35
scikit-image==0.15.0
scikit-learn==0.24.1
scipy @ file:///tmp/build/80754af9/scipy_1614021897168/work
Send2Trash==1.5.0
sentencepiece==0.1.95
SimpleITK==2.1.1
six @ file:///tmp/build/80754af9/six_1605205327372/work
sklearn==0.0
snowballstemmer==2.1.0
SoundFile==0.10.3.post1
soupsieve @ file:///tmp/build/80754af9/soupsieve_1614023247537/work
sox==1.4.1
spacy @ file:///tmp/build/80754af9/spacy_1608321098157/work
Sphinx==3.5.2
sphinx-glpi-theme==0.3
sphinx-rtd-theme==0.5.1
sphinxcontrib-applehelp==1.0.2
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==1.0.3
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.4
srsly @ file:///tmp/build/80754af9/srsly_1607548537638/work
subword-nmt @ git+git://github.com/rsennrich/subword-nmt.git@48ba99e657591c329e0003f0c6e32e493fa959ef
tabledata==1.1.3
tabulate==0.8.9
tensorboard @ file:///nvidia/opt/tensorboard_install/tensorboard-shim
tensorrt @ file:///workspace/TensorRT-*******/python/tensorrt-*******-cp38-none-linux_x86_64.whl
terminado==0.9.3
testpath==0.4.4
text-unidecode==1.3
thinc @ file:///tmp/build/80754af9/thinc_1607710152385/work
threadpoolctl==2.1.0
tifffile==2022.3.25
toml==0.10.2
torch @ file:///opt/pytorch/pytorch
torchtext @ file:///opt/pytorch/text
torchvision @ file:///opt/pytorch/vision
tornado==6.1
tqdm==4.53.0
traceback2==1.4.0
traitlets @ file:///home/<USER>/src/ci/traitlets_1611929699868/work
typepy==1.1.4
typing-extensions==*******
uff @ file:///workspace/TensorRT-*******/uff/uff-0.6.9-py2.py3-none-any.whl
Unidecode==1.2.0
unittest2==1.1.0
urllib3 @ file:///tmp/build/80754af9/urllib3_1603305693037/work
vtk==9.1.0
wasabi @ file:///tmp/build/80754af9/wasabi_1612219178408/work
wcwidth @ file:///tmp/build/80754af9/wcwidth_1593447189090/work
webencodings==0.5.1
Werkzeug==1.0.1
wrapt==1.10.11
wslink==1.4.3
xvfbwrapper==0.2.9
yacs==0.1.8
yarl==1.7.2
zipp @ file:///tmp/build/80754af9/zipp_1615904174917/work
absl-py==0.12.0
aiohttp==3.8.1
aiosignal==1.2.0
alabaster==0.7.12
apex==0.1
appdirs==1.4.4
argon2-cffi==20.1.0
ascii-graph==1.5.1
async-generator==1.10
async-timeout==4.0.2
attrs @ file:///tmp/build/80754af9/attrs_1604765588209/work
audioread==2.1.9
Babel==2.9.0
backcall @ file:///home/<USER>/src/ci/backcall_1611930011877/work
batchgenerators==0.23
beautifulsoup4 @ file:///home/<USER>/recipes/ci/beautifulsoup4_1610988766420/work
bleach==3.3.0
blis @ file:///tmp/build/80754af9/cython-blis_1613319335612/work
boto3==1.17.32
botocore==1.20.32
brotlipy==0.7.0
catalogue==1.0.0
certifi==2020.12.5
cffi @ file:///tmp/build/80754af9/cffi_1605538068321/work
chardet @ file:///tmp/build/80754af9/chardet_1605303185383/work
charset-normalizer==2.0.12
click==7.1.2
codecov==2.1.11
conda==4.9.2
conda-build==3.21.4
conda-package-handling @ file:///tmp/build/80754af9/conda-package-handling_1603018141399/work
coverage==5.5
cryptography @ file:///tmp/build/80754af9/cryptography_1605544487601/work
cucim==22.2.1
cupy-cuda110==10.2.0
cupy-cuda112==10.2.0
cxxfilt==0.2.2
cycler==0.10.0
cymem @ file:///tmp/build/80754af9/cymem_1613319259039/work
Cython==0.28.4
DataProperty==0.50.0
decorator @ file:///home/<USER>/src/ci/decorator_1611930055503/work
defusedxml==0.7.1
dicom2nifti==2.3.2
DLLogger @ git+git://github.com/NVIDIA/dllogger.git@26a0f8f1958de2c0c460925ff6102a4d2486d6cc
docutils==0.16
entrypoints==0.3
fastrlock==0.8
filelock @ file:///home/<USER>/recipes/ci/filelock_1610993975404/work
flake8==3.7.9
Flask==1.1.2
frozenlist==1.3.0
future==0.18.2
glob2 @ file:///home/<USER>/recipes/ci/glob2_1610991677669/work
graphsurgeon @ file:///workspace/TensorRT-*******/graphsurgeon/graphsurgeon-0.4.5-py2.py3-none-any.whl
grpcio==1.36.1
h5py==3.2.1
html2text==2020.1.16
hypothesis==4.50.8
idna @ file:///tmp/build/80754af9/idna_1593446292537/work
imageio==2.9.0
imagesize==1.2.0
importlib-metadata @ file:///tmp/build/80754af9/importlib-metadata_1615900442896/work
inflect==5.3.0
iniconfig==1.1.1
ipdb==0.13.7
ipykernel==5.5.0
ipython @ file:///tmp/build/80754af9/ipython_1614615245721/work
ipython-genutils @ file:///tmp/build/80754af9/ipython_genutils_1606773439826/work
itk==5.2.1.post1
itk-core==5.2.1.post1
itk-filtering==5.2.1.post1
itk-io==5.2.1.post1
itk-numerics==5.2.1.post1
itk-registration==5.2.1.post1
itk-segmentation==5.2.1.post1
itsdangerous==1.1.0
jedi==0.17.0
Jinja2 @ file:///tmp/build/80754af9/jinja2_1612213139570/work
jmespath==0.10.0
joblib==1.0.1
json5==0.9.5
jsonschema @ file:///tmp/build/80754af9/jsonschema_1594303806266/work
jupyter-client==6.1.12
jupyter-core==4.7.1
jupyter-tensorboard @ git+https://github.com/cliffwoolley/jupyter_tensorboard.git@ffa7e26138b82549453306e06b535a9ac36db17a
jupyterlab==2.2.9
jupyterlab-pygments==0.1.2
jupyterlab-server==1.2.0
jupytext==1.11.0
kiwisolver==1.3.1
libarchive-c @ file:///home/<USER>/recipes/ci/python-libarchive-c_1610974153025/work
librosa==0.8.0
linecache2==1.0.0
llvmlite==0.35.0
lmdb==1.1.1
Mako==1.1.4
Markdown==3.3.4
markdown-it-py==0.6.2
MarkupSafe==1.1.1
maskrcnn-benchmark @ file:///opt/pytorch/examples/maskrcnn/pytorch
matplotlib==3.3.4
mbstrdecoder==1.0.1
mccabe==0.6.1
mdit-py-plugins==0.2.6
MedPy==0.4.0
mistune==0.8.4
mlperf-compliance==0.0.10
mock @ file:///tmp/build/80754af9/mock_1607622725907/work
msgfy==0.1.0
multidict==6.0.2
murmurhash @ file:///tmp/build/80754af9/murmurhash_1607456108764/work
nbclient==0.5.3
nbconvert==6.0.7
nbformat==5.1.2
nest-asyncio==1.5.1
networkx==2.0
nibabel==3.2.2
nltk==3.5
nnunet==1.7.0
notebook==6.2.0
numba @ file:///tmp/build/80754af9/numba_1614888130619/work
numpy @ file:///tmp/build/80754af9/numpy_and_numpy_base_1603479643886/work
nvidia-dali-cuda110==0.31.0
nvidia-dlprof-pytorch-nvtx @ file:///nvidia/opt/dlprof/bin/nvidia_dlprof_pytorch_nvtx-1.0.0-py3-none-any.whl
nvidia-pyprof @ git+https://github.com/NVIDIA/PyProf@5e3756a779aee4b673adbc614b3a0ac96dc94485
nvidia-tensorboard @ file:///nvidia/opt/tensorboard_install/nvidia_tensorboard-1.15.0%2Bnv21.03-py3-none-any.whl
nvidia-tensorboard-plugin-dlprof @ file:///nvidia/opt/tensorboard_install/nvidia_tensorboard_plugin_dlprof-0.12-py3-none-any.whl
onnx @ file:///opt/pytorch/pytorch/third_party/onnx
onnxruntime==1.7.0
opencv-python==4.5.5.64
packaging==20.9
pandas==1.1.4
pandocfilters==1.4.3
parso @ file:///tmp/build/80754af9/parso_1607623074025/work
pathvalidate==2.3.2
pexpect @ file:///tmp/build/80754af9/pexpect_1605563209008/work
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
Pillow==9.0.1
Pillow-SIMD @ file:///tmp/pillow-simd
pkginfo==1.7.0
plac @ file:///tmp/build/80754af9/plac_1594259967336/work
pluggy==0.13.1
polygraphy==0.25.1
pooch==1.3.0
preshed==3.0.2
prettytable==2.1.0
progressbar==2.5
prometheus-client==0.9.0
prompt-toolkit @ file:///tmp/build/80754af9/prompt-toolkit_1602688806899/work
protobuf==3.15.6
psutil @ file:///tmp/build/80754af9/psutil_1612298023621/work
ptyprocess @ file:///tmp/build/80754af9/ptyprocess_1609355006118/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
py==1.10.0
pybind11==2.6.2
pycocotools @ git+https://github.com/nvidia/cocoapi.git@6ac4a93058202603f36fd1ce47228e7d81119e5a#subdirectory=PythonAPI
pycodestyle==2.5.0
pycosat==0.6.3
pycparser @ file:///tmp/build/80754af9/pycparser_1594388511720/work
pycuda==2020.1
pydicom==2.3.0
pydot==1.4.2
pyflakes==2.1.1
Pygments @ file:///tmp/build/80754af9/pygments_1615143339740/work
pynvml==8.0.4
pyOpenSSL @ file:///tmp/build/80754af9/pyopenssl_1605545627475/work
pyparsing==2.4.7
pyrsistent @ file:///tmp/build/80754af9/pyrsistent_1600141720057/work
PySocks @ file:///tmp/build/80754af9/pysocks_1605305779399/work
pytablewriter==0.47.0
pytest==6.2.2
pytest-cov==2.11.1
pytest-pythonpath==0.7.3
python-dateutil==2.8.1
python-hostlist==1.21
python-nvd3==0.15.0
python-slugify==4.0.1
pytools==2021.2
pytorch-quantization==2.1.0
pytorch-transformers==1.1.0
pytz @ file:///tmp/build/80754af9/pytz_1612215392582/work
PyWavelets==1.1.1
PyYAML==5.4.1
pyzmq==22.0.3
regex==2021.3.17
requests @ file:///tmp/build/80754af9/requests_1592841827918/work
resampy==0.2.2
revtok @ git+git://github.com/jekbradbury/revtok.git@f1998b72a941d1e5f9578a66dc1c20b01913caab
ruamel-yaml==0.15.87
s3transfer==0.3.6
sacrebleu==1.2.10
sacremoses==0.0.35
scikit-image==0.15.0
scikit-learn==0.24.1
scipy @ file:///tmp/build/80754af9/scipy_1614021897168/work
Send2Trash==1.5.0
sentencepiece==0.1.95
SimpleITK==2.1.1
six @ file:///tmp/build/80754af9/six_1605205327372/work
sklearn==0.0
snowballstemmer==2.1.0
SoundFile==0.10.3.post1
soupsieve @ file:///tmp/build/80754af9/soupsieve_1614023247537/work
sox==1.4.1
spacy @ file:///tmp/build/80754af9/spacy_1608321098157/work
Sphinx==3.5.2
sphinx-glpi-theme==0.3
sphinx-rtd-theme==0.5.1
sphinxcontrib-applehelp==1.0.2
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==1.0.3
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.4
srsly @ file:///tmp/build/80754af9/srsly_1607548537638/work
subword-nmt @ git+git://github.com/rsennrich/subword-nmt.git@48ba99e657591c329e0003f0c6e32e493fa959ef
tabledata==1.1.3
tabulate==0.8.9
tensorboard @ file:///nvidia/opt/tensorboard_install/tensorboard-shim
tensorrt @ file:///workspace/TensorRT-*******/python/tensorrt-*******-cp38-none-linux_x86_64.whl
terminado==0.9.3
testpath==0.4.4
text-unidecode==1.3
thinc @ file:///tmp/build/80754af9/thinc_1607710152385/work
threadpoolctl==2.1.0
tifffile==2022.3.25
toml==0.10.2
torch @ file:///opt/pytorch/pytorch
torchtext @ file:///opt/pytorch/text
torchvision @ file:///opt/pytorch/vision
tornado==6.1
tqdm==4.53.0
traceback2==1.4.0
traitlets @ file:///home/<USER>/src/ci/traitlets_1611929699868/work
typepy==1.1.4
typing-extensions==*******
uff @ file:///workspace/TensorRT-*******/uff/uff-0.6.9-py2.py3-none-any.whl
Unidecode==1.2.0
unittest2==1.1.0
urllib3 @ file:///tmp/build/80754af9/urllib3_1603305693037/work
vtk==9.1.0
wasabi @ file:///tmp/build/80754af9/wasabi_1612219178408/work
wcwidth @ file:///tmp/build/80754af9/wcwidth_1593447189090/work
webencodings==0.5.1
Werkzeug==1.0.1
wrapt==1.10.11
wslink==1.4.3
xvfbwrapper==0.2.9
yacs==0.1.8
yarl==1.7.2
zipp @ file:///tmp/build/80754af9/zipp_1615904174917/work
absl-py==0.12.0
aiohttp==3.8.1
aiosignal==1.2.0
alabaster==0.7.12
apex==0.1
appdirs==1.4.4
argon2-cffi==20.1.0
ascii-graph==1.5.1
async-generator==1.10
async-timeout==4.0.2
attrs @ file:///tmp/build/80754af9/attrs_1604765588209/work
audioread==2.1.9
Babel==2.9.0
backcall @ file:///home/<USER>/src/ci/backcall_1611930011877/work
batchgenerators==0.23
beautifulsoup4 @ file:///home/<USER>/recipes/ci/beautifulsoup4_1610988766420/work
bleach==3.3.0
blis @ file:///tmp/build/80754af9/cython-blis_1613319335612/work
boto3==1.17.32
botocore==1.20.32
brotlipy==0.7.0
catalogue==1.0.0
certifi==2020.12.5
cffi @ file:///tmp/build/80754af9/cffi_1605538068321/work
chardet @ file:///tmp/build/80754af9/chardet_1605303185383/work
charset-normalizer==2.0.12
click==7.1.2
codecov==2.1.11
conda==4.9.2
conda-build==3.21.4
conda-package-handling @ file:///tmp/build/80754af9/conda-package-handling_1603018141399/work
coverage==5.5
cryptography @ file:///tmp/build/80754af9/cryptography_1605544487601/work
cucim==22.2.1
cupy==10.3.0
cupy-cuda112==10.3.0
cxxfilt==0.2.2
cycler==0.10.0
cymem @ file:///tmp/build/80754af9/cymem_1613319259039/work
Cython==0.28.4
DataProperty==0.50.0
decorator @ file:///home/<USER>/src/ci/decorator_1611930055503/work
defusedxml==0.7.1
dicom2nifti==2.3.2
DLLogger @ git+git://github.com/NVIDIA/dllogger.git@26a0f8f1958de2c0c460925ff6102a4d2486d6cc
docutils==0.16
entrypoints==0.3
fastrlock==0.8
filelock @ file:///home/<USER>/recipes/ci/filelock_1610993975404/work
flake8==3.7.9
Flask==1.1.2
frozenlist==1.3.0
future==0.18.2
glob2 @ file:///home/<USER>/recipes/ci/glob2_1610991677669/work
graphsurgeon @ file:///workspace/TensorRT-*******/graphsurgeon/graphsurgeon-0.4.5-py2.py3-none-any.whl
grpcio==1.36.1
h5py==3.2.1
html2text==2020.1.16
hypothesis==4.50.8
idna @ file:///tmp/build/80754af9/idna_1593446292537/work
imageio==2.9.0
imagesize==1.2.0
importlib-metadata @ file:///tmp/build/80754af9/importlib-metadata_1615900442896/work
inflect==5.3.0
iniconfig==1.1.1
ipdb==0.13.7
ipykernel==5.5.0
ipython @ file:///tmp/build/80754af9/ipython_1614615245721/work
ipython-genutils @ file:///tmp/build/80754af9/ipython_genutils_1606773439826/work
itk==5.2.1.post1
itk-core==5.2.1.post1
itk-filtering==5.2.1.post1
itk-io==5.2.1.post1
itk-numerics==5.2.1.post1
itk-registration==5.2.1.post1
itk-segmentation==5.2.1.post1
itsdangerous==1.1.0
jedi==0.17.0
Jinja2 @ file:///tmp/build/80754af9/jinja2_1612213139570/work
jmespath==0.10.0
joblib==1.0.1
json5==0.9.5
jsonschema @ file:///tmp/build/80754af9/jsonschema_1594303806266/work
jupyter-client==6.1.12
jupyter-core==4.7.1
jupyter-tensorboard @ git+https://github.com/cliffwoolley/jupyter_tensorboard.git@ffa7e26138b82549453306e06b535a9ac36db17a
jupyterlab==2.2.9
jupyterlab-pygments==0.1.2
jupyterlab-server==1.2.0
jupytext==1.11.0
kiwisolver==1.3.1
libarchive-c @ file:///home/<USER>/recipes/ci/python-libarchive-c_1610974153025/work
librosa==0.8.0
linecache2==1.0.0
llvmlite==0.35.0
lmdb==1.1.1
Mako==1.1.4
Markdown==3.3.4
markdown-it-py==0.6.2
MarkupSafe==1.1.1
maskrcnn-benchmark @ file:///opt/pytorch/examples/maskrcnn/pytorch
matplotlib==3.3.4
mbstrdecoder==1.0.1
mccabe==0.6.1
mdit-py-plugins==0.2.6
MedPy==0.4.0
mistune==0.8.4
mlperf-compliance==0.0.10
mock @ file:///tmp/build/80754af9/mock_1607622725907/work
msgfy==0.1.0
multidict==6.0.2
murmurhash @ file:///tmp/build/80754af9/murmurhash_1607456108764/work
nbclient==0.5.3
nbconvert==6.0.7
nbformat==5.1.2
nest-asyncio==1.5.1
networkx==2.0
nibabel==3.2.2
nltk==3.5
nnunet==1.7.0
notebook==6.2.0
numba @ file:///tmp/build/80754af9/numba_1614888130619/work
numpy @ file:///tmp/build/80754af9/numpy_and_numpy_base_1603479643886/work
nvidia-dali-cuda110==0.31.0
nvidia-dlprof-pytorch-nvtx @ file:///nvidia/opt/dlprof/bin/nvidia_dlprof_pytorch_nvtx-1.0.0-py3-none-any.whl
nvidia-pyprof @ git+https://github.com/NVIDIA/PyProf@5e3756a779aee4b673adbc614b3a0ac96dc94485
nvidia-tensorboard @ file:///nvidia/opt/tensorboard_install/nvidia_tensorboard-1.15.0%2Bnv21.03-py3-none-any.whl
nvidia-tensorboard-plugin-dlprof @ file:///nvidia/opt/tensorboard_install/nvidia_tensorboard_plugin_dlprof-0.12-py3-none-any.whl
onnx @ file:///opt/pytorch/pytorch/third_party/onnx
onnxruntime==1.7.0
opencv-python==4.5.5.64
packaging==20.9
pandas==1.1.4
pandocfilters==1.4.3
parso @ file:///tmp/build/80754af9/parso_1607623074025/work
pathvalidate==2.3.2
pexpect @ file:///tmp/build/80754af9/pexpect_1605563209008/work
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
pika==0.12.0
Pillow==9.0.1
Pillow-SIMD @ file:///tmp/pillow-simd
pkginfo==1.7.0
plac @ file:///tmp/build/80754af9/plac_1594259967336/work
pluggy==0.13.1
polygraphy==0.25.1
pooch==1.3.0
preshed==3.0.2
prettytable==2.1.0
progressbar==2.5
prometheus-client==0.9.0
prompt-toolkit @ file:///tmp/build/80754af9/prompt-toolkit_1602688806899/work
protobuf==3.15.6
psutil @ file:///tmp/build/80754af9/psutil_1612298023621/work
ptyprocess @ file:///tmp/build/80754af9/ptyprocess_1609355006118/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
py==1.10.0
pybind11==2.6.2
pycocotools @ git+https://github.com/nvidia/cocoapi.git@6ac4a93058202603f36fd1ce47228e7d81119e5a#subdirectory=PythonAPI
pycodestyle==2.5.0
pycosat==0.6.3
pycparser @ file:///tmp/build/80754af9/pycparser_1594388511720/work
pycuda==2020.1
pydicom==2.3.0
pydot==1.4.2
pyflakes==2.1.1
Pygments @ file:///tmp/build/80754af9/pygments_1615143339740/work
PyMySQL==1.0.2
pynetdicom==2.0.1
pynvml==8.0.4
pyOpenSSL @ file:///tmp/build/80754af9/pyopenssl_1605545627475/work
pyparsing==2.4.7
pyrsistent @ file:///tmp/build/80754af9/pyrsistent_1600141720057/work
PySocks @ file:///tmp/build/80754af9/pysocks_1605305779399/work
pytablewriter==0.47.0
pytest==6.2.2
pytest-cov==2.11.1
pytest-pythonpath==0.7.3
python-dateutil==2.8.1
python-hostlist==1.21
python-nvd3==0.15.0
python-slugify==4.0.1
pytools==2021.2
pytorch-quantization==2.1.0
pytorch-transformers==1.1.0
pytz @ file:///tmp/build/80754af9/pytz_1612215392582/work
PyWavelets==1.1.1
PyYAML==5.4.1
pyzmq==22.0.3
regex==2021.3.17
requests @ file:///tmp/build/80754af9/requests_1592841827918/work
resampy==0.2.2
revtok @ git+git://github.com/jekbradbury/revtok.git@f1998b72a941d1e5f9578a66dc1c20b01913caab
ruamel-yaml==0.15.87
s3transfer==0.3.6
sacrebleu==1.2.10
sacremoses==0.0.35
scikit-image==0.15.0
scikit-learn==0.24.1
scipy @ file:///tmp/build/80754af9/scipy_1614021897168/work
Send2Trash==1.5.0
sentencepiece==0.1.95
SimpleITK==2.1.1
six @ file:///tmp/build/80754af9/six_1605205327372/work
sklearn==0.0
snowballstemmer==2.1.0
SoundFile==0.10.3.post1
soupsieve @ file:///tmp/build/80754af9/soupsieve_1614023247537/work
sox==1.4.1
spacy @ file:///tmp/build/80754af9/spacy_1608321098157/work
Sphinx==3.5.2
sphinx-glpi-theme==0.3
sphinx-rtd-theme==0.5.1
sphinxcontrib-applehelp==1.0.2
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==1.0.3
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.4
srsly @ file:///tmp/build/80754af9/srsly_1607548537638/work
subword-nmt @ git+git://github.com/rsennrich/subword-nmt.git@48ba99e657591c329e0003f0c6e32e493fa959ef
tabledata==1.1.3
tabulate==0.8.9
tensorboard @ file:///nvidia/opt/tensorboard_install/tensorboard-shim
tensorrt @ file:///workspace/TensorRT-*******/python/tensorrt-*******-cp38-none-linux_x86_64.whl
terminado==0.9.3
testpath==0.4.4
text-unidecode==1.3
thinc @ file:///tmp/build/80754af9/thinc_1607710152385/work
threadpoolctl==2.1.0
tifffile==2022.3.25
toml==0.10.2
torch @ file:///opt/pytorch/pytorch
torchtext @ file:///opt/pytorch/text
torchvision @ file:///opt/pytorch/vision
tornado==6.1
tqdm==4.53.0
traceback2==1.4.0
traitlets @ file:///home/<USER>/src/ci/traitlets_1611929699868/work
typepy==1.1.4
typing-extensions==*******
uff @ file:///workspace/TensorRT-*******/uff/uff-0.6.9-py2.py3-none-any.whl
Unidecode==1.2.0
unittest2==1.1.0
urllib3 @ file:///tmp/build/80754af9/urllib3_1603305693037/work
vtk==9.1.0
wasabi @ file:///tmp/build/80754af9/wasabi_1612219178408/work
wcwidth @ file:///tmp/build/80754af9/wcwidth_1593447189090/work
webencodings==0.5.1
Werkzeug==1.0.1
wrapt==1.10.11
wslink==1.4.3
xvfbwrapper==0.2.9
yacs==0.1.8
yarl==1.7.2
zipp @ file:///tmp/build/80754af9/zipp_1615904174917/work
