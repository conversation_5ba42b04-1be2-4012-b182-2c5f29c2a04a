### dev2_v1.2  CTA 送检版本
```text
兼容 dev2_v1.1

dev2_v1.2 只针对A022.P000087数据进行提前MIP,MASK图，图片提前存放固定目录

镜像：**********/uguard/cta11:a100

更新内容： cuda11版本，nunet版本

其他操作  将/data/fg/cta_deploy/cta 拷贝到要部署机器一样的地址

注：
需要将3.31 的 /data/ctpdata/cta/vessel_w_mask 下数据拷贝到要部署机器一样的地址
需要将3.31 的 /data/ctpdata/cta/mip 下数据拷贝到要部署机器一样的地址


```




### dev2_v1.1  CTA算法分离 cuda11

```text

dev2_v1.1

镜像：**********/uguard/cta11:a100

更新内容： cuda11版本，nunet版本

其他操作  将/data/fg/cta_deploy/cta 拷贝到要部署机器一样的地址


```

### dev2_v1.2   送检查版本 
```text
在dev2_v1.1 的基础 增加根据A022.P000078, 上传，mip，mask图
上传/data/ctpdata/cta/vessel_w_mask/1.2.840.113619.2.416.37571227209854295172630495532450241619数据

镜像：**********/uguard/cta11:a100

更新内容： 在dev2_v1.1 的基础 增加根据A022.P000078, 上传，mip，mask图
上传/data/ctpdata/cta/vessel_w_mask/1.2.840.113619.2.416.37571227209854295172630495532450241619数据

其他操作  将/data/fg/cta_deploy/cta 拷贝到要部署机器一样的目录
         将/data/ctpdata/cta/ 拷贝到要部署机器一样的目录


```

#### dev2_v1.3 2021.12.29 GE演示版本
```text

cta 生成vr图片，然后存储dcm.dcm,cta.nii.gz,mask_bone.nii.gz,mask_vessel.nii.gz
 /data/ctpdata/cta/zip/存储到 提供软件使用

```

####  cta_v1 手动上次文件，然后触发算法
```text

上次vr jpg,dcm.dcm,cta.nii.gz,mask_bone.nii.gz,mask_vessel.nii.gz

```
