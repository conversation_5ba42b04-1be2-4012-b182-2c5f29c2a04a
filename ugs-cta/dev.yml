version: "3"
services:
  ugs-cta:
    container_name: "ugs-cta"
    image: harbor.unionstrongtech.com/ugs/cta_a4000:2.1.0rc0
    volumes:
      - ./src/ugs_cta:/ugs_cta
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
      - /home/<USER>/upixel-station-backend/server/static:/static
    working_dir: /ugs_cta/
    env_file:
      - .env
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    command: python3 -u consumer.py
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true
