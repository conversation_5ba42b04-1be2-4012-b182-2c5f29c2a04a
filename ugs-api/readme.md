## uguard 服务器版本后端功能说明

### 1.algorithm模块：算法模块

> 主要是急性脑缺血aspect和ctp算法结果文字相关保存记录

### 2.async模块：医学图像获取，数据查询模块

> 主要是使用orthanc医学图像数据库提供查询数据，下载，上传，以及被动接收自动推送算法任务， 清除数据，调试，设置，服务重启， aet信息设置等相关功能

### 3.common模块：通用基础封装的库

> 数据库连接，后端登录token认证等方法

### 4.user模块：工作站授权码模块

> 主要是设置工作站授权码配置

### 5 static模块：静态文件存放位置

### dev2_v1.2.1

```text
        1.代码拉取版本地址：git clone http://172.16.1.27:8443/git/caobo/upixel-station-backend.git-b dev2_v1.2.1
        2.版本更新内容：
        后端主要更新内容：
        1.增加查询是否回传报告接口
        2.增加设置是否回传报告接口
        3.增加按ctp/aspects清除报告接口
        前端更新内容：
        1.增加设置是否回传功能
        2.增加ctp/aspects清除报告功能包
        增加 antspy == 0.27
        包路径\\***********\incoming\jinhailan\for_caobo
        算法
        1更新了aspects算法v1.1版本
```
### dev2_v1.2.2
```text
  
增加ge对接接口

1./grand 资源使用

2./grand-time 轮询接口

3. transport_algorithm.py里增加回调方法： 处理中状态

/series/updateProcessingStatus

4. ctp_aspects.clinet.py 里增加回调GE方法： 处理成功，处理失败

/series/updateProcessingStatus

ctp_aspects.clinet.py 里增加回调GE返回url地址方法：
/third-party/saveOrUpdateUrl
   
```

### dev2_v1.2.3
```text
增加ge对接接口

1./grand 资源使用

2./grand-time 轮询接口

3. transport_algorithm.py里增加回调方法： 处理中状态

4. ctp_aspects.clinet.py 里增加回调GE方法： 处理成功，处理失败，返回保存url

统一回调ge接口

/third-party/storageData"

配置文件修改：

.env 和 .env_algorithem 需要加一下配置

# GE配置

GE_CALLBACK_HOST=edison-ecm-service.default

GE_CALLBACK_PORT=8020

GE_CALLBACK_THIRD_PARTY_ID=2

# 是否开启GE回调 0不开启,1开启

GE_CALLBACK_START=1

# 本地web服务配置 返回GE保存url用

LOCAL_WEB_SERVER_HOST=***********

LOCAL_WEB_SERVER_PORT=4224

transport_algorithm增加/data/ctpdata得挂载

container_name: "transport_algorithm"
image: **********/library/cloud_python_dev:tensorflow1.5_workstation
volumes:

./algorithm_server:/algorithm_server
/usr/share/zoneinfo:/usr/share/zoneinfo
${DATA_VOLUME}data/ctpdata:/data/ctpdata

### 2021/8/10 dev2_V1.2.3更新
1. 新增GE调用平台删除数据接口，日志单独输出
2. 算法触发GE回调接口新增参数，series_Number, bodypartExamined，callbackUrl 参数返回

```


### dev2_v1.2.4

> 增加 antspy == 0.27
> 
> 包路径\\***********\incoming\jinhailan\for_caobo
> 
> 算法 :1更新了aspects算法v1.1版本

### dev2_v1.2.5 增加算法cta
> 1.安装cta依赖 file://***********/incoming/fanggang/CTA_debone/deploy/env/abstract_requirements.txt
> 
> 2.安装包 file://***********/incoming/fanggang/CTA_debone/deploy/env/vtk-9.0.1-cp36-cp36m-manylinux2010_x86_64.whl
> 
> 3.安装包file://***********/incoming/fanggang/CTA_debone/deploy/env/tensorflow_gpu-2.3.0-cp36-cp36m-manylinux2010_x86_64.whl
>
>4. 安装vxfb
> 
> cta版本1.0.
> 

### dev2_v1.2.6  更新aspects算法v1.3
```text
更新内容
aspects算法v1.3
env_algorithem  增加aspects参数

镜像增加vtk包
pip3 install vtk==9.0.1


当前版本
aspects v1.3
cta v1.0 已注释
ctp 以前版本
前端 v2.0.5 
在v2.0.4无ge的logo基础上修改了ctp左右脑显示问题
后端 v1.2.6


aspects v1.3更新内容

run.py is the interface script
调用get_report()获取result
report = result.get('report')
n_layer_template、up_layer_template、template_path、thre_low、thre_high、margin、
roi_thre、roi这8个参数建议可以从配置文件里读取。
template_path在git上的路径为/template

实验验证 

多中心数据，共1264个患者的2132个序列，V1.3相对于V1.2，在665/2132例上有提升。
 
优化基于的假设

1.V1.0给出分割的confidence大于等于0.8的数据，分割可以接受。
2.V1.0给出分割的confidence小于0.3的数据，通过刚性配准可以选出相对合理的核团层与核团上层，且柔性配准的分区合理性高于神经网络做分割。
3.V1.0给出分割的confidence在0.3到0.8之间的数据，若刚性配准之后神经网络分割的confidence+bargin大于V1.0给出分割的confidence，则使用刚性配准之后的结果，否则采用原结果。

优化策略 

1.V1.0分割的confidence大于等于0.8的数据，出V1.0（神经网络对原图像做选层与分区）的报告。
2.V1.0分割的confidence小于0.3的数据，通过刚性配准确定核团层与核团上层，通过柔性配准确定分区，给出报告。
3.V1.0分割的confidence大于等于0.3小于0.8的数据，通过刚性配准确定核团层与核团上层，对核团层与核团上层使用神经网络进行分区，并计算confidence_after_rigid_

```

### dev2_v1.2.7  A100上给GE演示版本

```text

aspects  版本 intergrate_ich_report
在v1.3基础上 增加出血demo  只针对A008.P000009.D02.S003 , A008.P000053.D02.S004,A0008.P000408.D01.S002进行出血处理
 出血测试数据 A008.P000009.D02.S003 , A008.P000053.D02.S004,A0008.P000408.D01.S002
 
 
 CTA   cta_nn v1.1
 使用cta_nn  v1.1 使用了nnUnet版本 ，镜像使用 **********/library/cta:2.2
 需要把 31上/data/cta 目录拷贝到部署机器的 /data/cta目录下 含有cta模型
 
 测试数据 A022.P000002，MAOJUNFANG
 演示数据 A022.P000086，A022.P000078, GE PEI EN
 
 CTP 
 版本：
 镜像使用： **********/library/ctp_v1
 增加了 对薄曾ctp处理cta
 需要把/mipf-env/bin 目录下 拷贝到部署机器的 /mipf-env/bin 下
 
 测试提取cta数据 A001.P000088
 
 
 
 前端 v2.0.5 
在v2.0.4无ge的logo基础上修改了ctp左右脑显示问题

后端 v1.2.7
 
 
 算法启动使用 docker-compose-algorithm-ctann.yml 启动
 
 演示环境使用A100环境
cta 使用镜像 **********/library/cta:a100
 aspects  修改aspect_util 的77行os.environ["CUDA_VISIBLE_DEVICES"] = "" 使用cpu
 ctp 需要编译cuda11版本 **********/library/algorithm_cuda11_1:ctp_v1
```

### dev2_v1.2.8  算法与server分离

```text
upixel-station-backend 内只启动 docker-compose.yml
启动服务如下，不启动任何算法服务
1、uguard server
2、nginx
3、mongodb
4、cloud_platform_ctp_aspect_consumer
5、pacs
6、redis
7、transport_algorithm

```

### dev2_v1.3.0  送检版本 
```text
upixel-station-backend 内只启动 docker-compose.yml 
启动服务如下，不启动任何算法服务
1、cloud_platform_api: **********/library/cloud_python_dev:tensorflow1.5_workstation
2、nginx  :nginx_cloud_local_platform
3、mongodb
4、cloud_platform_ctp_aspect_consumer
5、pacs
6、redis
7、transport_algorithm

更新内容：1.增加邮箱功能
        2.增加cta双侧图像显示及左侧分类
        
更新其他操作，需要再cloud库增加表mail_config,mail_history,mail_auto,algorithm_type,process_unusual_data,version表
```
### dev2_v1.3.1 GE-Edison版本
```text
算法 aspects dev2_v1.3
    ctp dev2_v1.1
    无cta
 
本次server更新内容
 修改ge 回调接口

```
