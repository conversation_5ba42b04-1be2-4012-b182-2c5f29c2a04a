#! /usr/bin/env python
# encoding: utf8

from django.urls import include, path

from server.api.views import *

urlpatterns = [
    path('', include('server.user.urls')),
    path('algorithm/', include('server.algorithm.urls')),
    path('async/', include('server.async.urls')),
    path('versions/', VersionView.as_view()),
    path('mail/', include('server.mails.urls')),
    path('studies/', include('server.study.urls')),
    path('series/', include('server.series.urls')),
    path('images/', include('server.image.urls')),
    path('report/', include('server.report.urls')),
    path('systemsettings', include('server.systemconfig.urls')),
    path('user/', include('server.user.urls'))
]
