from django.http import JsonResponse
from server.common.views import BaseView
from server.mails.models import VersionModel

# from server.settings import API_VERSION
# Create your views here.
# 脑缺血
# 生成图像文件：dic文件说类型中的2d图像
# 1.返回生成2d分区图像资料和图像对应的图像index
# 2.记录aspect评分


class VersionView(BaseView):
    """
    api 版本号
    """

    def get(self, request):
        API_VERSION = ""
        try:
            queryset = VersionModel.objects.filter(is_delete=False)
            if queryset:
                for i in queryset:
                    API_VERSION += i.server_type + ":"+i.version+" "
        except Exception as e:
            print("select versionModel is error,", str(e))
            pass
        response = self.response
        response['status'] = True
        response['code'] = 200
        response['data'] = {'versions': API_VERSION}
        response['message'] = '获取成功'
        return JsonResponse(response)
