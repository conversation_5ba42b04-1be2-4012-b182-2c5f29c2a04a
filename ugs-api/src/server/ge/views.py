import importlib

import requests
from django.http import JsonResponse

from server import settings
from server.algorithm.models import AlgorithmTask, AlgorithmResult
from server.common.base import *
from server.common.views import BaseView
import logging
import traceback
import os
import shutil

import uuid

async_util_module = importlib.import_module('server.async.utils')
async_view_module = importlib.import_module('server.async.views')
image_models_module = importlib.import_module('server.image.models')
orthanc_c_move = async_util_module.orthanc_c_move
CallBackDICOM = image_models_module.CallBackDICOM
find_orthanc_resource_by_type = async_util_module.find_orthanc_resource_by_type
delete_orthanc_resources_by_series = async_util_module.delete_orthanc_resources_by_series
from server.common.mongoConnector import MongoDB
from bson.objectid import ObjectId
import threading

log = logging.getLogger("django")


# Create your views here.

class GEInterfaceGrantView(BaseView):
    def post(self, request):
        data = request.POST
        response = dict()
        response['code'] = 500
        unique_instance_id = data.get("unique-instance-id", None)
        request_id = data.get("request-id", None)
        if not unique_instance_id or not request_id:
            return JsonResponse(response)
        # 解析stud_uid和series_uid
        instance = unique_instance_id.split(",")
        if not instance or len(instance) != 2:
            return JsonResponse(response)
        study_uid, series_uid = instance
        if not study_uid or not series_uid:
            return JsonResponse(response)
        #  c_move
        query_type = data.get("query_type", "SERIES")
        # patient_info = data.get("patient_info", {})
        # query_type="STUDY"
        patient_info = {
            "StudyInstanceUID": study_uid,
            "SeriesInstanceUID": series_uid
        }
        status, return_data = orthanc_c_move(patient_info=patient_info,
                                             querylevel=query_type,
                                             target_modality="targetpacs",
                                             accept_aet=settings.ORTHANC_AET
                                             )
        if not status:
            response["code"] = 400
            return JsonResponse(response)
        response["code"] = 200
        return JsonResponse(response)

    @staticmethod
    def callback_request(body):
        """
        {
          "processingStatus": "1",
          "seriesUid": "********.1107.5.12.7.2361.30000014123100425632800000037"
        }

        参数说明：
            processingStatus:序列处理状态[0:处理中, 1:处理完成, 2:处理失败]
            seriesUid:序列Uid

        Response body

        {
          "status": "200",
          "data": 1
            }

        """
        host = settings.GE_CALLBACK_HOST
        port = settings.GE_CALLBACK_PORT
        print("request to GE ", body)
        url = "http://" + host + ":" + port + "/series/updateProcessingStatus"
        headers = {"Content-Type": "application/json"}
        response = requests.post(url, body=body, headers=headers)
        print(response)


class GEInterfaceTimeoutView(BaseView):
    def post(self, request):
        data = request.POST
        response = dict()
        response['code'] = 200
        unique_instance_id = data.get("unique-instance-id", None)
        # request_id = data.get("request-id", None)
        if not unique_instance_id:
            return JsonResponse(response)
        # 解析stud_uid和series_uid
        instance = unique_instance_id.split(",")
        if not instance or len(instance) != 2:
            return JsonResponse(response)
        study_uid, series_uid = instance
        if not study_uid or not series_uid:
            return JsonResponse(response)
        queryset = AlgorithmTask.objects.filter(series_uid=series_uid)
        if queryset:
            task = queryset.first()
            finish_percent = str(task.finish_percent)
            if finish_percent.isdigit():
                percent = int(finish_percent)
                if percent < 100:
                    response['code'] = 202
                    return JsonResponse(response)
                else:
                    response['code'] = 200
                    return JsonResponse(response)
        return JsonResponse(response)


class GEInterfaceInterruptView(BaseView):
    def post(self, request):
        data = request.POST
        response = dict()
        response['code'] = 200
        return JsonResponse(response)


class DeleteStorageDicomView(BaseView):
    """
    {
             "mainDicomTags":
            {
                  "ProtocolName": "CCTA",
                  "SeriesInstanceUID": "1.2.276.0.7230010.3.1.3.0.29114.**********.656955", //如果此字段有值，则以此序列值作为删除条件
                  "ContrastBolusAgent": "",
                  "StationName": "awpc_16007351",
                  "ImageOrientationPatient": "1\\0\\0\\0\\1\\0",
                  "ImagesInAcquisition": "280",
                  "SeriesDate": "20191217",
                  "Manufacturer": "GE MEDICAL SYSTEMS",
                  "OperatorsName": "",
                  "SeriesNumber": "5000",
                  "BodyPartExamined": "HEART",
                  "Modality": "CT",
                  "SeriesTime": "101020"
            },
            "endDate": "2021-08-02 13:41:56", //endDate如果有值，则为删除dicom文件截止日期，在此之前的dicom文件须删除
            "asynchronous": true, // 同步和异步删除，如果为true则需立即给出相应，如果为false则一直等待相应
            "timeout": 60 //timeout时间 默认60s
    }

    """

    @staticmethod
    def check_field(data, request_id):
        mainDicomTags = data.get("mainDicomTags", None)
        if not isinstance(mainDicomTags, dict):
            if mainDicomTags is None:
                return False, dict(code=400,
                                   msg=f"mainDicomTags field is not blank---{mainDicomTags}--",
                                   request_id=request_id)
            else:
                return False, dict(code=400,
                                   msg=f"mainDicomTags field must be json format---{mainDicomTags}--",
                                   request_id=request_id)
        StudyInstanceUID = mainDicomTags.get("StudyInstanceUID", "")
        SeriesInstanceUID = mainDicomTags.get("SeriesInstanceUID", "")
        asynchronous = data.get("asynchronous", True)
        if not isinstance(asynchronous, bool):
            return False, dict(code=400,
                               msg=f"asynchronous field must be boolean format---{asynchronous}--",
                               request_id=request_id)
        timeout = data.get("timeout", 60)
        if not isinstance(timeout, int):
            return False, dict(code=400,
                               msg=f"timeout field must be integer format---{timeout}--",
                               request_id=request_id)
        endDate = data.get("endDate", None)
        if endDate:
            if not isinstance(endDate, str):
                return False, dict(code=400,
                                   msg=f"endDate field must be string format---{endDate}--",
                                   request_id=request_id)
            try:
                str_to_date = datetime.datetime.strptime(endDate, "%Y-%m-%d %H:%M:%S")
                dead_line = str_to_date + datetime.timedelta(days=1)
                SeriesDate = dead_line.strftime("%Y%m%d")
            except Exception:
                msg = dict(code=500,
                           msg=f"Failed to convert endDate field string  to datetime format ---{endDate}--",
                           request_id=request_id)
                log.error(f"request_id:{request_id}-"
                          f"--{str(msg)}-"
                          f"-{str(traceback.format_exc())}")
                return False, dict(code=500,
                                   msg=f"Failed to convert endDate field string  to datetime format ---{endDate}--",
                                   request_id=request_id)

        if SeriesInstanceUID:
            if not StudyInstanceUID:
                return False, dict(code=400,
                                   msg=f"use SeriesInstanceUID field must StudyInstanceUID "
                                       f"field is not blank---{StudyInstanceUID}",
                                   request_id=request_id)
            return True, dict(StudyInstanceUID=StudyInstanceUID,
                              SeriesInstanceUID=SeriesInstanceUID,
                              type="series")
        else:
            if not endDate:
                return False, dict(code=400,
                                   msg=f"SeriesInstanceUID field and endDate field are both blank",
                                   request_id=request_id)
            return True, dict(SeriesDate=SeriesDate,
                              type="date")

    @staticmethod
    def query_seriesdate_for_series_orthanc_uuid(series_date, request_id):
        url = F"http://{settings.ORTHANC_HOST}:{str(settings.ORTHANC_WEB_PORT)}/tools/find"
        data = {
            "Level": "SERIES",
            "Query": {
                "SeriesDate": f"-{series_date}"
            }
        }
        try:
            response = requests.post(url=url, json=data,
                                     auth=(settings.ORTHANC_WADO_USERNAME,
                                           settings.ORTHANC_WADO_PASSWORD),
                                     timeout=1)
            response_data = response.json()
            return True, response_data
        except Exception:
            msg = dict(code=500,
                       msg="query endDate error",
                       request_id=request_id)
            log.error(f"request_id:{request_id}-"
                      f"--{str(msg)}-"
                      f"--endDate_query--{str(traceback.format_exc())}")
            return False, msg

    def excute_delete_for_enddate(self, data_for_dict, request_id):
        SeriesDate = data_for_dict.get("SeriesDate", "")
        if SeriesDate:
            query_status, date_data = self.query_seriesdate_for_series_orthanc_uuid(series_date=SeriesDate,
                                                                                    request_id=request_id)
            if not query_status:
                log.warning(f"request_id:{request_id}---request_data:{str(date_data)}")
                return False, date_data
            if not date_data:
                msg = dict(code=404,
                           msg="query endDate orthanc not resource",
                           request_id=request_id)
                log.warning(f"request_id:{request_id}---request_data:{str(msg)}")
                return False, msg
            _callback_queryset = []
            # _callback_queryset = StudyViewModel.objects.filter(series_orthanc_uuid__in=date_data)
            if not _callback_queryset:
                msg = dict(code=404,
                           msg="query endDate database not resource",
                           request_id=request_id)
                log.warning(f"request_id:{request_id}---request_data:{str(msg)}")
                return False, msg
            del_record = []
            try:
                for i in _callback_queryset:
                    if i.algorithm_type:
                        del_record.append(f"{i.study_instance_uid}--{i.series_instance_uid}--{i.algorithm_type}")
                        self.delete_data(study_instance_id=i.study_instance_uid,
                                         algorithm_type=i.algorithm_type,
                                         series_instance_uid=i.series_instance_uid)
                    else:
                        algorithm_type = "force"
                        del_record.append(f"{i.study_instance_uid}--{i.series_instance_uid}--{i.algorithm_type}")
                        self.delete_data(study_instance_id=i.study_instance_uid,
                                         algorithm_type=algorithm_type,
                                         series_instance_uid=i.series_instance_uid)
            except Exception:
                msg = dict(code=500,
                           msg="failed to delete",
                           request_id=request_id)
                log.error(f"request_id:{request_id}-"
                          f"--{str(msg)}-"
                          f"--SeriesInstanceUID_delete--{str(traceback.format_exc())}")
                return False, msg
            _str_del = "\n".join(del_record)
            msg = dict(code=200,
                       msg="delete enddate data success please check",
                       request_id=request_id)
            log.info(f"request_id:{request_id}---request_data:{str(msg)}--\t {_str_del}")
            return True, dict(code=200,
                              msg="delete enddate data success please check",
                              request_id=request_id)

    def excute_delete_for_series(self, data_for_dict, request_id):
        try:
            pass
            # StudyInstanceUID = data_for_dict.get("StudyInstanceUID", None)
            # SeriesInstanceUID = data_for_dict.get("SeriesInstanceUID", None)
            # _callback_queryset = StudyViewModel.objects.filter(study_instance_uid=StudyInstanceUID,
            #                                                    series_instance_uid=SeriesInstanceUID)
            # if not _callback_queryset:
            #     msg = dict(code=404,
            #                msg="query series not resource",
            #                request_id=request_id)
            #     log.warning(f"request_id:{request_id}---request_data:{str(msg)}")
            #     return False, dict(code=404,
            #                        msg="query series not resource",
            #                        request_id=request_id)
            # algorithm_type = _callback_queryset.first().algorithm_type
            # if not algorithm_type:
            #     algorithm_type = "force"
            # self.delete_data(study_instance_id=StudyInstanceUID,
            #                  algorithm_type=algorithm_type,
            #                  series_instance_uid=SeriesInstanceUID)
        except Exception:
            msg = dict(code=500,
                       msg="failed to delete",
                       request_id=request_id)
            log.error(f"request_id:{request_id}-"
                      f"--{str(msg)}-"
                      f"--SeriesInstanceUID_delete--{str(traceback.format_exc())}")
            return False, msg
        msg = dict(code=200,
                   msg="delete success please check",
                   request_id=request_id)
        log.info(f"request_id:{request_id}---request_data:{str(msg)}")
        return True, msg

    @staticmethod
    def delete_data(study_instance_id, algorithm_type, series_instance_uid):
        clear_series_list = [series_instance_uid]
        # for i in _callback_queryset:
        #     if i.series_instance_uid not in clear_series_list:
        #         clear_series_list.append(i.series_instance_uid)
        _algorithm_task_queryset = AlgorithmTask.objects.filter(series_uid__in=clear_series_list)
        _algorithm_result_queryset = AlgorithmResult.objects.filter(image_series__in=clear_series_list)
        clear_mongodb_id_list = []
        task_id_list = []
        for j in _algorithm_result_queryset:
            if j.algorithm_result not in clear_mongodb_id_list:
                clear_mongodb_id_list.append(j.algorithm_result)
        for t in _algorithm_task_queryset:
            if t.uuid not in task_id_list:
                task_id_list.append(t.uuid)
        if _algorithm_task_queryset:
            _algorithm_task_queryset.delete()
        if _algorithm_result_queryset:
            _algorithm_result_queryset.delete()
        if clear_series_list:
            CallBackDICOM.objects.filter(series_instance_uid__in=clear_series_list).delete()

        # 删除mongodb
        if clear_mongodb_id_list:
            _mongodb = MongoDB()
            clear_mongodb_id_list = [ObjectId(m) for m in clear_mongodb_id_list]
            _deletemongodb = _mongodb.bulk_delete(condition_list=clear_mongodb_id_list, condition_key="_id",
                                                  table="algorithm")
        # 删除orthanc
        # select serise
        serise_list = find_orthanc_resource_by_type(study_instance_id, algorithm_type)
        if serise_list:
            clear_series_list.extend(serise_list)
        if clear_series_list:
            delete_orthanc_resources_by_series(series_list=clear_series_list)
            pass
        # 删除数据dcm目录
        for i in clear_series_list:
            folder = os.path.join(settings.DOCKER_CTP_ROOT_DIR, i)
            if os.path.exists(folder):
                shutil.rmtree(folder)
        #  删除ctp报告目录
        if algorithm_type == "ctp":
            for t in task_id_list:
                ctp_folder = os.path.join(settings.DOCKER_DATA_BASE_DIR, "ctp/" + t)
                if os.path.exists(ctp_folder):
                    shutil.rmtree(ctp_folder)

        return True

    def post(self, request):
        data = request.POST
        request_id = str(uuid.uuid1())
        creator = check_auth_code(request)
        log.info(f"request_id:{request_id}---request_data:{data}")
        if not creator:
            response = dict()
            response['code'] = 401
            response['message'] = 'Unauthorized'
            log.warning(f"request_id:{request_id}--response_data:{response}")
            return JsonResponse(response)
        check_status, request_dict = self.check_field(data=data, request_id=request_id)
        if not check_status:
            log.warning(f"request_id:{request_id}---request_data:{data}--response_data:{request_dict}")
            return JsonResponse(request_dict)
        request_type = request_dict.get("type", "")
        asynchronous = data.get("asynchronous", None)
        # timeout = data.get("timeout", None)
        if request_type == "date":
            if not asynchronous:
                excute_status, excute_data = self.excute_delete_for_enddate(data_for_dict=request_dict,
                                                                            request_id=request_id)
                return JsonResponse(excute_data)
            else:
                _th = threading.Thread(target=self.excute_delete_for_enddate, args=(request_dict, request_id))
                _th.start()
        elif request_type == "series":
            if not asynchronous:
                excute_status, excute_data = self.excute_delete_for_series(data_for_dict=request_dict,
                                                                           request_id=request_id)
                return JsonResponse(excute_data)
            else:
                _th = threading.Thread(target=self.excute_delete_for_series, args=(request_dict, request_id))
                _th.start()
        else:
            msg = {"code": 500,
                   "msg": "ukown error",
                   "request_id": request_id}
            log.error(f"request_id:{request_id}---request_data:{data}--response_data:{msg}")
            return JsonResponse(msg)
        response = dict()
        response['code'] = 200
        response['msg'] = "delete work asynchronous excute"
        response['request_id'] = request_id
        log.info(f"request_id:{request_id}---request_data:{data}--response_data:{response}")
        return JsonResponse(response)
