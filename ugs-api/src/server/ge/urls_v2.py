from django.urls import path

from .ge_views import (
    GrantAndStartCalc,
    InterruptCalc,
    QueryCalcResult,
    DeleteStorageDicom,
    CheckAlive,
    LicenseActivate,
    LicenseStatus,
    DicomSourceConfig,
    LicenseApplyInfo,
)

urlpatterns = [
    path('grantAndStartCalc', GrantAndStartCalc.as_view()),  # 授予资源并开始计算
    path('interruptCalc', InterruptCalc.as_view()),  # 中断计算接口
    path('queryCalcResult', QueryCalcResult.as_view()),  # 查询计算结果接口
    path('deleteStorageDicom', DeleteStorageDicom.as_view()),  # DICOM删除接口
    path('checkAlive', CheckAlive.as_view()),  # 应用心跳接口
    path('system/licenseActivate', LicenseActivate.as_view()),  # 激活AI应用license
    path('system/licenseStatus', LicenseStatus.as_view()),  # 获取AI应用license状态
    path('config/dicomSourceConfig', DicomSourceConfig.as_view()),  # 配置AI应用dicom源
    path('system/licenseApplyInfo', LicenseApplyInfo.as_view()),  # 获取AI应用的机器码,
]
