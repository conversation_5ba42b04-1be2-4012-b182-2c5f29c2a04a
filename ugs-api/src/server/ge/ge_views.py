#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import datetime
import os
import json
import logging
import traceback
import uuid
import base64
from urllib.parse import quote

from django.http import JsonResponse

from server.algorithm.models import AlgorithmTask
from server.algorithm.views import AlgorithmTextResult, get_algorithm_textresult

from server.common.base import check_auth_code
from server.common.code import RetCode
from server.user.auth import AuthVerify
from server.common.remote_api import RabbitMQProducer

from server.common.views import BaseView
from server.series.models import Series
from server.series.service import SeriesPushHandler
from server.study.models import DeleteStudy, Study
from server.study.service import StudyPushHandler, StudyRecalculateService, DeleteViewTools
from server.systemconfig.models import SystemConfig
from server.systemconfig.system_config_utils import SystemConfigUtils
from server.user.models import AuthorizationCode, AuthorizationRecord

log = logging.getLogger("django")

_env = os.environ
LOCAL_WEB_SERVER_HOST = _env.get('LOCAL_WEB_SERVER_HOST', "***********")
LOCAL_WEB_SERVER_PORT = _env.get('LOCAL_WEB_SERVER_PORT', "4224")

APPLY_CALC_SCHEDULE = _env.get("CLEAN_STORAGE_DATA_URL",
                               "http://ecm-backend.ecm:8020/third-party/v2/applyCalcSchedule")
STORAGE_DATA_URL = _env.get("STORAGE_DATA_URL", "http://ecm-backend.ecm:8020/third-party/v2/storageData")


class GrantAndStartCalc(BaseView):
    """
    授予资源并开始计算
    """""

    def post(self, request):
        request_body = request.POST
        response = {
            "code": 400,
            "message": ""
        }
        creator = check_auth_code(request)
        if not creator:
            response["message"] = "unauthorized"
            return JsonResponse(response)
        log.info("GE[GrantAndStartCalc] request uri:{} body：{}".format(request.get_full_path(), request_body))

        calcNo = request_body.get("calcNo", None)  # 计算编号：AI申请时CalcNo
        isRecalc = request_body.get("isRecalc", 0)  # 是否重新计算标记
        bizData = request_body.get("bizData", {})  # 坐标数据
        dicomResourceType = request_body.get("dicomResourceType", None)  # dicom资源类型：study/series
        dicomResourceUid = request_body.get("dicomResourceUid", None)  # dicom资源Uid：studyUid/seriesUid
        resourceData = {
            "resourceId": request_body.get("resourceId", None),  # GPU ID  不大于7；[0-7]
            "resourceType": request_body.get("resourceType", None),  # GPU 类型
            "resourceCapacity": request_body.get("resourceCapacity", None),  # GPU 容量
            "resourceNode": request_body.get("resourceNode", None),  # GPU所在位置
            "resourceMetadata": request_body.get("resourceMetadata", None)  # GPU 元数据
        }
        if not calcNo or not isinstance(calcNo, str):
            log.info(f"GE[GrantAndStartCalc] calcNo error: {calcNo}")
            response['message'] = f"GE[GrantAndStartCalc] calcNo error: {calcNo}"
            return JsonResponse(response)
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        if not GE_CALLBACK_START == "1":
            log.info(f"GE[GrantAndStartCalc] GE_CALLBACK_START: {GE_CALLBACK_START}")
            response['message'] = f"GE[GrantAndStartCalc] GE_CALLBACK_START: {GE_CALLBACK_START}"
            return JsonResponse(response)

        if "," in calcNo:
            study_instance_uid, series_instance_uid = calcNo.split(",")
            series_qs = Series.objects.filter(series_instance_uid=series_instance_uid)
            if not series_qs.exists():
                log.info(f'GE[GrantAndStartCalc] calcNo error: series not exists: {calcNo}')
                response['message'] = f'calcNo error: series not exists: {calcNo}'
                return JsonResponse(response)
            series = series_qs.first()
            algorithm_type = series.type
            if isRecalc == 1:
                log.info(f'GE[GrantAndStartCalc] series start recalculate, calcNo: {calcNo}')
                ret_code = StudyRecalculateService.recalculate(study_instance_uid, {"algorithmType": algorithm_type}, bizData)
            else:
                log.info(f'GE[GrantAndStartCalc] series start calc, calcNo: {calcNo}')
                Series.objects.filter(series_instance_uid=series_instance_uid).update(geapi_status=3)  # 表示进行中
                SeriesPushHandler.calculate_series(series, resource_data=resourceData)
                ret_code = RetCode.OK
            response["code"] = ret_code.code
            response["message"] = ret_code.msg
            return JsonResponse(response)
        else:
            # 东芝灌注ctp
            study_instance_uid = calcNo
            if isRecalc == 1:  # 重新计算
                log.info(f'GE[GrantAndStartCalc] toshiba study start recalculate, calcNo: {calcNo}')
                ret_code = StudyRecalculateService.recalculate(study_instance_uid, {"algorithmType": "ctp"}, bizData)
            else:
                log.info(f'GE[GrantAndStartCalc] toshiba study start calc, calcNo: {calcNo}')
                # 1、申请成功  2 申请失败, 3 算法进行
                Series.objects.filter(study__study_instance_uid=study_instance_uid, type='ctp').update(geapi_status=3)  # 表示进行中
                ret_code = StudyPushHandler.calculate_toshiba_series(study_instance_uid, resource_data=resourceData)
            response["code"] = ret_code.code
            response["message"] = ret_code.msg
            return JsonResponse(response)


class InterruptCalc(BaseView):
    def post(self, request):
        log.info("GE[InterruptCalc] request uri:{} body：{}".format(request.get_full_path(), request.POST))
        response = {
            "code": 400,
            "message": ""
        }
        creator = check_auth_code(request)
        if not creator:
            response["message"] = "unauthorized"
            return JsonResponse(response)
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        if not GE_CALLBACK_START == "1":
            log.info(f"GE[GrantAndStartCalc] GE_CALLBACK_START: {GE_CALLBACK_START}")
            response['message'] = f"GE[grantAndStartCalc] GE_CALLBACK_START: {GE_CALLBACK_START}"
            return JsonResponse(response)
        response["code"] = 200
        return JsonResponse(response)


class QueryCalcResult(BaseView):
    """
    查询计算结果接口
    """

    def post(self, request):
        request_body = request.POST

        calcNo = request_body.get('calcNo')
        response = {
            "code": 400,
            "calcNo": calcNo,
            "aiResultMsg": json.dumps({}),
            "processingStatus": '2',  # 计算结果， 1 计算成功；2 计算失败；
            # "processingSubStatus": processingSubStatus,  # 计算子码
            # "processingStatusMsg": processingStatusMsg,  # 计算结果描述
            # "requestUrl": requestUrl,  # 计算结果详情链接
            "aiModel": '',  # 计算的部位
        }
        creator = check_auth_code(request)
        if not creator:
            response["message"] = "unauthorized"
            return JsonResponse(response)
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        log.info("GE[QueryCalcResult] request uri:{} body：{}, GE_CALLBACK_START:{}".format(
            request.get_full_path(), request_body, GE_CALLBACK_START))
        # 判断GE开关
        if GE_CALLBACK_START != "1":
            log.info(f"GE[QueryCalcResult] GE_CALLBACK_START: {GE_CALLBACK_START}")
            response['processingStatusMsg'] = f"GE[QueryCalcResult] GE_CALLBACK_START: {GE_CALLBACK_START}"
            return JsonResponse(response)
        # 判断calcNo
        if not calcNo or not isinstance(calcNo, str):
            log.info(f"GE[QueryCalcResult] calcNo error: {calcNo}")
            response['processingStatusMsg'] = f'calcNo error: {calcNo}'
            return JsonResponse(response)
        series_field = ""
        if "," in calcNo:

            study_instance_uid, series_instance_uid = calcNo.split(",")
            algorithm_task_qs = AlgorithmTask.objects.filter(series_uid=series_instance_uid)

            series_uids = [series_instance_uid]
            # 算法类型
            algorithm_type = algorithm_task_qs.first().algorithm_type
            series_field = f";defaultSeriesUid={series_instance_uid}"
        else:
            # 东芝灌注ctp类型， 用study_instance_uid 查询算法结果
            study_instance_uid = calcNo
            algorithm_type = 'ctp'
            series_uids = list(Series.objects.filter(
                study__study_instance_uid=study_instance_uid
            ).values_list(
                "series_instance_uid", flat=True
            ))

        res = {
            'ctp': dict(status=False, value="", algorithm_state=AlgorithmTextResult.Const.ALGORITHM_STATE_WAIT),
            'aspects': dict(status=False, value="", algorithm_state=AlgorithmTextResult.Const.ALGORITHM_STATE_WAIT),
            'cta': dict(status=False, value="", algorithm_state=AlgorithmTextResult.Const.ALGORITHM_STATE_WAIT)
        }

        algorithm_text_result = get_algorithm_textresult(study_instance_uid, series_uids, res)
        # 获取算法对应的结果
        algoritm_result = algorithm_text_result.get(algorithm_type, {})
        log.info(f"GE[QueryCalcResult] algorithm_text_result： {algorithm_text_result}")
        response["aiResultMsg"] = json.dumps(algoritm_result, ensure_ascii=False)
        response["code"] = 200
        processingStatus = algoritm_result.get("algorithm_state", 0)
        processing_status = 0
        if processingStatus == 2:
            processing_status = 2
        elif processingStatus == 3:
            processing_status = 1
        response["processingStatus"] = processing_status
        response["aiModel"] = algorithm_type
        study_qs = Study.objects.filter(study_instance_uid=study_instance_uid)
        if not study_qs.exists():
            response['processingStatusMsg'] = f'calcNo error: study not exists'
            return JsonResponse(response)
        receiveTime = study_qs.first().gmt_modified or datetime.datetime.now()
        receiveTime_str = receiveTime.strftime('%Y-%m-%d %H:%M:%S')
        data_bytes = receiveTime_str.encode("utf-8")
        encode_str = quote(base64.b64encode(data_bytes).decode("utf-8"))
        response["requestUrl"] = f"http://{LOCAL_WEB_SERVER_HOST}:{LOCAL_WEB_SERVER_PORT}/read/{study_instance_uid}/v2/{encode_str}{series_field}"

        return JsonResponse(response)


class DeleteStorageDicom(BaseView):
    """
    DICOM删除接口
    请求示例
        {
            "mainDicomTags": {
                "SeriesInstanceUID": "1.2.276.0.7230010.*******.29114.1577713005.656955",
                "StudyInstanceUID": "1.2.276.0.7230010.*******.29114.1577713005.656952"
            },
            "calcNo": "xxx"
        }
    """

    def post(self, request):
        request_body = request.POST
        response = {
            "code": 400,
            "message": ""
        }
        creator = check_auth_code(request)
        if not creator:
            response["message"] = "unauthorized"
            return JsonResponse(response)
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        log.info("GE[DeleteStorageDicom] request uri:{} body：{}, GE_CALLBACK_START:{}".format(
            request.get_full_path(), request_body, GE_CALLBACK_START))
        if not GE_CALLBACK_START:
            log.info(f"GE[DeleteStorageDicom] GE_CALLBACK_START: {GE_CALLBACK_START}")
            response['message'] = f"GE[DeleteStorageDicom] GE_CALLBACK_START: {GE_CALLBACK_START}"
            return JsonResponse(response)

        mainDicomTags = request_body.get('mainDicomTags', {})
        StudyInstanceUID = mainDicomTags.get('StudyInstanceUID')

        delete_qs = DeleteStudy.objects.filter(
            study_list__contains=StudyInstanceUID,
            gmt_create__range=(
                    datetime.datetime.now()-datetime.timedelta(minutes=3),
                    datetime.datetime.now())
        )
        study_qs = Study.objects.filter(study_instance_uid=StudyInstanceUID)
        if not study_qs.exists():
            response["message"] = "StudyInstanceUID not exists"
            return JsonResponse(response)
        if delete_qs.exists():
            response["message"] = "Repeated requests"
            return JsonResponse(response)

        studyList = [StudyInstanceUID]
        delete_study_id = DeleteViewTools.create_delete_data(studyList, "")
        if delete_study_id is None:
            response["message"] = "params can not empty"
            return JsonResponse(response)
        if delete_study_id == -1:
            response["message"] = "study not exists"
            return JsonResponse(response)
        # 组装消息
        message = dict(studyList=studyList, delete_study_id=delete_study_id)
        queue_name = "study_delete_task"
        try:
            RabbitMQProducer.simple_send(queue_name, message)
            log.info(f"Study[delete] > studyList:{studyList} queue_name: {queue_name}, message:{message}")
            response["message"] = "删除成功"
            response["code"] = 200
            response["status"] = True
        except:
            response["message"] = "delete study error"
            response["code"] = 400
            response["status"] = False
        return JsonResponse(response)


class CheckAlive(BaseView):
    def post(self, request):
        """
        检测AI应用存活接口
        """
        return JsonResponse({"code": 200})


class LicenseActivate(BaseView):
    def post(self, request):
        """
        激活AI应用license
        """
        response = {
            "code": 400,
            "message": "",
        }
        data = request.POST
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        log.info("GE[LicenseActivate] request uri:{} body：{}, GE_CALLBACK_START:{}".format(
            request.get_full_path(), data, GE_CALLBACK_START))
        if GE_CALLBACK_START != "1":
            log.info(f"GE[LicenseActivate] GE_CALLBACK_START: {GE_CALLBACK_START}")
            response['message'] = f"GE[LicenseActivate] GE_CALLBACK_START: {GE_CALLBACK_START}"
            return JsonResponse(response)

        auth_code = data.get("license", None)
        if not auth_code:
            log.info(f"GE[LicenseActivate] license: {auth_code}")
            response["message"] = "请输入授权码"
            return JsonResponse(response)
        auth_record_qs = AuthorizationRecord.objects.filter(auth_code=auth_code)
        if auth_record_qs.exists():
            response["message"] = "此授权码已经被使用过"
            return JsonResponse(response)
        _auth = AuthVerify()
        try:
            start_time, period, modules_str = _auth.decrypt(auth_code)
        except Exception as e:
            log.info(f"GE[LicenseActivate] license: {auth_code}, 授权码不正确")
            response["message"] = "授权码不正确，请重新获取授权码"
            return JsonResponse(response)
        if not start_time or period is None:
            response["message"] = "授权失败"
            return JsonResponse(response)
        queryset = AuthorizationCode.objects.all()
        if queryset:
            # 有授权码
            queryset.delete()
        try:
            now_time = datetime.datetime.now()
            # start_time = datetime.datetime.strptime(start_time, "%Y-%m-%d")
            end_time = start_time + datetime.timedelta(days=period)
            if not (now_time > start_time and now_time < end_time):
                response["message"] = "授权码已过期，请重新获取授权码"
                log.info(f"GE[LicenseActivate] license: {auth_code}, 授权码已过期")
                return JsonResponse(response)
            AuthorizationCode.objects.create(
                uuid=str(uuid.uuid1()),
                create_user="admin",
                auth_local=modules_str,
                auth_end_date=end_time,
                auth_code=auth_code,
                auth_period=period,
                auth_start_date=start_time
            )
            AuthorizationRecord.objects.create(
                auth_code=auth_code
            )
        except Exception:
            print(traceback.format_exc())
            log.info(f"GE[LicenseActivate] license: {auth_code}, 授权码不正确， error：{traceback.format_exc()}")
            response["message"] = "授权码不正确，请重新获取授权码"
            return JsonResponse(response)
        response["message"] = "授权成功"
        response["code"] = 200
        log.info(f"GE[LicenseActivate] license: {auth_code} success")
        return JsonResponse(response)


class LicenseStatus(BaseView):
    def post(self, request):
        """
         获取AI应用license状态
        """
        response = {
            "code": 400,
            "message": "",
        }
        log.info("GE[LicenseStatus] request uri:{} body：{}".format(request.get_full_path(), request.POST))
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        if GE_CALLBACK_START != "1":
            log.info(f"GE[LicenseStatus] GE_CALLBACK_START: {GE_CALLBACK_START}")
            response['message'] = f"GE[LicenseStatus] GE_CALLBACK_START: {GE_CALLBACK_START}"
            return JsonResponse(response)
        response["code"] = 200
        response["status"] = True
        ctp = {"productName": "CTP", "statusMsg": "未激活", "validStartDate": "", "validEndDate": ""}
        ncct = {"productName": "NCCT", "statusMsg": "未激活", "validStartDate": "", "validEndDate": ""}
        cta = {"productName": "CTA", "statusMsg": "未激活", "validStartDate": "", "validEndDate": ""}
        queryset = AuthorizationCode.objects.all()
        if not queryset.exists():
            log.info("GE[LicenseStatus] AuthorizationCode not exists")
            response["productList"] = [ctp, ncct, cta]
            response["message"] = "GE[LicenseStatus] AuthorizationCode not exists"
            response["code"] = 400
            return JsonResponse(response)
        _auth = queryset.first()
        end_time = _auth.auth_end_date
        auth_code = _auth.auth_code
        if "-" not in auth_code:
            response["productList"] = [ctp, ncct, cta]
            response["message"] = "GE[LicenseStatus] AuthorizationCode  code error"
            response["code"] = 400
            return JsonResponse(response)
        now_time = datetime.datetime.now()
        total_sec = (end_time - now_time).days
        ctp["validStartDate"] = ncct["validStartDate"] = cta["validStartDate"] = _auth.auth_start_date.strftime(
            "%Y-%m-%d")
        ctp["validEndDate"] = ncct["validEndDate"] = cta["validEndDate"] = _auth.auth_end_date.strftime("%Y-%m-%d")

        ctp["statusMsg"] = ncct["statusMsg"] = cta["statusMsg"] = "已激活"
        ncct["statusMsg"] = "已激活"
        if total_sec < 0:
            ctp["statusMsg"] = ncct["statusMsg"] = cta["statusMsg"] = "已过期"
        productList = []
        auth_code_str = auth_code.split("-")[1]
        module_json = base64.b64decode(auth_code_str.encode(encoding="utf-8")).decode()
        code = json.loads(module_json)
        auth_module_list = [auth_module.get("name") for auth_module in code.get("modules", [])]
        for auth_module in auth_module_list:
            if auth_module.upper() == "NCCT":
                productList.append(ncct)
            if auth_module.upper() == "CTP":
                productList.append(ctp)
            if auth_module.upper() == "CTA":
                productList.append(cta)

        response["productList"] = productList

        return JsonResponse(response)


class DicomSourceConfig(BaseView):
    def post(self, request):
        """
        .配置AI应用dicom源
        Edison MOHE可配置dicom是否自动回传
        {
        }
        """
        response = {
            "code": 400,
            "message": ""
        }
        creator = check_auth_code(request)
        if not creator:
            response["message"] = "unauthorized"
            return JsonResponse(response)
        data = request.POST
        log.info("GE[DicomSourceConfig] request uri:{} body：{}".format(request.get_full_path(), data))
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        if GE_CALLBACK_START != "1":
            log.info(f"GE[DicomSourceConfig] GE_CALLBACK_START: {GE_CALLBACK_START}")
            response['message'] = f"GE[DicomSourceConfig] GE_CALLBACK_START: {GE_CALLBACK_START}"
            return JsonResponse(response)
        ae_type = data.get("type", None)
        if not ae_type in ['in', 'out']:
            response['message'] = 'type error'
            return JsonResponse(response)
        alie_name = "target_modality"
        if ae_type == 'in':
            alie_name = "target_pacs"

        callback = data.get("isAutoBack", False)  # 是否自动回传
        # 设置报告回传
        queryset = SystemConfig.objects.filter(code='canReportBack', )
        if not queryset.exists():
            response['code'] = 400
            response['message'] = '没有报告回传信息'
            return JsonResponse(response)
        system_config = queryset.first()
        system_config.value = callback
        system_config.save()

        pacs_list = [{
            "ip_address": data.get("ip", None),
            "port": data.get("port", None),
            "aet": data.get("aet", None),
            "alie_name": alie_name,
            "id": None
        }]
        # try:
        #     message = PACSServerView.config_pacs_server(pacs_list)
        #     response["message"] = message
        # except:
        #     message = f"GE[DicomSourceConfig] config_pacs_server error"
        #     # log.info(f"GE[DicomSourceConfig] config_pacs_server error{traceback.format_exc()}")
        #     response["message"] = message
        response["code"] = 200
        response["message"] = "更新或创建成功"
        return JsonResponse(response)


class LicenseApplyInfo(BaseView):
    def post(self, request):
        response = {
            "code": 400,
            "message": "",
            "applyInfoList": []
        }
        try:
            machine_code = AuthVerify().generate_machine_code()
            if machine_code:
                response["applyInfoList"].append({
                    'name': "machineCode",
                    'value': machine_code
                })
        except Exception:
            log.info(f"GE[LicenseApplyInfo] >  get machine code: error：{traceback.format_exc()}")
            response["message"] = "机器码获取失败"
            return JsonResponse(response)
        response["message"] = "机器码获取成功"
        response["code"] = 200
        log.info(f"GE[LicenseApplyInfo] > get machine code: success")
        return JsonResponse(response)
