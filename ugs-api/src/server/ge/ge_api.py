#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import os
import logging
import traceback

import requests

from server.common.code import RetCode
from server.series.models import Series

_env = os.environ
LOCAL_WEB_SERVER_HOST = _env.get('LOCAL_WEB_SERVER_HOST', "***********")
LOCAL_WEB_SERVER_PORT = _env.get('LOCAL_WEB_SERVER_PORT', "4224")
GE_RETRIES_TIMES = int(_env.get('GE_RETRIES_TIMES', 3))

GE_CALLBACK_THIRD_PARTY_ORIGIN_ID = _env.get('GE_CALLBACK_THIRD_PARTY_ID', 2)
GE_CALLBACK_THIRD_PARTY_ID = int(GE_CALLBACK_THIRD_PARTY_ORIGIN_ID) if GE_CALLBACK_THIRD_PARTY_ORIGIN_ID else 2
# GE回调配置
APPLY_CACL_SCHEDULE_URL = _env.get("APPLY_CACL_SCHEDULE",
                               "http://ecm-backend.ecm:8020/third-party/v2/applyCalcSchedule")
STORAGE_DATA_URL = _env.get("STORAGE_DATA_URL", "http://ecm-backend.ecm:8020/third-party/v2/storageData")
VALIDATE_DICOM_FAILED_URL = _env.get("VALIDATE_DICOM_FAILED_URL", "http://ecm-backend.ecm:8020/third-party/v2/validateDicomFailed")

log = logging.getLogger("django")


def ge_apply_calc_schedule_notice(calcNo, dicomResourceType, dicomResourceId, bizData=None, isRecalc=0):
    """
    请求接口并发通知
    """
    ret_code = ge_apply_calc_schedule(calcNo, dicomResourceType, dicomResourceId, bizData, isRecalc)
    if "," in calcNo:
        study_instance_uid, series_instance_uid = calcNo.split(",")
        condition = {
            "study__study_instance_uid": study_instance_uid,
            "series_instance_uid": series_instance_uid
        }
    else:
        study_instance_uid = calcNo
        condition = {
            "study__study_instance_uid": study_instance_uid,
            "type": "ctp"
        }
    # 1、申请成功  2 申请失败, 3 算法进行
    ge_api_status = 1 if ret_code.code == 200 else 2
    Series.objects.filter(**condition).update(geapi_status=ge_api_status)
    return ret_code


# @request_retry(retry_times=GE_RETRIES_TIMES, sleep_time=2)
def ge_apply_calc_schedule(calcNo, dicomResourceType, dicomResourceId, bizData, isRecalc):
    """
    申请调度计算任务: 申请计算任务入队列， ECM会在之后某个时间调用通知AI计算接口
    请求示例:
        {
            "calcNo": "qdaseryihk321654",
            "thirdConfigId": 0,
            "resourceType": "study",
            "resourceId": "1.3.46.670589.33.1.63717620946909319400001.5284401492300249294"
        }
    """

    data = {
        "thirdConfigId": 2,  # 第三方AI公司标识,必填项(不同AI公司id不一样, 想确认id和GE工作人员联系)
        'calcNo': calcNo,  # AI公司计算编号: 申请计算的case_num
        'dicomResourceType': dicomResourceType,  # 所用dicom资源类型:  patient:病人 / study:检查
        'dicomResourceId': dicomResourceId,  # dicom资源唯一标识: patientId/studyUid/seriesUid
        'isRecalc': isRecalc,
        'bizData': bizData if bizData else {},  # 重新計算的坐标数据
    }
    headers = {"Content-Type": "application/json"}
    log.info(f"GE[applyCalcSchedule] request url: {APPLY_CACL_SCHEDULE_URL}, body: {data}")
    try:
        response = requests.post(url=APPLY_CACL_SCHEDULE_URL, json=data, headers=headers)
        log.info(f"GE[applyCalcSchedule] calcNo: {calcNo}, response_code：{response.status_code}, message: {response.content}")
        if response.status_code in (200, 201):
            return RetCode.OK
        return RetCode.GE_APPLY_CACL_SCHEDULE_ERROR
    except:
        log.info(f"GE[applyCalcSchedule] calcNo{calcNo}, request error: {traceback.format_exc()}")
        return RetCode.GE_APPLY_CACL_SCHEDULE_ERROR


def storage_data(calcNo, aiResultMsg, aiModel,  processingStatus=2):
    """
    更新魔盒计算状态:  计算结果序列状态变化时回调接口
    """
    data = {
        "thirdConfigId": GE_CALLBACK_THIRD_PARTY_ID,
        "calcNo": calcNo,
        "aiResultMsg": aiResultMsg,
        "processingStatus": processingStatus,
        "aiModel": aiModel
    }
    ge_storage_data(data)


# @request_retry(retry_times=GE_RETRIES_TIMES, sleep_time=2)
def ge_storage_data(data):
    """
    更新魔盒计算状态:  计算结果序列状态变化时回调接口
    请求示例
    {
        "thirdConfigId": 2,
        "calcNo": "",
        "aiResultMsg": "{\"degree\": \"xxx\", \"level\": \"0\"}", //JSON.toJSONString(Map<String, Object>)
        "processingStatus": "-1",
        "processingStatusMsg": "图像脏数据",
        "requestUrl": "http://xxx/xxx",
        "aiModel": "冠脉"
    }
    """
    headers = {"Content-Type": "application/json"}
    log.info(f"GE storageData api url:{STORAGE_DATA_URL}, data: {data}")
    try:
        response = requests.post(url=STORAGE_DATA_URL, json=data, headers=headers, timeout=30)
        log.info(f"GE[storageData] response_code：{response.status_code}, message: {response.content}")
        return response.status_code
    except:
        log.info(f"GE[storageData] request error: {traceback.format_exc()}")
        return 400


def validate_dicom_failed(message, resource_type, resource_uid, ai_model):
    """
    通知魔盒DICOM数据匹配分发规则失败
    第三方AI申请计算前， 若未匹配规则或其他错误导致不会申请计算。则需调用此接口通知魔盒
    请求示例
    {
        "thirdConfigId": 2,
        "processingStatusMsg": ""， # 描述，如：失败原因等（匹配规则失败等）
        "dicomResourceType": "" , # study/series
        "dicomResourceUid": "", # studyUid/seriesUid
        "aiModel": "ctp"
    }
    """
    data = {
        "thirdConfigId": GE_CALLBACK_THIRD_PARTY_ID,  # 第三方AI公司标识,必填项(不同AI公司id不一样, 想确认id和GE工作人员联系)
        "processingStatusMsg": message,
        'dicomResourceType': resource_type,  # 所用dicom资源类型:  patient:病人 / study:检查
        'dicomResourceId': resource_uid,  # dicom资源唯一标识: patientId/studyUid/seriesUid
        'aiModel': ai_model
    }
    headers = {"Content-Type": "application/json"}
    log.info(f"GE validateDicomFailed api url:{VALIDATE_DICOM_FAILED_URL}, data: {data}")
    try:
        response = requests.post(url=VALIDATE_DICOM_FAILED_URL, json=data, headers=headers, timeout=30)
        log.info(f"GE[validateDicomFailed] response_code：{response.status_code}, message: {response.content}")
        return response.status_code
    except:
        log.error(f"GE[validateDicomFailed] request error: {traceback.format_exc()}")
        return 400