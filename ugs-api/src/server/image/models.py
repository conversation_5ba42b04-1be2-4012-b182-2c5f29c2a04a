#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : models
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/18 10:10
"""

from django.db import models

# Create your models here.


class CallBackDICOM(models.Model):
    uuid = models.CharField(primary_key=True, verbose_name='uuid', max_length=255)
    hospital_uid = models.CharField(max_length=64, null=False, default="0", verbose_name="医院标识")
    sop_instance_uid = models.Char<PERSON><PERSON>(verbose_name="SOP序列uid", max_length=128, null=True, blank=True)
    instance_number = models.SmallIntegerField(null=False, default=0, verbose_name="影像编号")
    study_instance_uid = models.CharField(verbose_name="STUDY序列uid", max_length=128, null=True, blank=True)
    series_instance_uid = models.Cha<PERSON><PERSON><PERSON>(verbose_name="Series序列uid", max_length=128, null=True, blank=True)
    slice_thickness = models.CharField(verbose_name="切片厚度", max_length=16, default='0')
    slice_position = models.CharField(verbose_name="切片位置", max_length=16, default='0')
    path = models.CharField(max_length=512, null=False, default="", verbose_name="图像地址")
    sop_orthanc_uuid = models.CharField(verbose_name="OrthancID", max_length=255, null=True, blank=True)
    protocol_name = models.TextField(verbose_name="协议名", null=True, blank=True)
    timestamp = models.DateTimeField(verbose_name='创建时间', auto_now_add=True)
    updatetimestamp = models.DateTimeField(verbose_name='更新时间', auto_now=True)

    class Meta:
        db_table = 'callback_dicom'
        verbose_name = '回调接收内容'
        verbose_name_plural = '回调接收内容'

    def to_dict(self):
        return dict(
            soporthancuuid=self.sop_instance_uid,
            StudyInstanceUID=self.study_instance_uid,
            SeriesInstanceUID=self.series_instance_uid,
            updatetimestamp=self.updatetimestamp,
            StudyDate=self.timestamp.strftime("%Y-%m-%d")
        )
