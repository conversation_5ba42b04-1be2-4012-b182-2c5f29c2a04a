#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import base64
import datetime
import hashlib
import json
import time
import os
import traceback
import logging

from server.common.code import RetCode

try:
    import uwsgi
except ImportError:
    pass

from server.user.models import AuthorizationCode

log = logging.getLogger("django")


# 授权码的生成以及验证
class AuthVerify(object):
    """
    结构体：
        {
            "modules":
                [
                    {"name": "required: true", "startDate": "required: false", "period": "required: false"},
                    {"name": "required: true", "startDate": "required: false", "period": "required: false"}
                ],
            "startDate": "required: false",
            "period": "required: false"
        }
    """
    _instance = None
    algorithm_map = {
        "NCCT": "N",
        "CTP": "P",
        "CTA": "A"
    }

    # 单例，防止uwsgi启动多进程/多线程时  实例化多个
    def __new__(cls, *args, **kw):
        if cls._instance is None:
            cls._instance = object.__new__(cls)
        return cls._instance

    def encrypt_str(self, startDate, period):
        # 1.对收到的startDate进行解析，分解成年月日
        # 2.对每一位的年月日分别从key中取出对应的值
        # 3.在新生成的字符串的第一个位置加入'-'加入周期长度
        # 4.进行base64位的encode
        start_date = datetime.datetime.strptime(startDate, "%Y-%m-%d")
        period_list = list(str(period).rjust(5, '0'))
        month_str = str(start_date.month).rjust(2, '0')
        day_str = str(start_date.day).rjust(2, '0')
        start_date_str = str(start_date.year) + period_list[0] + month_str + period_list[1] + day_str + "".join(period_list[2:])
        salt_str = self.add_salt(start_date_str)
        return self.str_to_base(salt_str)

    def encrypt(self, module_json):
        """
        @module_json:
            {
                "modules":  # 当没有模块的时候就不会有这个字段
                    [
                        {"name": "required: true", "startDate": "required: false", "period": "required: false"},
                        {"name": "required: true", "startDate": "required: false", "period": "required: false"}
                    ],
                "startDate": "required: true", 这个版本是true
                "period": "required: true" 这个版本是true
            }

        encrypt_json:
            {
                "modules":  # 当没有模块的时候就不会有这个字段
                    [
                        {"name": "required: true", "code": "required: false"},
                        {"name": "required: true", "code": "required: false"}
                    ],
                "code": "required: true"  # 这个版本是true
            }
        """
        module_list = module_json.get("modules", [])
        startDate = module_json.get("startDate")
        period = module_json.get("period")

        encrypt_str = self.encrypt_str(startDate, period)
        module_list.sort(key=lambda k: (k.get("name")), reverse=True)
        module_alias = ""
        for module_info in module_list:
            module_name = module_info.get("name")
            module_alias += self.algorithm_map.get(module_name.upper())
        if module_list:
            module_dict = {
                "modules": module_list,
                "code": encrypt_str
            }
            str_code = json.dumps(module_dict).encode(encoding="utf-8")
            encrypt_str = base64.b64encode(str_code).decode()
        encrypt_str = f"{module_alias}-{encrypt_str}" if module_alias else encrypt_str
        return encrypt_str

    def decrypt_str(self, authCode):
        decode_string = self.base_to_str(authCode)
        result_string = self.sub_salt(decode_string)
        period_index_list = [4, 7, 10, 11, 12]
        period_content_list = [str(result_string[index]) for index in period_index_list]
        date_content_list = ["-" if index in period_index_list else str(x) for index, x in enumerate(result_string)]
        period = int(''.join(period_content_list))
        start_date = ''.join(date_content_list)
        return start_date, period

    def decrypt(self, auth_code=""):
        """
        解密
        """
        log.info("AUTHGenerator > decrypt start")
        start_time, period, modules_str = None, None, ""
        try:
            if not auth_code:
                if not uwsgi.cache_exists("authCode") or not self.cache_get("authCode", tag=False):
                    auth_code = AuthorizationCode.objects.first().auth_code
                    self.cache_set("authCode", auth_code, tag=False)
                else:
                    auth_code = self.cache_get("authCode", tag=False)
            auth_code_str = auth_code.split("-")[1]
            module_json = base64.b64decode(auth_code_str.encode(encoding="utf-8")).decode()
            module_dict = json.loads(module_json)
            modules = [auth_module.get("name").lower() for auth_module in module_dict.get("modules", [])]
            if "ncct" in modules:
                modules.append("aspects")
            modules_str = "|".join(modules)

            code = module_dict.get("code")
            start_date, period = self.decrypt_str(code)
            start_time = datetime.datetime.strptime(start_date[:10], "%Y-%m-%d")
            end_time = start_time + datetime.timedelta(days=period)
            now_time = datetime.datetime.now()

            if now_time >= start_time and now_time <= end_time:
                self.cache_set("AuthExpireTime", datetime.datetime.now() + datetime.timedelta(minutes=5))
                old_auth_code = self.cache_get("authCode", tag=False)
                if not uwsgi.cache_exists("authCode") or not uwsgi.cache_get("authCode") or old_auth_code != auth_code:
                    self.cache_set("authCode", auth_code, tag=False)
            log.info(f"AUTHGenerator > decrypt end, start_date: {start_date[:-1]}, period: {period}, auth_code:{auth_code}")
        except:
            log.info(f"AUTHGenerator > decrypt auth_code:{auth_code}, error: {traceback.format_exc()}")
        return start_time, period, modules_str

    def base_to_str(self, code):
        """base 64 decode"""
        code = code.encode(encoding="utf-8")
        return base64.b64decode(code).decode().split('-')[0]

    def str_to_base(self, str_code):
        """base 64 encode"""
        str_code = str_code + '-' + str(int(time.time()))
        str_code = str_code.encode(encoding="utf-8")
        return base64.b64encode(str_code).decode()

    def add_salt(self, start_date_str):
        """加盐值"""
        key = self.get_machine_code()
        key = self.process_machine_code(key)
        start_date_list = list(start_date_str)
        code_string_list = [key[int(date)] for date in start_date_list]
        return ''.join(code_string_list)

    def sub_salt(self, decode_str):
        key = self.cache_get("MachineCode", tag=False)
        key = self.process_machine_code(key)
        decode_string_list = list(decode_str)
        string_list = [key.find(_str) for _str in decode_string_list]
        return string_list

    def process_machine_code(self, keys):
        """
        机器码：在使用前进行预处理
        :return:
        """
        order_data = []
        if not keys:
            return ""
        for key in keys:
            if key not in order_data:
                order_data.append(key)
        order_data.extend(list("@$&*^#)+{?"))
        result = "".join(order_data)
        return result

    def cache_set(self, key, value, tag=True):
        try:
            if tag:
                value = value.strftime("%Y-%m-%d %H:%M:%S")
            uwsgi.lock()
            if not uwsgi.cache_exists(key) and value:
                uwsgi.cache_set(key, json.dumps(value))
            else:
                uwsgi.cache_update(key, json.dumps(value))
            uwsgi.unlock()
        except:
            log.info(f"cache set error key: {key}, value: {value}")

    def cache_get(self, key, tag=True):
        try:
            value = uwsgi.cache_get(key)
            if value:
                if not tag:
                    return json.loads(value)
                result = json.loads(value)
                if result:
                    result = datetime.datetime.strptime(result, "%Y-%m-%d %H:%M:%S")
                return result
        except:
            log.info(f"cache get error key: {key}")
        return ""

    def generate_machine_code(self):
        """
        生成机器码
        """
        machine_sn_cmd = os.popen("dmidecode -s baseboard-serial-number")
        machine_sn = machine_sn_cmd.read().replace("\n", "")
        system_uuid_cmd = os.popen("dmidecode -s system-uuid")
        system_uuid = system_uuid_cmd.read().replace("\n", "")
        code = f"{machine_sn}{system_uuid}"
        MachineCode = hashlib.md5(code.encode("utf-8")).hexdigest().upper() if code else ""
        return MachineCode

    def get_machine_code(self):
        """
        通过命令获取机器码，必须是穿透，具体查看docker-compoose.yaml
        """
        log.info(f"AUTHGenerator get MachineCode start...")
        MachineCode = ""  # 机器码
        current_time = datetime.datetime.now()
        try:
            MachineCode = self.generate_machine_code()
            self.cache_set("machineTime", datetime.datetime.now())
            self.cache_set("MachineCode", MachineCode, tag=False)
            log.info(f"AUTHGenerator > get MachineCode end > MachineCode: {MachineCode}, current_time: {current_time},"
                     f" machineTime: {self.cache_get('machineTime')}")
        except:
            log.info("AUTHGenerator > get MachineCode error: {}".format(traceback.format_exc()))
        return MachineCode

    def check_need_get_machine_code(self, current_time, create_user):
        """
        检查是否需要重新获取机器码
        """
        # 1天内不需要重新获取机器码，也不要重新解密， 超过一天就需要重新获取机器码及解密
        if not self.cache_get("machineTime") or (current_time - datetime.timedelta(days=1)) > self.cache_get(
                "machineTime"):
            log.info(f"AUTHGenerator > 获取机器码时间过期，需要重新获取机器码")
            self.get_machine_code()
        result = self.check_is_exemption(current_time, create_user)
        return result

    def check_is_exemption(self, current_time, create_user):
        """
        检查是否过期（是否免检）
        """
        # 5min内可以免检， 超过5min需要重新解密
        if not self.cache_get("AuthExpireTime") or current_time > self.cache_get("AuthExpireTime"):
            log.info(
                f"AUTHGenerator >  check_is_exemption before > current_time: {current_time}, machineTime: "
                f"{self.cache_get('machineTime')}, AuthExpireTime: {self.cache_get('AuthExpireTime')}")
            result = self.check_expire(create_user)
            log.info(f"AUTHGenerator >  check_is_exemption after > current_time: {current_time}, machineTime:"
                     f" {self.cache_get('machineTime')}, AuthExpireTime: {self.cache_get('AuthExpireTime')}")
            return result
        return create_user

    def check_expire(self, create_user):
        """
        检查过期的具体方法
        """
        try:
            start_time, period, modules_str = self.decrypt()
            if not start_time or period is None:
                return None
            return create_user
        except:
            log.info(f"AUTHGenerator decrypt error: f{traceback.format_exc()}")
            return None

    def get_auth_code_and_module(self):
        queryset = AuthorizationCode.objects.all()
        if not queryset.exists():
            return None, None, None
        auth_obj = queryset.first()
        create_user = auth_obj.create_user
        start_date = auth_obj.auth_start_date
        end_date = auth_obj.auth_end_date
        current_time = datetime.datetime.now()
        auth_code = auth_obj.auth_code
        if "-" not in auth_code:
            return None, None, []
        auth_code_str = auth_code.split("-")[1]
        module_json = base64.b64decode(auth_code_str.encode(encoding="utf-8")).decode()
        code = json.loads(module_json)
        auth_module_list = code.get("modules")

        if not (current_time >= start_date and current_time <= end_date):
            return None, end_date, auth_module_list
        try:
            auth_generator = AuthVerify()
            # 机器码获取的频率暂定每天更新一次，  在一天的范围内是否已经重新获取了机器码和解密
            result = auth_generator.check_need_get_machine_code(current_time, create_user)
            return result, end_date, auth_module_list
        except:
            log.info(f"check_auth_code > error: {traceback.format_exc()}")
            return None, end_date, auth_module_list

    def auth_modules(self):
        """模块校验入口"""
        queryset = AuthorizationCode.objects.all()
        if not queryset.exists():
            return []
        auth_obj = queryset.first()
        auth_local = auth_obj.auth_local
        if not auth_local:
            return []
        return auth_local.split("|")

    @staticmethod
    def check_module(algorithm_type):
        auth = AuthorizationCode.objects.first()
        if not auth or not auth.auth_local:
            return False
        module_list = auth.auth_local.split("|")
        return algorithm_type in module_list


