from django.test import TestCase

# Create your tests here.
import base64
import json

qq = {
    "status": True,
    "message": True,
    "data": {
        "tree_data": {
            "uuid": "69d45d30-bc31-11ea-ad72-000ec6535a9f",
            "name": "科室1",
            "doctor_num": 1,
            "access_id": "",
            "parent_uuid": "",
            "parent_name": "",
            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
            "hospital_name": "医院test2",
            "timestamp": "2020-07-02 06:58:20",
            "update_timestamp": "2020-07-02 06:58:20",
            "type": "Hospital",
            "children": [
                {
                    "uuid": "6bde8ede-d0b8-11ea-81bf-0242ac12000d",
                    "name": "gg",
                    "doctor_num": 0,
                    "access_id": "",
                    "parent_uuid": "69d45d30-bc31-11ea-ad72-000ec6535a9f",
                    "parent_name": "科室1",
                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                    "hospital_name": "医院test2",
                    "timestamp": "2020-07-28 09:55:09",
                    "update_timestamp": "2020-07-28 09:55:09",
                    "type": "Hospital",
                    "children": [
                        {
                            "uuid": "90f10ce2-d0c7-11ea-a54a-0242ac12000c",
                            "name": "sss",
                            "doctor_num": 0,
                            "access_id": "",
                            "parent_uuid": "6bde8ede-d0b8-11ea-81bf-0242ac12000d",
                            "parent_name": "gg",
                            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                            "hospital_name": "医院test2",
                            "timestamp": "2020-07-28 11:43:33",
                            "update_timestamp": "2020-07-28 11:43:33",
                            "type": "Hospital",
                            "children": []
                        },
                        {
                            "uuid": "b8cd867a-d0bb-11ea-96c7-0242ac12000d",
                            "name": "ggg2",
                            "doctor_num": 0,
                            "access_id": "",
                            "parent_uuid": "6bde8ede-d0b8-11ea-81bf-0242ac12000d",
                            "parent_name": "gg",
                            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                            "hospital_name": "医院test2",
                            "timestamp": "2020-07-28 10:18:46",
                            "update_timestamp": "2020-07-28 10:18:46",
                            "type": "Hospital",
                            "children": []
                        }
                    ]
                },
                {
                    "uuid": "ffb2393e-bc30-11ea-a691-000ec6535a9f",
                    "name": "科室11",
                    "doctor_num": 0,
                    "access_id": "",
                    "parent_uuid": "69d45d30-bc31-11ea-ad72-000ec6535a9f",
                    "parent_name": "科室1",
                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                    "hospital_name": "医院test2",
                    "timestamp": "2020-07-02 06:55:22",
                    "update_timestamp": "2020-07-02 06:55:22",
                    "type": "Hospital",
                    "children": [
                        {
                            "uuid": "10",
                            "name": "科室333",
                            "doctor_num": 0,
                            "access_id": "",
                            "parent_uuid": "ffb2393e-bc30-11ea-a691-000ec6535a9f",
                            "parent_name": "科室11",
                            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                            "hospital_name": "医院test2",
                            "timestamp": "2020-07-22 10:39:33",
                            "update_timestamp": "2020-07-22 10:39:36",
                            "type": "Hospital",
                            "children": []
                        },
                        {
                            "uuid": "55544",
                            "name": "科室111",
                            "doctor_num": 0,
                            "access_id": "",
                            "parent_uuid": "ffb2393e-bc30-11ea-a691-000ec6535a9f",
                            "parent_name": "科室11",
                            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                            "hospital_name": "医院test2",
                            "timestamp": "2020-07-07 14:39:27",
                            "update_timestamp": "2020-07-07 14:39:30",
                            "type": "Hospital",
                            "children": [
                                {
                                    "uuid": "2",
                                    "name": "科室1111",
                                    "doctor_num": 0,
                                    "access_id": "",
                                    "parent_uuid": "55544",
                                    "parent_name": "科室111",
                                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                    "hospital_name": "医院test2",
                                    "timestamp": "2020-07-22 10:26:11",
                                    "update_timestamp": "2020-07-22 10:26:15",
                                    "type": "Hospital",
                                    "children": [
                                        {
                                            "uuid": "11",
                                            "name": "科室3333",
                                            "doctor_num": 0,
                                            "access_id": "",
                                            "parent_uuid": "2",
                                            "parent_name": "科室1111",
                                            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                            "hospital_name": "医院test2",
                                            "timestamp": "2020-07-22 10:40:40",
                                            "update_timestamp": "2020-07-22 10:40:44",
                                            "type": "Hospital",
                                            "children": []
                                        },
                                        {
                                            "uuid": "3",
                                            "name": "科室11111",
                                            "doctor_num": 0,
                                            "access_id": "",
                                            "parent_uuid": "2",
                                            "parent_name": "科室1111",
                                            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                            "hospital_name": "医院test2",
                                            "timestamp": "2020-07-22 10:28:13",
                                            "update_timestamp": "2020-07-22 10:28:16",
                                            "type": "Hospital",
                                            "children": [
                                                {
                                                    "uuid": "0b309d8e-d0c2-11ea-a54a-0242ac12000c",
                                                    "name": "gg",
                                                    "doctor_num": 0,
                                                    "access_id": "474be748-d161-11ea-8c8d-0242ac12000c",
                                                    "parent_uuid": "3",
                                                    "parent_name": "科室11111",
                                                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                                    "hospital_name": "医院test2",
                                                    "timestamp": "2020-07-28 11:04:01",
                                                    "update_timestamp": "2020-07-29 06:03:52",
                                                    "type": "Hospital",
                                                    "children": []
                                                }
                                            ]
                                        },
                                        {
                                            "uuid": "9",
                                            "name": "科室22222",
                                            "doctor_num": 0,
                                            "access_id": "",
                                            "parent_uuid": "2",
                                            "parent_name": "科室1111",
                                            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                            "hospital_name": "医院test2",
                                            "timestamp": "2020-07-22 10:38:49",
                                            "update_timestamp": "2020-07-22 10:38:51",
                                            "type": "Hospital",
                                            "children": []
                                        }
                                    ]
                                },
                                {
                                    "uuid": "8",
                                    "name": "科室2222",
                                    "doctor_num": 0,
                                    "access_id": "",
                                    "parent_uuid": "55544",
                                    "parent_name": "科室111",
                                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                    "hospital_name": "医院test2",
                                    "timestamp": "2020-07-22 10:37:40",
                                    "update_timestamp": "2020-07-22 10:37:44",
                                    "type": "Hospital",
                                    "children": []
                                }
                            ]
                        },
                        {
                            "uuid": "7",
                            "name": "科室222",
                            "doctor_num": 0,
                            "access_id": "",
                            "parent_uuid": "ffb2393e-bc30-11ea-a691-000ec6535a9f",
                            "parent_name": "科室11",
                            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                            "hospital_name": "医院test2",
                            "timestamp": "2020-07-22 10:36:56",
                            "update_timestamp": "2020-07-22 10:36:58",
                            "type": "Hospital",
                            "children": [
                                {
                                    "uuid": "cd86dd4c-d0c4-11ea-a1df-0242ac12000c",
                                    "name": "sdgfdyhdfghj",
                                    "doctor_num": 0,
                                    "access_id": "",
                                    "parent_uuid": "7",
                                    "parent_name": "科室222",
                                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                    "hospital_name": "医院test2",
                                    "timestamp": "2020-07-28 11:23:46",
                                    "update_timestamp": "2020-07-28 11:23:46",
                                    "type": "Hospital",
                                    "children": []
                                }
                            ]
                        },
                        {
                            "uuid": "f76d1fba-d0bd-11ea-8d9d-0242ac12000c",
                            "name": "gg3",
                            "doctor_num": 0,
                            "access_id": "",
                            "parent_uuid": "ffb2393e-bc30-11ea-a691-000ec6535a9f",
                            "parent_name": "科室11",
                            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                            "hospital_name": "医院test2",
                            "timestamp": "2020-07-28 10:34:50",
                            "update_timestamp": "2020-07-28 10:34:50",
                            "type": "Hospital",
                            "children": [
                                {
                                    "uuid": "03cfbb6a-d0c2-11ea-92c0-0242ac12000c",
                                    "name": "g4444",
                                    "doctor_num": 0,
                                    "access_id": "5ba7daec-d2df-11ea-83ee-0242ac12000d",
                                    "parent_uuid": "f76d1fba-d0bd-11ea-8d9d-0242ac12000c",
                                    "parent_name": "gg3",
                                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                    "hospital_name": "医院test2",
                                    "timestamp": "2020-07-28 11:03:49",
                                    "update_timestamp": "2020-07-31 03:38:54",
                                    "type": "Hospital",
                                    "children": []
                                },
                                {
                                    "uuid": "10a2c41c-d0be-11ea-8c93-0242ac12000c",
                                    "name": "g4",
                                    "doctor_num": 0,
                                    "access_id": "",
                                    "parent_uuid": "f76d1fba-d0bd-11ea-8d9d-0242ac12000c",
                                    "parent_name": "gg3",
                                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                    "hospital_name": "医院test2",
                                    "timestamp": "2020-07-28 10:35:32",
                                    "update_timestamp": "2020-07-28 10:35:32",
                                    "type": "Hospital",
                                    "children": []
                                },
                                {
                                    "uuid": "6e2b1aee-d0be-11ea-8a20-0242ac12000c",
                                    "name": "g44",
                                    "doctor_num": 0,
                                    "access_id": "",
                                    "parent_uuid": "f76d1fba-d0bd-11ea-8d9d-0242ac12000c",
                                    "parent_name": "gg3",
                                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                    "hospital_name": "医院test2",
                                    "timestamp": "2020-07-28 10:38:09",
                                    "update_timestamp": "2020-07-28 10:38:09",
                                    "type": "Hospital",
                                    "children": [
                                        {
                                            "uuid": "95512f1a-d0c7-11ea-a1df-0242ac12000c",
                                            "name": "sss",
                                            "doctor_num": 0,
                                            "access_id": "",
                                            "parent_uuid": "6e2b1aee-d0be-11ea-8a20-0242ac12000c",
                                            "parent_name": "g44",
                                            "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                                            "hospital_name": "医院test2",
                                            "timestamp": "2020-07-28 11:43:41",
                                            "update_timestamp": "2020-07-28 11:43:41",
                                            "type": "Hospital",
                                            "children": []
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "list_data": {
            "record": [
                {
                    "uuid": "03cfbb6a-d0c2-11ea-92c0-0242ac12000c",
                    "name": "g4444",
                    "doctor_num": 0,
                    "access_id": "5ba7daec-d2df-11ea-83ee-0242ac12000d",
                    "parent_uuid": "f76d1fba-d0bd-11ea-8d9d-0242ac12000c",
                    "parent_name": "gg3",
                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                    "hospital_name": "医院test2",
                    "timestamp": "2020-07-28 11:03:49",
                    "update_timestamp": "2020-07-31 03:38:54",
                    "type": "Hospital"
                },
                {
                    "uuid": "0b309d8e-d0c2-11ea-a54a-0242ac12000c",
                    "name": "gg",
                    "doctor_num": 0,
                    "access_id": "474be748-d161-11ea-8c8d-0242ac12000c",
                    "parent_uuid": "3",
                    "parent_name": "科室11111",
                    "hospital_uuid": "bc59f152-bc2d-11ea-ba33-000ec6535a9f",
                    "hospital_name": "医院test2",
                    "timestamp": "2020-07-28 11:04:01",
                    "update_timestamp": "2020-07-29 06:03:52",
                    "type": "Hospital"
                }
            ],
            "page_index": 1,
            "page_count": 10,
            "has_previous": False,
            "has_next": True
        }
    },
    "token": "",
    "code": 200
}
b = json.dumps({
    "name": 777,
    "value": {
        "value": "还好hhhhhdahdja"
    }
})
# b = json.dumps(qq)
s = b.encode("utf-8")
a = base64.b64encode(s)
print(a.decode("utf-8"))
# c = "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"
c = "eyJuYW1lIjogNzc3LCAidmFsdWUiOiB7InZhbHVlIjogIlx1OGZkOFx1NTk3ZGhoaGhoZGFoZGphIn19"
f = base64.b64decode(c)
# m = f.decode("unicode_escape")
m = f.decode('utf-8')
print(m)
g = json.loads(m)
print(g)
