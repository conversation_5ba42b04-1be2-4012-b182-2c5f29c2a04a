# Generated by Django 2.0.5 on 2020-06-08 16:01

import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0009_alter_user_last_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=30, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('full_name', models.CharField(max_length=255, verbose_name='中文全名')),
                ('last_login_time', models.DateTimeField(auto_now=True, verbose_name='上次登录时间')),
                ('wechat_openid', models.CharField(blank=True, max_length=255, null=True, verbose_name='微信openid')),
                ('phone', models.CharField(blank=True, max_length=255, null=True, verbose_name='电话')),
                ('photo_url', models.CharField(blank=True, max_length=255, null=True, verbose_name='角色照片')),
                ('access_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='权限')),
                ('role_type', models.CharField(blank=True, choices=[('Patient', '病人'), ('Doctor', '医生'), ('company', '公司')], max_length=255, null=True, verbose_name='角色类型')),
                ('is_delete', models.BooleanField(default=False, verbose_name='是否删除账户')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
                'swappable': 'AUTH_USER_MODEL',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Token',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='主键')),
                ('token', models.CharField(blank=True, max_length=255, null=True)),
                ('expire_time', models.IntegerField(blank=True, default=0, null=True, verbose_name='微信Token超时时间')),
                ('time_stamp', models.DateTimeField(auto_now=True, verbose_name='时间戳')),
            ],
            options={
                'verbose_name': 'token验证',
                'verbose_name_plural': 'token验证',
                'db_table': 'token',
            },
        ),
    ]
