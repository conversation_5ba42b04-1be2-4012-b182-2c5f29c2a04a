#! /usr/bin/env python
# encoding: utf8

from django.urls import include, path
from server.user import views as userviews

urlpatterns = [
    # 用户登录
    # path('login/', userviews.Login.as_view()),
    # # 登录验证码
    # path('user/code/', userviews.Code.as_view()),
    # # 用户注册
    # path('user/register/', userviews.Register.as_view()),
    # # 用户删除和修改
    # path('user/detail/<pk>', userviews.UserView.as_view()),
    # 授权码登录接口
    path('authorizationcode/login/', userviews.AuthorizationCodeLogin.as_view()),
    path('authorizationcode/check/', userviews.CheckoutAuthorizationCodeView.as_view()),
    path('admin_login/', userviews.AdminLogin.as_view())

]
