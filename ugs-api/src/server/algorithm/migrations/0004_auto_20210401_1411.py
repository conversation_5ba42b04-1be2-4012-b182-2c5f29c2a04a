# Generated by Django 2.0.5 on 2021-04-01 14:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('algorithm', '0003_auto_20210401_1408'),
    ]

    operations = [
        migrations.CreateModel(
            name='AlgorithmTaskType',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('create_time', models.DateTimeField(auto_now=True, null=True, verbose_name='创建时间')),
                ('task_type', models.CharField(choices=[('1', '串行'), ('2', '并行')], max_length=1, verbose_name='类型')),
                ('comment', models.CharField(blank=True, max_length=255, null=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '算法运行类型记录',
                'verbose_name_plural': '算法运行类型记录',
                'db_table': 'algorithm_task_type',
                'ordering': ['-create_time'],
            },
        ),
    ]
