# Generated by Django 2.0.5 on 2020-06-08 16:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Algorithm_result',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False)),
                ('image_series', models.CharField(max_length=255, verbose_name='序列UUID')),
                ('algorithm_type', models.CharField(max_length=255, verbose_name='算法类型')),
                ('algorithm_result', models.TextField(blank=True, null=True, verbose_name='返回结果')),
                ('mask_url', models.TextField(blank=True, null=True, verbose_name='算法mask')),
                ('index', models.IntegerField(default=0, verbose_name='序列帧数')),
            ],
            options={
                'verbose_name': '算法结果表',
                'verbose_name_plural': '算法结果表',
                'db_table': 'algorithm_result',
                'ordering': ['index'],
            },
        ),
        migrations.CreateModel(
            name='AlgorithmTask',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('series_uid', models.CharField(max_length=255, verbose_name='序列uid')),
                ('user_id', models.CharField(max_length=255, verbose_name='进行算法任务人员')),
                ('algorithm_type', models.CharField(max_length=255, verbose_name='算法类型')),
                ('finish_percent', models.IntegerField(default=0, verbose_name='完成百分比')),
                ('finish_dated', models.DateTimeField(auto_now=True, verbose_name='任务完成时间')),
                ('start_dated', models.DateTimeField(auto_now_add=True, verbose_name='任务开始时间')),
                ('total_task', models.IntegerField(default=0, verbose_name='总体任务次数')),
                ('finish_task', models.IntegerField(default=0, verbose_name='完成任务次数')),
            ],
            options={
                'verbose_name': '算法任务',
                'verbose_name_plural': '算法任务',
                'db_table': 'algorithm_task',
                'ordering': ['-start_dated'],
            },
        ),
        migrations.CreateModel(
            name='AspectMaskColorMap',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('name', models.CharField(max_length=255, verbose_name='aspect分区名称')),
                ('color', models.CharField(blank=True, max_length=255, null=True, verbose_name='aspect分区颜色')),
                ('index', models.IntegerField(default=0, verbose_name='颜色序列')),
                ('update_time', models.DateTimeField(verbose_name='创建时间')),
            ],
            options={
                'verbose_name': 'aspect颜色地图',
                'verbose_name_plural': 'aspect颜色地图',
                'db_table': 'aspect_mask_color_map',
                'ordering': ['index'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('content', models.TextField(verbose_name='消息')),
                ('create_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')),
                ('is_read', models.BooleanField(default=False, verbose_name='是否已读')),
                ('owner', models.CharField(blank=True, max_length=255, null=True, verbose_name='消息拥有者')),
                ('read_date', models.DateTimeField(auto_now=True, null=True, verbose_name='阅读时间')),
            ],
            options={
                'verbose_name': '消息通知',
                'verbose_name_plural': '消息通知',
                'db_table': 'notification',
                'ordering': ['-create_date'],
            },
        ),
        migrations.AddField(
            model_name='algorithm_result',
            name='task',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='algorithm.AlgorithmTask', verbose_name='所属算法任务'),
        ),
    ]
