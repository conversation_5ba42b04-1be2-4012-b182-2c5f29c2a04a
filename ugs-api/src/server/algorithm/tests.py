from django.test import TestCase

# Create your tests here.
import requests


# import json

# a = requests.get(url="http://172.16.1.53:15673/api/queues/%2f/hhh", auth=('admin', 'admin'))
# # s = 0
# # for dd in a.json():
# #     s += dd['messages']
# # print(s)
# data_tem = json.dumps(a.json(), ensure_ascii=False, indent=1).encode('utf-8')
# with open('rddss_data.json', 'wb+') as f:
#     f.write(data_tem)
# print(a.json())


def check_queue_status():
    '''
        :return: (status, queues_status, message)
        status: 当前请求成功失败
        queues_status: 当前是否有排队
        message: 错误消息
    '''
    try:
        response = requests.get(url="http://172.16.1.53:15673/api/queues/%2f/hhh", auth=('admin', 'admin'))
    except Exception:
        return False, None, "rabbitmq server shutdowwn, please restart"
    try:
        data = response.json()
        # comumer服务挂了堆积总数
        if data["messages_ready"] > 0:
            return False, None, "consumer server shutdown, please restart"
        # 消费确认数
        if data["messages_unacknowledged"] > 0:
            return True, False, data["messages_unacknowledged"]
        elif data["messages_unacknowledged"] == 0:
            return True, True, data["messages_unacknowledged"]
        return False, None, "unkown error"
    except Exception:
        return False, None, "data read error"


def getInstance():
    "StudyInstanceUID=1.3.46.670589.33.1.63666207444743647800002.5732398364182397898"
    a = requests.get(url='http://172.16.1.53:4223/api/v1/async/pacslist/'
                         '?type=INSTANCES&'
                         'SeriesInstanceUID=1.3.46.670589.33.1.63666207444743647800002.5732398364182397898'
    # '&StudyInstanceUID=1.2.840.113619.186.80861654864.20180703093632670.435'
                         '&PatientID=')
    print(len(a.json()["data"]))


import json


def delete_instance():
    url = "http://172.16.1.80:8042/modalities/sample/query"
    post_data = {
        "Level": "Study",
        "Query": {
            "StudyInstanceUID": "1.2.840.113619.186.80861654864.20180703093632670.435"
        }
    }
    post_data = json.dumps(post_data)
    a = requests.post(url=url, data=post_data, auth=('orthanc', 'orthanc'))
    print(a.json())
    pass


if __name__ == '__main__':
    # (status, check_status, message) = check_queue_status()
    # print(status, check_status, message)
    # getInstance()
    # delete_instance()
    pass
