#! /usr/bin/env python
# -*- coding: utf-8 -*-
import datetime
import os
import logging

from django.http import FileResponse
from django.utils.encoding import escape_uri_path

from server import settings
from server.async.utils import DockerMonitor
from server.common.views import BaseView
from server.systemconfig.service import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LogDownloadHandler
from server.systemconfig.system_config_utils import SystemConfigUtils

log = logging.getLogger("django")


class LogView(BaseView):
    def get(self, request):
        """
        获取服务列表
        """
        service_path_map_list = []
        for key, value in settings.monitor_list.items():
            if key in settings.UGS_SERVICES_PLATFORM:
                service_path_map_list.append({
                    "serviceCode": key,
                    "serviceName": value,
                    "is_docker": False
                })
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        if GE_CALLBACK_START != "1":
            docker_server_list = []
            try:
                docker_server_list = DockerMonitor().check_containers_server()
            except:
                log.error("docker ip maybe error, please check ip")
            result_list = []
            for docker_server in docker_server_list:
                container_name = docker_server.get("container_name", "")
                if isinstance(container_name, str) and container_name in settings.UGS_SERVICES_THIRD_PARTY:
                    result_list.append({
                        "serviceCode": container_name,
                        "serviceName": docker_server.get("name"),
                        "container_id": docker_server.get("container_id"),
                        "is_docker": True,
                        "status": docker_server.get("status"),
                        "status_error": docker_server.get("status_error"),
                    })

            service_path_map_list.extend(result_list)
        return self.ok(data=service_path_map_list)


class LogQueryView(BaseView):

    def get(self, request):
        """
        根据服务名或者时间来查询指定目标
        """
        request_params = request.GET
        log.info("logQuery > params:{}".format(request_params))
        service_code = request_params.get("serviceCode")
        key_word = request_params.get("keyWord", None)
        start_time = request_params.get("startTime", None)
        end_time = request_params.get("endTime", None)
        page_no = int(request_params.get("pageNo", 1))
        page_size = int(request_params.get("pageSize", 100))
        # 获取当前路径下的所有服务名
        if service_code not in settings.UGS_SERVICES_THIRD_PARTY and service_code not in settings.UGS_SERVICES_PLATFORM:
            return self.fail(code=400, message="服务不存在")
        _now = datetime.datetime.now()
        # 起始时间默认24小时
        start_time = (_now+datetime.timedelta(days=-1)) if not start_time else datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        end_time = _now if not end_time else datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        log_handler = LogQueryHandler(service_code, start_time, end_time, key_word, page_no, page_size)
        result = log_handler.find()
        return self.ok(data="".join(result))


class DownloadView(BaseView):

    def get(self, request):
        """
        根据服务的code获取日志列表
        """
        request_params = request.GET
        log.info("logDownload > params:{}".format(request_params))
        service_code = request_params.get("serviceCode")
        if service_code not in settings.UGS_SERVICES_THIRD_PARTY and service_code not in settings.UGS_SERVICES_PLATFORM:
            return self.fail(code=400, message="服务不存在")
        file_list = LogDownloadHandler(service_code).find_logs()
        return self.ok(data=file_list)

    def post(self, request):
        """
        下载日志文件
        """
        request_body = request.POST
        log.info("logDownload > body:{}".format(request_body))
        service_code = request_body.get("serviceCode")
        file_name = request_body.get('fileName')
        file_path = LogDownloadHandler(service_code).get_path(file_name)
        if not file_path:
            return self.fail(code=400, message="日志不存在")
        response = FileResponse(open(file_path, mode="rb"))
        response["Content-Type"] = "application/octet-stream"
        response["Content-Disposition"] = F"attachment;filename={escape_uri_path(file_name)}"
        return response

