# Generated by Django 2.0.5 on 2022-04-12 12:26

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PDFReport',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('patient_name', models.Char<PERSON>ield(max_length=255, null=True, verbose_name='患者姓名id')),
                ('male', models.IntegerField(null=True, verbose_name='性别；非1表示女性，1=男性')),
                ('age', models.IntegerField(default=0, verbose_name='年龄')),
                ('check_no', models.Char<PERSON>ield(max_length=255, null=True, verbose_name='检查号')),
                ('image_no', models.Char<PERSON>ield(max_length=255, verbose_name='影像号')),
                ('check_datetime', models.DateTimeField(null=True, verbose_name='检查时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': 'PDF 报告信息',
                'verbose_name_plural': 'PDF 报告信息',
                'db_table': 't_pdf_report',
            },
        ),
    ]
