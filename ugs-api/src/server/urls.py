"""server URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from server.ge.views import (GEInterfaceGrantView, GEInterfaceTimeoutView, GEInterfaceInterruptView,
                             DeleteStorageDicomView)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/v1/', include('server.api.urls')),
    path('api/v2/', include('server.api.urls_v2')),
    path('grant/', GEInterfaceGrantView.as_view()),
    path('grant-time/', GEInterfaceTimeoutView.as_view()),
    path('interrupt/', GEInterfaceInterruptView.as_view()),
    path('deleteStorageDicom', DeleteStorageDicomView.as_view()),
    path('ge/v2/', include('server.ge.urls_v2')),
]
