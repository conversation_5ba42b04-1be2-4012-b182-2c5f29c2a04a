import os
import pathlib
import zipfile

import traceback
from django.core.mail import send_mail, EmailMessage, get_connection
from django.core.validators import validate_email
from django.core.exceptions import ValidationError
from smtplib import SMTPException
from server.settings import EMAIL_FROM


# from server.user.models import User


class SendMail:
    """发送各类信息邮件"""

    @staticmethod
    def __validate_email(email):
        """邮件格式验证"""
        try:
            validate_email(email)
            return True
        except ValidationError:
            return False

    @staticmethod
    def __send_email(subject, message, to_emails: list, from_email=None, auth_user=None, auth_password=None) -> bool:
        # 取得有效邮箱列表
        to_emails = list(filter(SendMail.__validate_email, to_emails))
        if from_email is None:
            from_email = EMAIL_FROM
        try:
            send_mail(subject=subject, message="", from_email=from_email, recipient_list=to_emails,
                      auth_user=auth_user,
                      auth_password=auth_password, html_message=message)
        except SMTPException as e:
            print(traceback.format_exc())
            return False
        return True

    @staticmethod
    def send_init_password(to_email: str, init_password: str) -> bool:
        """
        发送初始密码
        @param to_email: 用户邮箱
        @param init_password: 初始密码
        @return: bool
        """
        subject = "【强联智创】用户初始化密码"
        message = f"您账户的初始密码是【{init_password}】，请登录后重置密码。"

        return SendMail.__send_email(subject, message, [to_email])

    @staticmethod
    def send_email(title: str, content: str, to_emails: list, auth_user=None, auth_password=None) -> bool:
        """
        发送邮件
        :param to_emails: 用户邮箱列表
        :param title: 通知标题
        :param content: 通知内容
        :return: bool
        """
        return SendMail.__send_email(title, content, to_emails, auth_user=auth_user, auth_password=auth_password)

    @staticmethod
    def send_email_with_attachment(to_emails: list, title: str, content: str, file_name, file_data):
        """
        发送带附件的邮件
        :param to_emails: 用户邮箱列表
        :param title: 通知标题
        :param content: 通知内容
        :param file_name: 附件名
        :param file_data: 附件数据
        :return:
        """
        to_emails = list(filter(SendMail.__validate_email, to_emails))
        mail = EmailMessage(title, content, to=to_emails, from_email=EMAIL_FROM, )
        mail.attach(file_name, file_data)
        mail.send()

    @staticmethod
    def send_email_image(title: str, content: str, to_emails: list, auth_user=None, auth_password=None, image=None,
                         fail_silently=False):
        """
        邮件内发送图片
        """
        connection = get_connection(
            username=auth_user,
            password=auth_password,
            fail_silently=fail_silently,
        )
        msg = EmailMessage(title, content, to=to_emails, from_email=auth_user, connection=connection)
        msg.content_subtype = "html"
        msg.encoding = "utf-8"
        msg.attach(image)
        try:

            return msg.send(), ""
        except Exception as e:
            print(">>", e)
            return False, str(e)


class FileUtil(object):
    """文件处理工具类"""

    @staticmethod
    def get_unified_path(path: str):
        """
        将文件路径统一成Linux风格

        :param path: 需要被格式化的路径
        :return: 格式化后的路径
        """
        return pathlib.PurePath(path).as_posix() if path else path

    @staticmethod
    def zip_dir(dirname, zip_file_path):
        """
        压缩指定目录

        :param dirname: 被压缩的目录
        :param zip_file_path: 压缩文件路径
        :return: None
        """
        file_list = []
        if os.path.isfile(dirname):
            file_list.append(dirname)
        else:
            for root, dirs, files in os.walk(dirname):
                for name in files:
                    file_list.append(FileUtil.get_unified_path(os.path.join(root, name)))
        zf = zipfile.ZipFile(zip_file_path, "w", zipfile.zlib.DEFLATED)

        for tar in file_list:
            zf_name = tar[len(dirname):]
            zf.write(tar, zf_name)
        zf.close()
        print("generate {} > find {} files in {}".format(zip_file_path, len(file_list), dirname))


if __name__ == "__main__":
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST = "smtp.exmail.qq.com"
    EMAIL_HOST_PASSWORD = "tAHdMaXX2jChuFxs"
    EMAIL_HOST_USER = "<EMAIL>"
    EMAIL_FROM = "强联智创<<EMAIL>>"
    EMAIL_PORT = 465
    EMAIL_USE_SSL = True
    # email=SendMail()
    # email.send_email()
    subject = "test"
    message = "测试"
    send_mail(subject, subject, from_email=EMAIL_FROM, recipient_list=["<EMAIL>"], auth_user=EMAIL_HOST_USER,
              auth_password=EMAIL_HOST_PASSWORD)
