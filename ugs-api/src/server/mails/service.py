#!/usr/bin/python
# -*- coding: utf-8 -*-
# @Author: liu<PERSON>
# @Time  : 2021/11/3 11:23
# @File  : service.py

import json
import os
import traceback
import uuid
import logging
from email.mime.image import MIMEImage

from bson.objectid import ObjectId

from server.algorithm.models import AlgorithmResult, AlgorithmTask
from server.common.mongoConnector import MongoDB
from server.common.utils import PACSUtil
from server.mails.models import MailConfigModel, MailHistoryModel, MailAutoModel
from server.mails.utils import SendMail
from ..common.code import Const
from ..series.models import Series
from ..study.models import Study
from ..systemconfig.system_config_utils import SystemConfigUtils

log = logging.getLogger("django")


class SendMailByStudyUID(object):
    def __init__(self):
        self.send_mail_address = None
        self.send_password = None
        self.receive = []
        self.mongo_id = None
        self.error = None

    def get_mail_config(self):
        """
        查询邮箱配置信息
        """
        send_queryset = MailConfigModel.objects.filter(is_delete=False, type="send")
        if not send_queryset:
            self.error = "send Email address is null"
            return False
        else:
            self.send_mail_address = send_queryset.first().address
            self.send_password = send_queryset.first().password
        receive_queryset = MailConfigModel.objects.filter(is_delete=False, type="receive")
        if not receive_queryset:
            self.error = "receive Email address is null"
            return False
        else:
            self.receive = [i["address"] for i in receive_queryset.values("address")]
            return True

    def get_series(self, study, algorithm_type):
        """
        根据study和算法类型，查询series，取最新一条结果
        """

        study = Study.objects.filter(study_instance_uid=study).first()
        if not study:
            self.error = "study not found by study:{} and algorithm_type:{}".format(study, algorithm_type)
            return False, None
        series_uid_list = [series.series_instance_uid for series in study.series_set.all()]
        queryset = AlgorithmTask.objects.filter(series_uid__in=series_uid_list,
                                     algorithm_type=algorithm_type).order_by("-finish_dated")
        if not queryset:
            self.error = "get_series this data is null by study:{} and algorithm_type:{}".format(study, algorithm_type)
            return False, None
        return True, queryset.first().series_uid

    def get_result_by_series(self, series, algorithm_type):
        """
        根据series查询mongo结果id，取最新一条
        """
        queryset = AlgorithmResult.objects.filter(image_series=series, algorithm_type=algorithm_type).order_by(
            "-create_time")
        # queryset = Algorithm_result.objects.filter(algorithm_type=series)
        if not queryset:
            self.error = "get algorithm result is null by series:{} and algorithm_type:{}".format(series,
                                                                                                  algorithm_type)
            return False, None
        else:
            result = queryset.first()
            return True, result.algorithm_result

    def get_mongo_result(self, algorithm_result):
        """
        获取mongo结果
        """
        mongodb = MongoDB()
        results = mongodb.query(ObjectId(algorithm_result), 'algorithm', '_id')
        dict_check = {}
        if results:
            self.mongo_id = algorithm_result
            for i in results:
                algorithm_content = i.get('result', '')
                dict_check = json.loads(algorithm_content)
                # print(">>>>>>>>>>>", dict_check)
        else:
            self.error = "get mongo result is null by objectId :{}".format(algorithm_result)
        return dict_check

    def get_send_mail_address(self):
        """
        获取发件地址
        """
        return self.send_mail_address

    def get_send_password(self):
        """
        获取发件地址密码
        """
        return self.send_password

    def get_receive(self):
        """
        获取收件地址 list
        """
        return self.receive

    def get_error(self):
        """
        获取错误信息
        """
        return self.error

    @staticmethod
    def delete_extra_zero(num):
        """
        删除小数点后多余的0

        :param num: 数字
        :return:
        """

        if isinstance(num, str):
            return num
        num = '{:g}'.format(num)
        # 含小数点转float否则int
        num = float(num) if '.' in num else int(num)
        return num

    def cta_template(self, data, tags):
        """
        cta邮件模板
        TODO 支持文字，图片未支持
        """
        PatientAge = tags.get("PatientAge", "")
        PatientBirthDate = tags.get("PatientBirthDate", "")
        PatientID = tags.get("PatientID", "")
        PatientName = tags.get("PatientName", "")
        PatientWeight = tags.get("PatientWeight", "")
        # study = tags.get("StudyInstanceUID", "")
        template = """
        <div>
        <div>
        <h4>您好：</h4>
        <h4>患者{}CTA数据已评估完成</h4>
        <br/>
        <h4>基本信息描述</h4>
        <p>患者姓名：{}</p>
        <p>患者ID：{}</p>
        <p>患者年龄：{}</p>
        <p>患者体重：{}</p>
        <p>患者生日：{}</p>
         <br/> 
        <h4>CTA评估</h4>
        <p>{}</p>
        <p>（侧支循环结果为0-3分，0分代表侧支循环较差，3分代表侧支循环较好）</p>
        <br/>
         <br/>
         <br/>
        </div>
        <img src="cid:logo">
        </div>
               """
        if "score" not in data:
            self.error = "make cta_template fail , tan score is null by objectID:{}".format(self.mongo_id)
            return False, None
        tan_score = data.get("score")
        cta_result = F"Tan评分为{tan_score}分"
        if tan_score != 3:
            cta_result = "结果显示右侧可能存在病变，"+cta_result
        template = template.format(PatientName, PatientName, PatientID, PatientAge, PatientWeight,
                                   PatientBirthDate, cta_result)
        return True, template


    def aspects_template(self, data, tags):
        """
        aspects模板
        TODO 只支持文字
        """
        PatientAge = tags.get("PatientAge", "")
        PatientBirthDate = tags.get("PatientBirthDate", "")
        PatientID = tags.get("PatientID", "")
        PatientName = tags.get("PatientName", "")
        PatientWeight = tags.get("PatientWeight", "")

        template = """
        <div>
        <div>
        <h4>您好：</h4>
        <h4>患者{}NCCT数据已评估完成</h4>
        <br/>
        <h4>基本信息描述</h4>
        <p>患者姓名：{}</p>
        <p>患者ID：{}</p>
        <p>患者年龄：{}</p>
        <p>患者体重：{}</p>
        <p>患者生日：{}</p>
         <br/>
        
        <h4>NCCT评估</h4>
        <p>ASPECT评分：{}</p>
        <br/>
         <br/>
         <br/>
        </div>
        <img src="cid:logo">
        </div>
        """
        template_1 = """
         <div>
        <div>
        <h4>您好：</h4>
        <h4>患者{}NCCT数据已评估完成</h4>
        <br/>
        <h4>基本信息描述</h4>
        <p>患者姓名：{}</p>
        <p>患者ID：{}</p>
        <p>患者年龄：{}</p>
        <p>患者体重：{}</p>
        <p>患者生日：{}</p>
         <br/>
        
        <h4>NCCT评估</h4>
        <p>出血：出血部位{}，出血体积{}ml</p>
        <p>ASPECT评分：{}</p>
        <br/>
         <br/>
         <br/>
        </div>
        <img src="cid:logo">
        </div>
        """
        score = data.get("infarct_result", None)
        score_left = data.get("frontCycleScoreLeft", "")  # 前循环左侧评分

        score_right = data.get("frontCycleScoreRight", "")  # 前循环右侧评分
        score_text = F"{score}分" if score else F"左侧{score_left}分，右侧{score_right}分"
        # post_score = data.get("postCycleScore", "")  # 后循环评分
        # ichVolume = data.get("ichVolume", None)  # 出血

        template = template.format(PatientName, PatientName, PatientID, PatientAge, PatientWeight,
                                   PatientBirthDate, score_text)
        return True, template

    def ctp_template(self, data, tags):
        """
        ctp邮件模板
        TODO 只支持文字
        """
        PatientAge = tags.get("PatientAge", "")
        PatientBirthDate = tags.get("PatientBirthDate", "")
        PatientID = tags.get("PatientID", "")
        PatientName = tags.get("PatientName", "")
        PatientWeight = tags.get("PatientWeight", "")
        # study = tags.get("StudyInstanceUID", "")

        # msg = MIMEMultipart()
        # msg_str = """
        # <p>send email with inside picture</p>
        # <img src="cid:123">
        # """

        template = """
        <div>
        <div>
        <h4>您好：</h4>
        <h4>患者{}CTP数据已评估完成</h4>
        <br/>
        <h4>基本信息描述</h4>
        <p>患者姓名：{}</p>
        <p>患者ID：{}</p>
        <p>患者年龄：{}</p>
        <p>患者体重：{}</p>
        <p>患者生日：{}</p>
         <br/>
        <h4>CTP评估</h4>
        <p>低灌注体积（Tmax>6.0s的区域）为（{}）ml </p>
        <p>核心梗死体积（rCBF<30%的区域）为（{}）ml</p>
        <p>低灌和核心梗死不匹配体积（{}）ml</p>
        <p>不匹配比（{}）</p>
         <br/>
         <br/>
         <br/>
        </div>
        <img src="cid:logo">
        </div>
        """
        v_cbf = round(data.get("v_cbf", '')[0], 2) if data.get("v_cbf", '') else 0
        # 低灌注体积Tmax>6s取Tmax第3值
        tmax = round(data.get("v_TMax", '')[2], 2) if data.get("v_TMax", 0) else 0
        # 低灌和核心梗死不匹配体积
        TMaxcbf = round(tmax - v_cbf, 2)
        ratio = "Inf"
        if tmax and v_cbf:
            try:
                ratio = round(tmax / v_cbf, 2)
            except Exception:
                log.warning(traceback.format_exc())
        # path = LOCAL_WEB_SERVER_HOST + ":" + str(LOCAL_WEB_SERVER_PORT) + "/read/" + str(study)
        template = template.format(PatientName, PatientName, PatientID, PatientAge, PatientWeight,
                                   PatientBirthDate, self.delete_extra_zero(tmax), self.delete_extra_zero(v_cbf),
                                   self.delete_extra_zero(TMaxcbf), self.delete_extra_zero(ratio))
        return True, template

    def get_tags(self, study, series=None):
        """
        获取tags信息
        """
        _pacs = PACSUtil()
        data = {}
        if study:
            patient_info = {"StudyInstanceUID": study,
                            "PatientAge": "",
                            "SeriesInstanceUID": "",
                            "PatientBirthDate": '',
                            "PatientID": "",
                            "Patient'sName": "",
                            "PatientWeight": ""
                            }
            page_info, series_list, status = _pacs.split_page_patient(patient_info)
        if series:
            patient_info = {"SeriesInstanceUID": series,
                            "PatientAge": "",
                            "StudyInstanceUID": "",
                            "PatientBirthDate": '',
                            "PatientID": "",
                            "PatientName": "",
                            "PatientWeight": ""
                            }

            page_info, series_list, status = _pacs.split_page_patient(patient_info)
        if page_info and page_info.get("record", None):
            data = page_info['record'][0]
        return data

    @staticmethod
    def add_imag():
        """读取图片"""
        image_path = os.path.join(os.path.abspath(os.path.dirname(os.path.dirname(__file__))), "static/img/logo.png")
        with open(image_path, 'rb') as f:
            msg_image = MIMEImage(f.read())
        msg_image.add_header("Content-ID", "logo")
        return msg_image

    def __get_ctp_merge_result(self, study_instance_uid):
        study = Study.objects.filter(study_instance_uid=study_instance_uid).first()
        if not study or study.toshiba:
            return None
        series_queryset = Series.objects.filter(study__id=study.id, type=Const.ALGORITHM_TYPE_CTP).order_by(
            "gmt_modified")
        if len(series_queryset) <= 1:
            return None
        result_one = None
        result_two = None
        flag, _id = self.get_result_by_series(series_queryset[0].series_instance_uid, Const.ALGORITHM_TYPE_CTP)
        if flag:
            result_one = self.get_mongo_result(_id)
        if not result_one:
            return None
        flag, _id = self.get_result_by_series(series_queryset[1].series_instance_uid, Const.ALGORITHM_TYPE_CTP)
        if flag:
            result_two = self.get_mongo_result(_id)
        if not result_two:
            return None
        total_tmax = []
        total_cbf = []
        for index in range(len(result_one["v_TMax"])):
            total_tmax.append(result_one["v_TMax"][index] + result_two["v_TMax"][index])
        for index in range(len(result_one["v_cbf"])):
            total_cbf.append(result_one["v_cbf"][index] + result_two["v_cbf"][index])
        return dict(v_TMax=total_tmax, v_cbf=total_cbf)

    def send_mail_by_study(self, study_instance_uid, algorithm_type, series_instance_uid=None):
        """
        发送邮件
        study_instance_uid: Study UID
        algorithm_type: 算法类型
        series_instance_uid: Series UID
        """
        theme = "UGuard处理结果"
        record_history = RecordSendMailHistory()
        # 1 查询邮箱配置
        config_status = self.get_mail_config()
        # config_status = False
        # self.receive = None
        if not config_status:
            record_history.record(send_address=self.send_mail_address,
                                  receive_address=str(self.receive) if self.receive else "",
                                  object_id=None,
                                  message=self.get_error(), theme=theme, send_status=0)
            return False, self.get_error()
        # 获取image_series
        study = Study.objects.filter(study_instance_uid=study_instance_uid).first()
        if study.toshiba and algorithm_type == "ctp":
            # 东芝数据
            image_series = study_instance_uid
        else:
            # 非东芝数据
            if not series_instance_uid:
                series_status, image_series = self.get_series(study=study_instance_uid, algorithm_type=algorithm_type)
                if not series_status:
                    record_history.record(send_address=self.send_mail_address, receive_address=str(self.receive),
                                          object_id=None,
                                          message=self.get_error(), theme=theme, send_status=0)
                    return False, self.get_error()
            else:
                image_series = series_instance_uid
        result_status, mongo_id = self.get_result_by_series(series=image_series, algorithm_type=algorithm_type)
        if not result_status:
            record_history.record(send_address=self.send_mail_address, receive_address=str(self.receive),
                                  object_id=None,
                                  message=self.get_error(), theme=theme, send_status=0)
            return False, self.get_error()
        result_data = self.get_mongo_result(mongo_id)
        if not result_data:
            record_history.record(send_address=self.send_mail_address, receive_address=str(self.receive),
                                  object_id=None,
                                  message=self.get_error(), theme=theme, send_status=0)
            return False, self.get_error()
        # try:
        #     tags = self.get_tags(study=study_instance_uid, series=series)
        # except Exception as e:
        #     print(e)
        #     tags = {}
        tags = dict(PatientID=study.patient_id, PatientName=study.patient_name, PatientAge=study.patient_age,
                    PatientWeight=study.patient_weight, PatientBirthDate=study.patient_birthdate)
        if algorithm_type == "aspects":
            status, template = self.aspects_template(result_data, tags)
        elif algorithm_type == "ctp":
            # 检查合并结果
            ctp_report_merge = int(SystemConfigUtils().getConfigValue("ctpReportMerge", "2"))
            if ctp_report_merge == 1:
                merge_result = self.__get_ctp_merge_result(study_instance_uid)
                log.info("get merge result:{}".format(merge_result))
                if merge_result:
                    result_data = merge_result
            status, template = self.ctp_template(result_data, tags)
        elif algorithm_type == "cta":
            status, template = self.cta_template(result_data, tags)
        else:
            status = False
            template = ""
            self.error = "algorithm_type:{} no in ['aspects','ctp','cta'] ".format(algorithm_type)
        if not status:
            record_history.record(send_address=self.send_mail_address, receive_address=str(self.receive),
                                  object_id=None,
                                  message=self.get_error(), theme=theme, send_status=0)
            return False, self.get_error()
        email = SendMail()
        to_emails = self.get_receive()
        auth_user = self.get_send_mail_address()
        auth_password = self.get_send_password()
        msg_image = self.add_imag()
        send_status, send_message = email.send_email_image(title=theme, content=template, to_emails=to_emails,
                                                           auth_user=auth_user,
                                                           auth_password=auth_password, image=msg_image)
        if not send_status:
            if not send_message:
                send_message = "发送邮件失败"
            record_history.record(send_address=self.send_mail_address, receive_address=str(self.receive),
                                  object_id=template,
                                  message=send_message, theme=theme, send_status=0)
            return False, send_message
        record_history.record(send_address=self.send_mail_address, receive_address=str(self.receive),
                              object_id=template,
                              message=None, theme=theme, send_status=1)
        return True, None


class RecordSendMailHistory(object):
    """
    记录发送历史
    """

    @staticmethod
    def record(send_address, receive_address, object_id, message, theme, send_status):
        """
        记录发送历史
        TODO 发送内容未存到mongo内，目前只发送文字，发信息太长可能会超出255
        """
        record_data = {
            "uuid": str(uuid.uuid1()),
            "send_address": send_address,
            "receive_address": receive_address,
            "send_status": send_status,
            "object_id": object_id,
            "message": message,
            "theme": theme
        }
        try:
            MailHistoryModel.objects.create(**record_data)
        except Exception as e:
            print(traceback.format_exc())
            pass

