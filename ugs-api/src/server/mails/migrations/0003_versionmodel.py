# Generated by Django 2.0.5 on 2021-11-09 17:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mails', '0002_auto_20211109_1641'),
    ]

    operations = [
        migrations.CreateModel(
            name='VersionModel',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('server_type', models.CharField(max_length=255, verbose_name='服务类型')),
                ('version', models.CharField(blank=True, max_length=255, null=True, verbose_name='版本')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_timestamp', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('is_delete', models.<PERSON><PERSON>anField(default=False, verbose_name='是否删除')),
            ],
            options={
                'verbose_name': '版本信息',
                'verbose_name_plural': '版本信息',
                'db_table': 'version',
            },
        ),
    ]
