from django.db import models
from server.common.trans_time import transport_time_fmt

MAX_LENGTH = 255


# Create your models here.
class MailConfigModel(models.Model):
    STATUS_CHOICES = (("send", '发信箱'), ("receive", '收信箱'))

    uuid = models.CharField(primary_key=True,
                            verbose_name='uuid',
                            max_length=MAX_LENGTH)
    address = models.CharField(verbose_name='邮箱地址',
                               max_length=MAX_LENGTH,
                               null=True, blank=True
                               )
    password = models.CharField(verbose_name='密码',
                                max_length=MAX_LENGTH,
                                null=True, blank=True
                                )
    type = models.CharField(verbose_name='类型',
                            choices=STATUS_CHOICES,
                            max_length=MAX_LENGTH,
                            default="receive")
    timestamp = models.DateTimeField(verbose_name='创建时间',
                                     auto_now_add=True)
    update_timestamp = models.DateTimeField(verbose_name='更新时间',
                                            auto_now=True)
    comment = models.TextField(verbose_name='备注',
                               null=True, blank=True)
    is_delete = models.BooleanField(verbose_name='是否删除',
                                    default=False,
                                    blank=True)

    class Meta:
        db_table = "mail_config"
        verbose_name = "邮箱配置表"
        verbose_name_plural = "邮箱配置表"

    def __str__(self):
        return self.address

    def to_dict(self):
        return dict(
            uuid=self.uuid,
            address=self.address,
            password=self.password,
            type=self.type,
            timestamp=transport_time_fmt(self.timestamp, is_fmt=True) if self.timestamp else "",
            update_timestamp=transport_time_fmt(self.update_timestamp, is_fmt=True) if self.update_timestamp else "",
            comment=self.comment,
            is_delete=self.is_delete
        )


class MailHistoryModel(models.Model):
    STATUS_CHOICES = ((0, '失败'), (1, '成功'))
    uuid = models.CharField(primary_key=True,
                            verbose_name='uuid',
                            max_length=MAX_LENGTH)
    send_address = models.CharField(verbose_name='发件地址',
                                    max_length=MAX_LENGTH,
                                    null=True, blank=True)
    receive_address = models.TextField(verbose_name='收件地址',
                                       null=True, blank=True)
    send_time = models.DateTimeField(verbose_name='发送时间',
                                     auto_now_add=True)
    send_status = models.IntegerField(verbose_name='发送状态',
                                      choices=STATUS_CHOICES, default=0)
    object_id = models.TextField(verbose_name='发送内容',
                                 null=True, blank=True)
    message = models.CharField(verbose_name='发送错误信息',
                               max_length=MAX_LENGTH,
                               null=True, blank=True)
    theme = models.CharField(verbose_name='主题',
                             max_length=MAX_LENGTH,
                             null=True, blank=True)
    timestamp = models.DateTimeField(verbose_name='创建时间',
                                     auto_now_add=True)
    update_timestamp = models.DateTimeField(verbose_name='更新时间',
                                            auto_now=True)
    comment = models.TextField(verbose_name='备注',
                               null=True, blank=True)
    is_delete = models.BooleanField(verbose_name='是否删除',
                                    default=False,
                                    blank=True)

    class Meta:
        db_table = "mail_history"
        verbose_name = "邮件发送历史表"
        verbose_name_plural = "邮件发送历史表"

    def __str__(self):
        return self.uuid


class MailAutoModel(models.Model):
    AUTO_CHOICES = ((0, '手动'), (1, '自动'))
    METHOD_CHOICES = ((0, "文字"), (1, "图片"), (2, "文字+图片"))
    uuid = models.CharField(primary_key=True,
                            verbose_name='uuid',
                            max_length=MAX_LENGTH)
    type = models.CharField(verbose_name='类型',
                            max_length=MAX_LENGTH)
    status = models.CharField(verbose_name='状态',
                              max_length=MAX_LENGTH,
                              null=True, blank=True)
    auto = models.IntegerField(verbose_name='自动发送',
                               choices=AUTO_CHOICES, default=0)
    method = models.IntegerField(verbose_name='发送类型',
                                 choices=METHOD_CHOICES, default=0)
    timestamp = models.DateTimeField(verbose_name='创建时间',
                                     auto_now_add=True)
    update_timestamp = models.DateTimeField(verbose_name='更新时间',
                                            auto_now=True)
    comment = models.TextField(verbose_name='备注',
                               null=True, blank=True)
    is_delete = models.BooleanField(verbose_name='是否删除',
                                    default=False,
                                    blank=True)

    class Meta:
        db_table = "mail_auto"
        verbose_name = "邮件自动发送表"
        verbose_name_plural = "邮件自动发送表"

    def __str__(self):
        return self.uuid


class VersionModel(models.Model):
    uuid = models.CharField(primary_key=True,
                            verbose_name='uuid',
                            max_length=MAX_LENGTH)
    server_type = models.CharField(verbose_name='服务类型',
                                   max_length=MAX_LENGTH)
    version = models.CharField(verbose_name='版本',
                               max_length=MAX_LENGTH,
                               null=True, blank=True)
    timestamp = models.DateTimeField(verbose_name='创建时间',
                                     auto_now_add=True)
    update_timestamp = models.DateTimeField(verbose_name='更新时间',
                                            auto_now=True)
    comment = models.TextField(verbose_name='备注',
                               null=True, blank=True)
    is_delete = models.BooleanField(verbose_name='是否删除',
                                    default=False,
                                    blank=True)

    class Meta:
        db_table = "version"
        verbose_name = "版本信息"
        verbose_name_plural = "版本信息"

    def __str__(self):
        return self.uuid
