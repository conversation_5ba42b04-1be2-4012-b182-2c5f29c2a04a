# encoding: utf8

DATABASES_MAPPING_APPS = {
    'user': 'default',
    'admin': 'default',
    'account': 'default',
    'sessions': 'default',
    'contenttypes': 'default',
    'auth': 'default',
    'async': 'default',
    'report': 'default',
    'study': 'default',
    'series': 'default',
    'material': 'algorithm',
    'algorithm': 'algorithm',
    'django_celery_beat': 'algorithm',
    "mails": "default",
    "systemconfig": 'default'
}


class DataBaseRouter:
    @staticmethod
    def db_for_read(model, **hints):
        if hasattr(model, '_meta'):
            if getattr(model, '_meta').app_label in DATABASES_MAPPING_APPS:
                return DATABASES_MAPPING_APPS.get(
                    getattr(model, '_meta').app_label, None
                )
        return None

    @staticmethod
    def db_for_write(model, **hints):
        if hasattr(model, '_meta'):
            if getattr(model, '_meta').app_label in DATABASES_MAPPING_APPS:
                return DATABASES_MAPPING_APPS.get(
                    getattr(model, '_meta').app_label, None
                )
        return None

    @staticmethod
    def all_relation(obj1, obj2, **hints):
        obj1 = DATABASES_MAPPING_APPS.get(getattr(obj1, '_meta').app_label, None)
        obj2 = DATABASES_MAPPING_APPS.get(getattr(obj2, '_meta').app_label, None)
        if obj1 and obj2:
            if obj1 == obj2:
                return True
            return False
        return None

    @staticmethod
    def allow_migrate(db, app_label, model_name=None, **hints):
        if db in DATABASES_MAPPING_APPS.values():
            return DATABASES_MAPPING_APPS.get(app_label) == db
        elif app_label in DATABASES_MAPPING_APPS:
            return False
        return None
