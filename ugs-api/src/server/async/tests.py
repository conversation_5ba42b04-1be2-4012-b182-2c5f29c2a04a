# Create your tests here.
import os

import requests

#
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")
import django
import importlib
import math

#
# #
django.setup()
from django.db.models import Max, Count

qq = importlib.import_module('server.async.models')

import platform
from server.settings import *
import docker
import traceback
import datetime
import re
import server.settings as settings
from server.algorithm.models import *
from server.common.utils import PACSUtil
from bson.objectid import ObjectId

requests.packages.urllib3.disable_warnings()

print(PACS_SERVER_IP)
print(DB_HOST)
print(DB_DOCKER_EXPOSE_PORT)
monitor_config = {
    "3310": {
        "name": "mysql",
        "chinese_name": "mysql数据库服务",
        "contatner_name": "cloud_platform_mysql",
        "status": False,
        "port": "3310"
    },
    str(LOCAL_PORT): {
        "name": "pacs_local_server",
        "chinese_name": "pacs被动接收服务",
        "contatner_name": "cloud_platform_pacs_local_server",
        "status": False,
        "port": str(LOCAL_PORT)
    },
    "4224": {
        "name": "nginx",
        "chinese_name": "工作站前端服务",
        "contatner_name": "nginx_cloud_local_platform",
        "status": False,
        "port": "4224"
    },
    "4223": {
        "name": "cloud_platform_api",
        "chinese_name": "工作站后台服务",
        "contatner_name": "cloud_platform_api",
        "status": False,
        "port": "4223"
    },
    "15673": {
        "name": "cloud_platform_rabbitmq",
        "chinese_name": "rabbitmq消息中间件服务",
        "contatner_name": "cloud_platform_rabbitmq",
        "status": False,
        "port": "15673"
    },
    "8044": {
        "name": "cloud_platform_pacs",
        "chinese_name": "PACS服务",
        "contatner_name": "cloud_platform_pacs",
        "status": False,
        "port": "8044"
    },
    "27018": {
        "name": "cloud_platform_mongodb",
        "chinese_name": "MONGODB数据库服务",
        "contatner_name": "cloud_platform_mongodb",
        "status": False,
        "port": "27018"
    }
}


def monitor():
    if platform.system() == "Windows":
        os.environ["PATH"] = "C:\\Program Files (x86)\\Nmap;" + os.environ["PATH"]
    import nmap

    state = ("open", "closed", "filtered", "unfiltered", "open|filtered", "closed|filtered")
    reason = ("syn-ack",)
    nm = nmap.PortScannerYield()
    data = []
    ports = ",".join(monitor_config.keys())
    for ip, result in nm.scan('***********', ports=ports, arguments="-sS"):
        print(ip)
        scan_info = result.get("scan", None)
        if scan_info:
            tcp_info = scan_info[ip].get("tcp", {})
            for key, value in tcp_info.items():
                config_dict = monitor_config.get(str(key), {})
                if config_dict:
                    config_dict["status"] = True if value["state"] == "open" else False
                    data.append(config_dict)

    print(data)


def callback_result(host, scan_result):
    print('------------------')
    print(host, scan_result)


class DockerMonitor:
    def __init__(self, ip=settings.monitor_ip, port=settings.monitor_port,
                 monitor_list=list(settings.monitor_list.keys())):
        ls_config = docker.tls.TLSConfig(
            client_cert=(settings.client_crt_path, settings.client_key_path)
        )
        self.client = docker.DockerClient(base_url="tcp://{ip}:{port}".format(ip=ip, port=str(port)), tls=ls_config)
        self.monitor_list = monitor_list

    def check_containers_server(self, filters=dict()):
        container_list = None
        data = []
        if self.monitor_list:
            print("hhhhhh")
            container_list = self.client.containers.list(filters=dict(name=self.monitor_list), all=True)
        if filters:
            container_list = self.client.containers.list(filters=filters, all=True)
        if not container_list:
            container_list = self.client.containers.list(all=True)
        for container in container_list:
            attrs_dict = container.attrs
            data.append(dict(status=attrs_dict["State"]["Running"],
                             container_name=attrs_dict["Name"].strip("/"),
                             restart_count=attrs_dict["RestartCount"],
                             container_port=attrs_dict["NetworkSettings"]["Ports"],
                             status_error=attrs_dict["State"]["Error"],
                             container_id=attrs_dict["Id"]
                             ))
        return data

    def restart_containers_server(self, name):
        if name:
            try:
                _container = self.client.containers.get(container_id=name)
            except Exception:
                print(traceback.format_exc())
                return False, "该容器名不存在"
            try:
                _container.restart()
            except Exception:
                print(traceback.format_exc())
                return False, "该容器重启失败"
            return True
        return False, "容器名不能为空"

    def get_conntainer_logs(self, name, **kwargs):
        """
        获取服务日志信息
        :param name: 容器id或容器名
        :param kwargs: 以下参数可有可无
           1.timestamps -->bool-->是否显示时间戳
           2.until -->datetime-->获取该时间之前的日志数据
           3.since -->datetime-->获取该时间之后的日志数据
           4.tail -->int -->获取最后多少行数据
        :return:  (generator or str)
        """
        try:
            _container = self.client.containers.get(container_id=name)
        except Exception:
            print(traceback.format_exc())
            return False, "该容器名不存在"
        if kwargs:
            try:
                return _container.logs(**kwargs)
            except Exception:
                print(traceback.format_exc())
                now_time = datetime.datetime.now()
                return _container.logs(timestamps=True, tail=600, until=now_time)
        return _container.logs(tail=600)

    def get_df(self):
        a = self.client.df()
        print(a["Volumes"])
        for key, value in a.items():
            print(key, "----", type(value))
            print()


def get_orthanc():
    from orthanc_rest_client import Orthanc
    from requests.auth import HTTPBasicAuth
    auth = HTTPBasicAuth('orthanc', 'orthanc')
    orthanc = Orthanc('http://172.16.1.80:8042', auth=auth, warn_insecure=False)
    a = orthanc.get_patients()
    # query = {'Level': 'Patient',
    #          'Query': {'PatientName': ''},
    #          }
    # a = orthanc.find(query)
    # a = orthanc.get_patient(a[0])

    # print(a)
    for i in a:
        b = orthanc.get_patient(i)
        # print(b)
        c = b.get("MainDicomTags", "")
        PatientID = c.get("PatientID")
        Name = c.get("PatientName")
        # d = orthanc.get_patient_studies_from_id(PatientID)
        # print(d)
        # for j in d:
        #     print(j)
        p = PACSUtil()
        info = dict(PatientID=PatientID)
        m = p.retrieval_all_patient(info, queryRetrievel="STUDY")
        # print(m)
        if not m:
            print(Name, "****", PatientID)
        # break


def query_callback_table(data_list, query_type="STUDY"):
    query_type_to_field = dict(STUDY=["study_instance_uid", "study_instance_uid__in"],
                               SERIES=["series_instance_uid", "series_instance_uid__in"])
    condition_combine = dict(study_number=Count(query_type_to_field[query_type][0]),
                             sop_orthanc_uuid=Max("sop_orthanc_uuid"),
                             series_instance_uid=Max("series_instance_uid"),
                             is_pushed=Max("is_pushed"),
                             dowload_status=Max("download_status"),
                             patient_name=Max("patient_name"))
    condition = {
        query_type_to_field[query_type][0]: data_list
    }
    data_temp = qq.CallBackDICOM.objects.all().values(query_type_to_field[query_type][0]).annotate(
        **condition_combine).all()

    series_is_pushed_list = []
    for i in data_temp:
        if i.get("is_pushed", None):
            series_is_pushed_list.append(i.get("series_instance_uid", ""))
        else:
            pass

    print(series_is_pushed_list)


def c_find(is_debug=False,
           patient_info={"PatientID": "",
                         "current_index": 1,
                         "per_page_count": 10},
           querylevel="STUDY",
           target_modality="orthanc427"):
    ## test.chenwensheng.19
    url = F"http://172.16.1.80:8042/modalities/{target_modality}/query"
    query_data = dict()
    for i in C_Find_Parameter_List:
        query_data[i] = ""
    current_index = patient_info.pop("current_index", 1)
    per_page_count = patient_info.pop("per_page_count", 10)
    if patient_info:
        query_data.update(patient_info)
    data = dict(Level=querylevel, Query=query_data, Normalize=False)
    # data = {
    #     "Level": "STUDY",
    #     "Query": {
    #         "PatientID": "test.chenwensheng.19",
    #         "PatientName": "*",
    #         "NumberOfSeriesRelatedInstances": ""
    #     },
    #     "Normalize": False
    # }
    response = requests.post(url=url, json=data,
                             auth=("orthanc", "orthanc"))
    response_orthanc_id = response.json()
    return_data = []
    if response_orthanc_id.get("ID", None):
        response_orthanc_id["ID"]
        data_url = F"http://172.16.1.80:8042/queries/{response_orthanc_id['ID']}/answers?expand"
        response = requests.get(url=data_url,
                                auth=("orthanc", "orthanc"))
        response_data = response.json()
        for index, i in enumerate(response_data):
            data_dict = dict()
            data_dict["id"] = index
            for key, value in i.items():
                data_dict[value["Name"]] = value["Value"]
            return_data.append(data_dict)
        if is_debug:
            for i in return_data:
                print(i)
        page_count = math.ceil(len(return_data) / per_page_count) if len(return_data) / per_page_count > 1 else 1
        full_queryset = return_data[(current_index - 1) * per_page_count:current_index * per_page_count]
        return dict(
            has_next=False if len(full_queryset) < per_page_count else True,
            has_previous=True if current_index > 1 else False,
            page_count=page_count,
            record=full_queryset
        )


def orthanc_c_move():
    pass


def get_orthanc_uuid(study_instance_uid="1.2.840.113619.2.94.************.2.829546.1589026.1"):
    if not study_instance_uid:
        return None
    print(settings.ORTHANC_HOST, "----", settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD)
    url = F"http://{settings.ORTHANC_HOST}:8044/tools/find"
    data = {
        "Level": "SERIES",
        "Query": {
            "SeriesInstanceUID": "1.2.826.0.1.3680043.8.498.10029425333590790075252259323515992271"
        },
        "Expand": True
    }
    try:
        response = requests.post(url=url, json=data,
                                 auth=(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD),
                                 timeout=1)
        response_data = response.json()
        # print(response_data)
        series_dict = dict()
        for i in response_data:
            main_dicom_tags = i.get("MainDicomTags", "")
            instances = i.get("Instances", "")
            print("-------------------------")
            print(i)
            if main_dicom_tags and instances:
                seriesinstanceuid = main_dicom_tags.get("SeriesInstanceUID", "")
                if seriesinstanceuid:
                    series_dict[seriesinstanceuid] = instances[0]
    except Exception:
        print(traceback.format_exc())
        return False
    return series_dict


def get_orthanc_dicom(orthanc_instance_uid, ):
    pass


# 异步Scanner

# if __name__ == '__main__':
#     nm = nmap.PortScannerAsync()
#
#     # 扫描参数，第一个是扫描对象，可以是单个IP、网段、IP-IP诸多写法，详细自己查手册或者百度
#     # 第二个是ports参数，同样写法多样
#     # 第三个arguments参数，这个就有讲究了，假如不写这个参数，默认会带一个-sV，然后你扫描一个ip都能等到天荒地老，关于-sV的含义在文后给出作为参考。在这里，我们给一个-sS，或者可以给个空白字符串也是可以的
#     # 第四个是指定回调函数
#     nm.scan('***********', ports='4223,4221,4222,80,8080',arguments="-sS", callback=callback_result)
#
#     # 以下是必须写的，否则你会看到一运行就退出，没有任何的结果
#     while nm.still_scanning():
#         print("sleep")
#         nm.wait(2)
def request_api(url=None, json=None, auth=None, method="get", is_file=False):
    """

    :param url: 请求地址
    :param json: dict
    :param auth: tuple （username，password）
    :param method: 请求类型（get ， post， delete， put）
    :return: dict
    """
    obj = getattr(requests, method, None)
    try:
        response = obj(url=url,
                       json=json,
                       auth=auth)
        if is_file:
            return response.content

        return response.json()
    except Exception:
        print(traceback.format_exc())
        return dict()


def wirte_config_file(config_path=None, wirte_data=None, key="DicomModalities"):
    data = ""
    with open(config_path, 'r') as f:
        for i in f:
            if not re.findall("^/.*|^\*.*", i.strip()):
                data += i
    if not data:
        return False, "orthanc.json file is blank"
    try:
        data_dict = json.loads(data)
    except Exception:
        return False, "json load error"
    if isinstance(wirte_data, list):
        data_dict[key][wirte_data[0]] = wirte_data[1]
    else:
        data_dict[key] = wirte_data
    data_tem = json.dumps(data_dict, ensure_ascii=False, indent=1).encode('utf-8')
    with open(config_path, 'wb+') as f:
        f.write(data_tem)
    return True, None


def orthanc_config_modify(config_path=None, **kwargs):
    """

    :param config_path: orthanc.json file path
    :param kwargs: dict(Host="name", AET="test","Port"=4242)
    :return:
    """
    vardata = kwargs
    modalitys = vardata.pop("alie_name", "")
    if not modalitys:
        return False
    modalitys = modalitys.replace("_", "")
    print(modalitys)
    url = F"http://{settings.ORTHANC_HOST}:8044/modalities/{modalitys}"
    data = vardata
    print(data)
    response_data = request_api(url=url,
                                json=data,
                                auth=(settings.ORTHANC_WADO_USERNAME,
                                      settings.ORTHANC_WADO_PASSWORD),
                                method="put"
                                )
    print(response_data)
    validate_url = F"http://{settings.ORTHANC_HOST}:8044/modalities?expand"
    response_validate_data = request_api(url=validate_url,
                                         auth=(settings.ORTHANC_WADO_USERNAME,
                                               settings.ORTHANC_WADO_PASSWORD)
                                         )
    if not response_validate_data.get(modalitys, None):
        return False, "orthanc配置设置失败"
    wirte_status, message = wirte_config_file(config_path=config_path,
                                              wirte_data=[modalitys,
                                                          [vardata["AET"],
                                                           vardata["Host"],
                                                           vardata["Port"]
                                                           ]
                                                          ])
    return wirte_status, message
def delete_data(start_date, end_date):
    _callback_queryset = qq.CallBackDICOM.objects.filter(timestamp__lt=end_date, timestamp__gt=start_date)
    clear_series_list = []
    clear_study_list = []
    for i in _callback_queryset:
        if i.series_instance_uid not in clear_series_list:
            clear_series_list.append(i.series_instance_uid)
        if i.study_instance_uid not in clear_study_list:
            clear_study_list.append(i.study_instance_uid)
    if _callback_queryset:
        pass
        # _callback_queryset.delete()
    _algorithm_task_queryset = AlgorithmTask.objects.filter(series_uid__in=clear_series_list)
    if _algorithm_task_queryset:
        pass
        #_algorithm_task_queryset.delete()
    _algorithm_result_queryset = AlgorithmResult.objects.filter(image_series__in=clear_series_list)
    clear_mongodb_id_list = []
    for j in _algorithm_result_queryset:
        if j.algorithm_result not in clear_mongodb_id_list:
            clear_mongodb_id_list.append(j.algorithm_result)
    if _algorithm_result_queryset:
        pass
        # _algorithm_result_queryset.delete()
    # 删除mongodb
    print(clear_mongodb_id_list)
    if clear_mongodb_id_list:
        pass
        # _mongodb = MongoDB()
        clear_mongodb_id_list = [ObjectId(m) for m in clear_mongodb_id_list]
        print(clear_mongodb_id_list)
        # _deletemongodb = _mongodb.bulk_delete(condition_list=clear_mongodb_id_list, condition_key="_id",
        #                                       table="algorithm")


if __name__ == '__main__':

    delete_data(start_date="2021-04-01 00:00:00", end_date="2021-04-12 00:00:00")
    # vardata = dict(alie_name="target_moda",
    #                AET="songtesttest",
    #                Host="***********",
    #                Port=4262,
    #                config_path="D:\\code\\upixel-station-backend\\server\\async\\tls\\orthanc.json")
    # a = orthanc_config_modify(**vardata)
    # print(a)
    # a = qq.StudyViewModel.objects.all()
    # for i in a:
    #     print(i.to_dict())
    # get_orthanc_uuid()
    # a = c_find()
    # print(a)
    # client = docker.DockerClient(base_url="tcp://***********:4683")
    # client.df()
    # a = client.images.list()  # 类似docker images命令，显示image的信息列表
    # a = client.containers.list()
    # # b = client.containers.list(filters=dict(name=["cloud_platform_mysql", "cloud_platform_mongodb"]))  # 类似docker ps命令
    # for i in a:
    #     # print(i.id, "***", i.attrs.get("Name"))
    #     # print(i.attrs)
    #     for key, value in i.attrs.items():
    #         print(key, "****", value)
    #     print("**************************************")
    # # c = client.containers.list(all=True)  # 类似docker ps -a命令
    # #
    # # print(a)
    # # print(b)
    # # print(c)
    # container = client.containers.get("cloud_platform_api")  # 获取daodocker容器，这里container_id 是你要输入的具体容器id
    # # print(container.attrs.get("Id"))
    # start = datetime.datetime.now()
    # time = "2021-04-9"
    # a = datetime.datetime.strptime(time, "%Y-%m-%d")
    # c = datetime.datetime.strptime("2021-04-02", "%Y-%m-%d")
    # end = start - datetime.timedelta(days=3)
    # total_sen = abs((end - start).total_seconds())
    # _mo = DockerMonitor()
    # b = _mo.get_conntainer_logs(timestamps=True, name="cloud_platform_api", until=a, since=c)
    # # b = _mo.get_conntainer_logs(name="cloud_platform_api")
    # # print(b)
    # with open("test.txt", "wb") as f:
    #     f.write(b)

    # # container.restart()
    # # print(container.logs())
    # start = datetime.datetime.now()
    # end = start - datetime.timedelta(days=1)
    # total_sen = abs((end - start).total_seconds())
    # print(total_sen)
    # with open("test.txt", "wb") as f:
    #     f.write(container.logs(timestamps=True, since=int(total_sen), stdout=False))
    # # for k in container.logs(tail=10):
    # #     print(k)
    #
    # a = DockerMonitor()
    # c = a.get_df()
    # import os
    # import re
    # result_cpu = os.popen("df -h")
    # text_cpu = result_cpu.readlines()
    #
    # p = "([0-9]+)G.*([0-9]+)G.*([0-9]+)G.*([0-9]+)%/\n$"
    # for i in text_cpu:
    #     a = re.findall(p, i)
    #     print(a)

    # b = a.restart_containers_server(name="12d252dbae34f93dc7ee1ab02d0bc1987f8a7b2edb688bc4a82471003cbebb86")
    # print(b)
    # # print(c)
    # for k in c:
    #     print(k)
    # get_orthanc()
    # a = qq.CallBackDICOM.objects.values("study_instance_uid").annotate(study_number=Count("series_instance_uid"),
    #                                                                    sop_orthanc_uuid=Max("sop_orthanc_uuid"),
    #                                                                    is_pushed=Max("is_pushed"),
    #                                                                    dowload_status=Max("download_status"),
    #                                                                    patient_name=Max("patient_name")
    #                                                                    ).all()
    # print(a.query)
    # for i in a:
    #     print(i)
    pass
