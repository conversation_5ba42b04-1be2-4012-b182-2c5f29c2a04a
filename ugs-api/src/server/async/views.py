import datetime
import os
import shutil
import threading
import time
import traceback
import uuid

import SimpleIT<PERSON> as sitk
import pydicom
import requests
from bson.objectid import ObjectId
from django.http import JsonResponse, HttpResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from pydicom.dataset import Dataset
from pynetdicom import (AE,
                        QueryRetrievePresentationContexts, )

# from django.conf import settings
from server import settings
from server.image.models import CallBackDICOM
from server.algorithm.models import AlgorithmTask, AlgorithmResult
from server.common.base import check_auth_code, page_split
from server.common.mongoConnector import MongoDB
from server.common.utils import PACSUtil
from server.common.utils_v1 import PACSUtilNew
from server.common.views import BaseView
from server.settings import C_Find_Parameter_List
from .download_dicom_comsumer import DownloadDICOMClient, MQ_CONSUMER_NAME
from .models import PacsServer, RecordTime, ConfigSet
from .utils import DockerMonitor, get_cpu_capacity, \
    delete_orthanc_resources, orthanc_c_find, orthanc_c_move, orthanc_config_modify, \
    wirte_config_file, delete_orthanc_resources_by_series, \
    K8sMonitor
from ..common.code import RetCode, Const
from ..common.remote_api import RabbitMQProducer
from ..report.models import PDFReport
from ..report.post_back import BackReport
from ..series.models import Series, SeriesAlgorithm, FeatureMap
from ..study.models import Study
from ..systemconfig.system_config_utils import SystemConfigUtils


import logging
log = logging.getLogger("django")


# Create your views here.

class PacsListView(BaseView):

    def get(self, request):
        data = request.GET
        response = self.response
        if data:
            query_type = data.get('type', 'PATIENT')
            info_query = dict()
            info_query['PatientName'] = data.get('PatientName', '')
            info_query['QueryRetrieveLevel'] = data.get('QueryRetrieveLevel')
            info_query['PatientID'] = data.get('PatientID', '')
            info_query['PatientSex'] = data.get('PatientSex', '')
            info_query['StudyInstanceUID'] = data.get('StudyInstanceUID', '')
            info_query['SeriesInstanceUID'] = data.get('SeriesInstanceUID', '')
            info_query['PatientAge'] = data.get('PatientAge', '')
            info_query['PatientWeight'] = data.get('PatientWeight', '')
            info_query['PatientBirthDate'] = data.get('PatientBirthDate', '')
            info_query['StudyDescription'] = data.get('StudyDescription', '')
            info_query['StudyDate'] = data.get('StudyDate', '')
            info_query['StudyTime'] = data.get('StudyTime', '')
            info_query['SeriesDescription'] = data.get('SeriesDescription', '')
            info_query['SeriesDate'] = data.get('SeriesDate', '')
            info_query['SeriesTime'] = data.get('SeriesTime', '')
            info_query['SeriesNumber'] = data.get('SeriesNumber', '')
            info_query['InstitutionName'] = data.get('InstitutionName', '')
            info_query['StationName'] = data.get('StationName', '')
            info_query['InstanceNumber'] = data.get('InstanceNumber', '')
            info_query['dataNumberOfFrames'] = data.get('NumberOfFrames', '')
            info_query['SliceThickness'] = data.get('SliceThickness', '')
            info_query['Manufacturer'] = data.get('Manufacturer', '')
            info_query["ManufacturerModelName"] = data.get('ManufacturerModelName', '')
            init = PACSUtil()
            data = init.retrieval_all_patient(info_query, queryRetrievel=query_type)
            log.info("API[pacslist] > get %s images", (len(data) if data else 0))
            if data and query_type == 'INSTANCES' and data[0].get('NumberOfFrames', None):
                if data[0].get('NumberOfFrames', None):
                    raw_data = []
                    i = 0
                    count = data[0].get('NumberOfFrames', None)
                    frame_index_max = count - 1
                    while i < frame_index_max:
                        info = dict()
                        info['PatientName'] = data[0].get('PatientName', '')
                        info['QueryRetrieveLevel'] = data[0].get('QueryRetrieveLevel')
                        info['PatientID'] = data[0].get('PatientID', '')
                        info['PatientSex'] = data[0].get('PatientSex', '')
                        info['StudyInstanceUID'] = data[0].get('StudyInstanceUID', '')
                        info['SeriesInstanceUID'] = data[0].get('SeriesInstanceUID', '')
                        info['PatientAge'] = data[0].get('PatientAge', '')
                        info['PatientWeight'] = data[0].get('PatientWeight', '')
                        info['PatientBirthDate'] = data[0].get('PatientBirthDate', '')
                        info['StudyDescription'] = data[0].get('StudyDescription', '')
                        info['StudyDate'] = data[0].get('StudyDate', '')
                        info['StudyTime'] = data[0].get('StudyTime', '')
                        info['SeriesDescription'] = data[0].get('SeriesDescription', '')
                        info['SeriesDate'] = data[0].get('SeriesDate', '')
                        info['SeriesTime'] = data[0].get('SeriesTime', '')
                        info['SeriesNumber'] = data[0].get('SeriesNumber', '')
                        info['InstitutionName'] = data[0].get('InstitutionName', '')
                        info['StationName'] = data[0].get('StationName', '')
                        info['dataNumberOfFrames'] = data[0].get('NumberOfFrames', '')
                        info['SliceThickness'] = data[0].get('SliceThickness', '')
                        info['InstanceNumber'] = i
                        info['SOPInstanceUID'] = data[0].get('SOPInstanceUID').split('-')[0] + '-{}'.format(i)
                        info['TransferSyntaxUID'] = data[0].get('TransferSyntaxUID', '')
                        info['SOPClassUID'] = data[0].get('SOPClassUID', '')
                        info['PixelSpacing'] = data[0].get('PixelSpacing', '')
                        info['Manufacturer'] = data[0].get('Manufacturer', '')
                        info["ManufacturerModelName"] = data[0].get('ManufacturerModelName', '')
                        raw_data.append(info)
                        i += 1
                    # 防止NumberOfFrame为1时，赋值空数组
                    if raw_data:
                        data = raw_data
            if isinstance(data, list):
                response['status'] = True
                response['code'] = 200
                response['data'] = sorted(data, key=lambda data: data['InstanceNumber'])
                return JsonResponse(response)
            response['message'] = '没有获取数据'
            return JsonResponse(response)
        response['message'] = '请传入查询参数'
        return JsonResponse(response)


class PacsImageList(BaseView):

    def get(self, request):
        data = request.GET
        studyUID = data.get('studyUID', '')
        seriesUID = data.get('seriesUID', '')
        objectUID = data.get('sopUID', '')
        response = self.response
        response['status'] = False
        response['code'] = 403
        if not studyUID:
            response['message'] = '请输入dicom文件的的study的ID'
            return JsonResponse(response)
        if not seriesUID:
            response['message'] = '请输入dicom文件的series的ID'
            return JsonResponse(response)
        if not objectUID:
            response['message'] = '请输入dicom文件的对象的ID'
            return JsonResponse(response)
        init = PACSUtil()
        dicom_response = init.get_instance_wado(studyUID, seriesUID, objectUID)
        if dicom_response:
            return HttpResponse(dicom_response, content_type="application/dicom")
        print(dicom_response.content)
        response['message'] = '请求错误'
        return JsonResponse(response)


class ImagePacsView(View):

    @csrf_exempt
    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs)
        response['Access-Control-Allow-Origin'] = '*'
        return response

    @staticmethod
    def generate_img():
        str_name = str(uuid.uuid1())
        time_stamp = int(time.time())
        name = "%s.%s" % (str_name + str(time_stamp), 'dic')
        file = os.path.join(settings.UPLOAD_PATH, name)
        if not os.path.exists(settings.UPLOAD_PATH):
            os.makedirs(settings.UPLOAD_PATH, exist_ok=True)
        return file, name

    # def post(self, request):
    #     img = request.body
    #     img_file = self.generate_img()
    #     with open(img_file[0], 'wb') as f:
    #         f.write(img)
    #     ds = pydicom.read_file(img_file[0], force=True)
    #     normal = True
    #     if ds.file_meta.TransferSyntaxUID == '1.2.840.10008.1.2.4.70':
    #         normal = False
    #     init = PACSUtil()
    #     file = img_file[0].replace('/', '\\')
    #     series_uid = init.save_dicom_img([img_file[0]], normal)
    #     if series_uid:
    #         data = dict(message='上传成功',
    #                     code=200,
    #                     status=True,
    #                     series_uid=series_uid)
    #         return JsonResponse(data)
    #     data = dict(message='上传失败',
    #                 code=412,
    #                 status=False,
    #                 )
    #     return JsonResponse(data)
    def post(self, request):
        img = request.body
        img_file = self.generate_img()
        print(img_file)
        with open(img_file[0], 'wb') as f:
            f.write(img)
        # ds = pydicom.read_file(img_file[0], force=True)
        # normal = True
        # if ds.file_meta.TransferSyntaxUID == '1.2.840.10008.1.2.4.70':
        #     normal = False
        init = PACSUtilNew()
        status, series_uid = init.save_dicom_img(img_file[0])
        try:
            os.remove(img_file[0])
        except Exception:
            print(traceback.format_exc())
            pass
        if status:
            data = dict(message='上传成功',
                        code=200,
                        status=True,
                        series_uid=series_uid)
            return JsonResponse(data)
        print(status)
        data = dict(message='上传失败',
                    code=412,
                    status=False,
                    )
        return JsonResponse(data)


class ExchangeImageView(BaseView):
    # 获取医学影像资料数据转化成图片
    def get(self, request):
        response = self.response
        response['status'] = False
        response['code'] = 403
        data = request.GET
        series = data.get('series', '')
        instance = data.get('instance', '')
        if instance:
            info = self.get_pacs_info(instance)
            print(info)
            if isinstance(info, dict):
                image_result = self.get_instance_wado(info['StudyInstanceUID'],
                                                      info['SeriesInstanceUID'],
                                                      info['SOPInstanceUID'])
                response['data'] = image_result
                response['message'] = '获取成功'
                response['code'] = 200
                return JsonResponse(response)
        response['message'] = '获取错误'
        return JsonResponse(response)

    def get_pacs_info(self, SOPInstanceUID, queryRetrievel='INSTANCE'):
        self.ae = AE(ae_title=settings.ORTHANC_AET)
        self.ae.requested_contexts = QueryRetrievePresentationContexts
        self.assoc = self.ae.associate(settings.ORTHANC_HOST, settings.ORTHANC_PORT)
        if self.assoc.is_established:
            data = Dataset()
            data.QueryRetrieveLevel = queryRetrievel
            data.StudyInstanceUID = ''
            data.SeriesInstanceUID = ''
            data.SOPInstanceUID = SOPInstanceUID
            value = self.assoc.send_c_find(data, query_model='S')
            if value:
                data_list = []
                for (status, identifier) in value:
                    if status.get('Status', ''):
                        if status.Status == 65280:
                            pacs_content = dict()
                            pacs_content['StudyInstanceUID'] = identifier.get('StudyInstanceUID')
                            pacs_content['SeriesInstanceUID'] = identifier.get('SeriesInstanceUID')
                            pacs_content['SOPInstanceUID'] = identifier.get('SOPInstanceUID')
                            self.assoc.release()
                            return pacs_content
                self.assoc.release()
        else:
            print('pacs connect fail')

    # 获取pacs服务器医学影像数据转化成SimpleITK格式数据
    def get_instance_wado(self, study_uid, series_uid, instance_uid):
        # 利用链接方式访问从pacs服务器获取ct影像资料
        instance_url = 'http://' + settings.ORTHANC_HOST + ':' + str(
            8042) + '/wado?' + 'requestType=WADO' + '&contentType=application/dicom' + \
                       '&studyUID=' + study_uid + '&seriesUID=' + \
                       series_uid + '&objectUID=' + instance_uid
        try:
            name = '{}.dcm'.format(instance_uid)
            file = os.path.join(name)
            if not os.path.exists(file):
                response = requests.get(instance_url, auth=(settings.ORTHANC_WADO_USERNAME,
                                                            settings.ORTHANC_WADO_PASSWORD))
                print(response)
                with open(file, 'wb') as f:
                    f.write(response.content)
            image = sitk.ReadImage(file)
            ds = pydicom.read_file(file)
            slice_location = float(ds[0x20, 0x1041].value)
            result = dict(
                img=sitk.GetArrayFromImage(image).tolist(),
                origin=image.GetOrigin(),
                spacing=image.GetSpacing(),
                direction=image.GetDirection(),
                slice_location=slice_location
            )
            os.remove(name)
            return result
        except SystemError as e:
            print(e)


class MonitorView(BaseView):
    def get(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        if GE_CALLBACK_START == "1":
            data = K8sMonitor().check_containers_server()
        else:
            _docker_monitor = DockerMonitor()
            data = _docker_monitor.check_containers_server()
        response["data"] = data
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)

    def post(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        data = request.POST
        container_id = data.get("container_id", None)
        if not container_id:
            response["message"] = "请传入container_id"
            return JsonResponse(response)
        _docker_monitor = DockerMonitor()
        status, info = _docker_monitor.restart_containers_server(name=container_id)
        if not status:
            response["message"] = info
            return JsonResponse(response)
        response["message"] = "重启成功"
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)


class PACSServerView(BaseView):

    def get(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        pacs_queryset = PacsServer.objects.all()
        if not pacs_queryset:
            response["message"] = "没有记录"
            response["data"] = {}
            response["status"] = True
            response["code"] = 200
            return JsonResponse(response)
        response["data"] = [i.to_dict() for i in pacs_queryset]
        response["status"] = True
        response["code"] = 200
        return JsonResponse(response)

    def post(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        data = request.POST
        pacs_list = data.get("pacs_list", [])
        if not pacs_list:
            response["message"] = "请传入pacs_list参数"
            return JsonResponse(response)
        if not isinstance(pacs_list, list):
            response["message"] = "pacs_list 字段必须为数组"
            return JsonResponse(response)
        if len(pacs_list) != 2:
            response["message"] = "pacs_list 必须两个内容"
            return JsonResponse(response)
        message = PACSServerView.config_pacs_server(pacs_list)
        if message:
            response["message"] = message
            return JsonResponse(response)
        response["status"] = True
        response["code"] = 200
        response["message"] = "更新或创建成功"
        return JsonResponse(response)
    
    @staticmethod
    def config_pacs_server(pacs_list):
        """
        配置dicom源, 代码抽离
        """
        for item in pacs_list:
            aet = item.get("aet", "")
            ip_address = item.get("ip_address", "")
            port = item.get("port", 9999)
            alie_name = item.get("alie_name", "")
            if not aet or not ip_address or not port or not alie_name:
                return "ae title ,ip_address, port, alie_name 设置不能为空"
            if alie_name not in ["target_modality", "target_pacs"]:
                return "alie_name与规定的设置不匹配"
            try:
                item["port"] = int(port)
            except Exception:
                return "端口必须为整数"
            item.pop("id", None)
            alie_name_obj = PacsServer.objects.filter(alie_name=alie_name)
            if alie_name_obj:
                alie_name_obj.update(**item)
            else:
                item["id"] = str(uuid.uuid1())
                PacsServer.objects.create(**item)
            vardata = dict(alie_name=alie_name,
                           AET=aet,
                           Host=ip_address,
                           Port=item["port"],
                           config_path=settings.ORTHANC_CONFIG_FILE)
            status, message = orthanc_config_modify(**vardata)
            if not status:
                return message


class PACSRemoteDownloadView(BaseView):
    @staticmethod
    def check_info(pacs_info):
        if not isinstance(pacs_info, dict):
            return False, "pacs配置信息格式不对"
        target_ip = pacs_info.get("target_ip", None)
        if not target_ip:
            return False, "目标主机地址不能为空"
        target_ae = pacs_info.get("target_ae", None)
        if not target_ae:
            return False, "目标AE不能为空"
        target_port = pacs_info.get("target_port", None)
        if not target_port:
            return False, "目标端口不能为空"
        if not isinstance(target_port, int):
            return False, "目标端口必须为数字"
        return True, None

    def post(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        data = request.POST
        pacs_info = data.get("pacs_info", None)
        SeriesInstanceUID = data.get("SeriesInstanceUID", None)
        StudyInstanceUID = data.get("StudyInstanceUID", None)
        query_type = data.get("query_type", "SERIES")
        status, message = self.check_info(pacs_info)
        if not status:
            response["message"] = message
            return JsonResponse(response)
        if not SeriesInstanceUID:
            response["message"] = "SeriesInstanceUID不能为空"
            return JsonResponse(response)
        if not StudyInstanceUID:
            response["message"] = "StudyInstanceUID不能为空"
            return JsonResponse(response)
        mq_message = dict(pacs_info=pacs_info,
                          patient_info=dict(SeriesInstanceUID=SeriesInstanceUID,
                                            StudyInstanceUID=StudyInstanceUID),
                          queryRetrievel=query_type,
                          is_check=True)
        downloadDICOMClient = DownloadDICOMClient(name=MQ_CONSUMER_NAME, **mq_message)
        downloadDICOMClient.send_message()
        response["data"] = dict(total=0,
                                SeriesInstanceUID=SeriesInstanceUID,
                                StudyInstanceUID=StudyInstanceUID,
                                query_type=query_type)
        # dowload_status, info = download_target_patient_info(pacs_info=pacs_info,
        #                                                     patient_info=dict(SeriesInstanceUID=SeriesInstanceUID,
        #                                                                       StudyInstanceUID=StudyInstanceUID),
        #                                                     queryRetrievel=query_type,
        #                                                     is_check=True
        #                                                     )
        # if not dowload_status:
        #     response["message"] = info.get("message", "默认值没有取到message")
        #     return JsonResponse(response)
        # response["data"] = dict(total=info.get("total", 0), SeriesInstanceUID=SeriesInstanceUID)
        # newThread = threading.Thread(target=download_target_patient_info,
        #                              args=(pacs_info,
        #                                    dict(SeriesInstanceUID=SeriesInstanceUID,
        #                                         StudyInstanceUID=StudyInstanceUID),
        #                                    query_type,
        #                                    False))
        # newThread.start()
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)


class CpuInfoView(BaseView):

    def get(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        response["data"] = get_cpu_capacity()
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)


class ClearPeriodDataView(BaseView):
    @staticmethod
    def delete_data(start_date, end_date):
        _callback_queryset = CallBackDICOM.objects.filter(timestamp__lt=end_date, timestamp__gt=start_date)
        clear_series_list = []
        clear_study_list = []
        for i in _callback_queryset:
            if i.series_instance_uid not in clear_series_list:
                clear_series_list.append(i.series_instance_uid)
            if i.study_instance_uid not in clear_study_list:
                clear_study_list.append(i.study_instance_uid)
        # if _callback_queryset:
        #     _callback_queryset.delete()
        _algorithm_task_queryset = AlgorithmTask.objects.filter(series_uid__in=clear_series_list)
        # if _algorithm_task_queryset:
        #     _algorithm_task_queryset.delete()
        _algorithm_result_queryset = AlgorithmResult.objects.filter(image_series__in=clear_series_list)
        clear_mongodb_id_list = []
        for j in _algorithm_result_queryset:
            if j.algorithm_result not in clear_mongodb_id_list:
                clear_mongodb_id_list.append(j.algorithm_result)
        task_id_list = []
        for t in _algorithm_task_queryset:
            if t.uuid not in task_id_list and t.algorithm_type == "ctp":
                task_id_list.append(t.uuid)
        if _callback_queryset:
            _callback_queryset.delete()
        if _algorithm_task_queryset:
            _algorithm_task_queryset.delete()
        if _algorithm_result_queryset:
            _algorithm_result_queryset.delete()
        PDFReport.objects.filter(study_instance_uid__in=clear_study_list).delete()
        SeriesAlgorithm.objects.filter(series_instance_uid__in=clear_study_list).delete()
        FeatureMap.objects.filter(study_instance_uid__in=clear_study_list).delete()
        # 删除检查和序列
        deleted, rows_count = Series.objects.filter(series_instance_uid__in=clear_series_list).delete()
        log.info("API[clearperioddata] > delete series, result:%s, rows_count:%s", deleted, rows_count)
        deleted, rows_count = Study.objects.filter(study_instance_uid__in=clear_study_list).delete()
        log.info("API[clearperioddata] > delete study, result:%s, rows_count:%s", deleted, rows_count)
        # 删除mongodb
        if clear_mongodb_id_list:
            _mongodb = MongoDB()
            clear_mongodb_id_list = [ObjectId(m) for m in clear_mongodb_id_list]
            _deletemongodb = _mongodb.bulk_delete(condition_list=clear_mongodb_id_list, condition_key="_id",
                                                  table="algorithm")
        # 删除orthanc
        if clear_study_list:
            delete_orthanc_resources(study_list=clear_study_list)
        # 删除数据dcm目录
        for i in clear_series_list:
            folder = os.path.join(settings.DOCKER_CTP_ROOT_DIR, i)
            if os.path.exists(folder):
                shutil.rmtree(folder)
        # 删除ctp报告目录
        if task_id_list:
            for t in task_id_list:
                ctp_folder = os.path.join(settings.DOCKER_DATA_BASE_DIR, "ctp/" + t)
                if os.path.exists(ctp_folder):
                    shutil.rmtree(ctp_folder)
        # 清除检查通知
        notify_message = {"action": "clear", "data": {"StudyInstanceUID": ""}}
        RabbitMQProducer.ws_notify("ws.notify.study_clear", notify_message)

    def get(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        data = request.GET
        period = data.get("period", "-")
        period_list = period.split("-")
        if not all(period_list):
            response["message"] = "开始日期或者结束日期为空"
            return JsonResponse(response)
        if len(period_list) != 2:
            print(period_list)
            response["message"] = "时间格式有问题"
            return JsonResponse(response)
        if len(period_list[0]) != 8 or len(period_list[1]) != 8:
            response["message"] = "开始时间或结束时间少于8位"
            return JsonResponse(response)
        start_date_str = period_list[0]
        end_date_str = period_list[1]
        y_end = int(end_date_str[:4])
        m_end = int(end_date_str[4:6])
        d_end = int(end_date_str[6:])
        start_time = datetime.datetime(year=y_end, month=m_end, day=d_end)
        end_time = datetime.timedelta(days=1) + start_time
        end_date = end_time.strftime("%Y-%m-%d %H:%M:%S")
        y_start = int(start_date_str[:4])
        m_start = int(start_date_str[4:6])
        d_start = int(start_date_str[6:])
        start_date = datetime.datetime(year=y_start, month=m_start, day=d_start).strftime("%Y-%m-%d %H:%M:%S")
        # self.delete_data(start_date, end_date)
        _th = threading.Thread(target=self.delete_data, args=(start_date, end_date))
        _th.start()
        response["message"] = "删除成功"
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)

    @staticmethod
    def delete_data_by_series(series_uid_list):
        _callback_queryset = CallBackDICOM.objects.filter(series_instance_uid__in=series_uid_list)
        # clear_series_list = []
        # clear_study_list = []
        # for i in _callback_queryset:
        #     # if i.series_instance_uid not in clear_series_list:
        #         # clear_series_list.append(i.series_instance_uid)
        #     if i.study_instance_uid not in clear_study_list:
        #         clear_study_list.append(i.study_instance_uid)
        # if _callback_queryset:
        #     _callback_queryset.delete()
        _algorithm_task_queryset = AlgorithmTask.objects.filter(series_uid__in=series_uid_list)
        # if _algorithm_task_queryset:
        #     _algorithm_task_queryset.delete()
        _algorithm_result_queryset = AlgorithmResult.objects.filter(image_series__in=series_uid_list)
        clear_mongodb_id_list = []
        task_id_list = []
        for j in _algorithm_result_queryset:
            if j.algorithm_result not in clear_mongodb_id_list:
                clear_mongodb_id_list.append(j.algorithm_result)
        for t in _algorithm_task_queryset:
            if t.uuid not in task_id_list and t.algorithm_type == "ctp":
                task_id_list.append(t.uuid)
        if _algorithm_result_queryset:
            _algorithm_result_queryset.delete()
        if _callback_queryset:
            _callback_queryset.delete()
        if _algorithm_task_queryset:
            _algorithm_task_queryset.delete()

        # 删除mongodb
        if clear_mongodb_id_list:
            _mongodb = MongoDB()
            clear_mongodb_id_list = [ObjectId(m) for m in clear_mongodb_id_list]
            _deletemongodb = _mongodb.bulk_delete(condition_list=clear_mongodb_id_list, condition_key="_id",
                                                  table="algorithm")
        # 删除orthanc
        if series_uid_list:
            delete_orthanc_resources_by_series(series_list=series_uid_list)
            pass
        # 删除数据dcm目录
        for i in series_uid_list:
            folder = os.path.join(settings.DOCKER_CTP_ROOT_DIR, i)
            if os.path.exists(folder):
                shutil.rmtree(folder)
        # 删除ctp报告目录
        if task_id_list:
            for t in task_id_list:
                ctp_folder = os.path.join(settings.DOCKER_DATA_BASE_DIR, "ctp/" + t)
                if os.path.exists(ctp_folder):
                    shutil.rmtree(ctp_folder)


class OrthancCFindView(BaseView):
    def post(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        data = request.POST
        patient_info = data.get("patient_info", {})
        print(patient_info)
        query_type = data.get("query_type", "STUDY")
        if patient_info:
            if not isinstance(patient_info, dict):
                return False, "pacs查询信息格式不对"
            try:
                patient_info["current_index"] = int(patient_info.get("current_index", 1))
                patient_info["per_page_count"] = int(patient_info.get("per_page_count", 10))
            except Exception:
                print(traceback.format_exc())
                return False, "分页参数必须为整数"
        status, return_data, message = orthanc_c_find(is_debug=False,
                                                      patient_info=patient_info,
                                                      querylevel=query_type,
                                                      target_modality="targetpacs"
                                                      )
        if not status:
            response["message"] = message
        response["data"] = return_data
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)


class OrthancCMoveView(BaseView):
    def post(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        data = request.POST
        patient_info = data.get("patient_info", {})
        print(patient_info)
        query_type = data.get("query_type", "SERIES")
        patient_info = data.get("patient_info", {})
        status, return_data = orthanc_c_move(patient_info=patient_info,
                                             querylevel=query_type,
                                             target_modality="targetpacs",
                                             accept_aet=settings.ORTHANC_AET
                                             )
        if not status:
            response["message"] = return_data
            return JsonResponse(response)
        response["data"] = return_data
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)


class TransmissionTimeView(BaseView):
    def get(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        queryset = RecordTime.objects.filter(category="callback").order_by("-create_time")
        time_consume = 0
        predict_time = 0
        if queryset:
            print(queryset)
            last_queryset = queryset.first()
            print(last_queryset.series_instance_uid)
            if last_queryset:
                last_two_queryset = RecordTime.objects.filter(series_instance_uid=last_queryset.series_instance_uid,
                                                              category="callback").order_by("-create_time")
                print(last_two_queryset)
                if last_two_queryset:
                    time_list = [i.create_time for i in last_two_queryset]
                    print(time_list)
                    if len(time_list) == 2:
                        time_consume = (time_list[0] - time_list[1]).total_seconds()
                        predict_time = time_consume * 2
        response["status"] = True
        response["data"] = dict(time_consume=time_consume, predict_time=predict_time)
        response["code"] = 200
        return JsonResponse(response)


class ConfigSetView(BaseView):
    """
    动态配置被动接收推送aspect或ctp判定条件
    """

    def get(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        data = request.GET
        search = data.get('search', '')
        page_index = data.get('page_index', 1)
        page_per_count = data.get('page_per_count', 10)
        condition_key = data.get("condition_key", None)
        condition_value = data.get("condition_value", None)
        queryset = ConfigSet.objects.filter(is_delete=False)
        if not queryset:
            response["data"] = {}
            response["code"] = 200
            response["status"] = True
            return JsonResponse(response)
        if condition_key and condition_value:
            try:
                condition = {
                    condition_key: condition_value
                }
                queryset = queryset.filter(**condition)
            except Exception:
                log.error(traceback.format_exc())
                response["message"] = "condition_key or condition_value type error"
                return JsonResponse(response)
        if search:
            queryset = queryset.filter(algorithm_type__icontains=search)
        if not queryset:
            response['data'] = {}
            response['status'] = True
            response['code'] = 200
            response['message'] = '没有记录'
            return JsonResponse(response)
        pre_data = page_split(queryset,
                              page_index,
                              page_per_count,
                              )
        response["data"] = pre_data
        response['status'] = True
        response['code'] = 200
        return JsonResponse(response)

    def post(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        data = request.POST
        key = data.get("key", None)
        value = data.get("value", "")
        algorithm_type = data.get("algorithm_type", None)
        if algorithm_type not in ("aspects", "ctp", "cta"):
            response["message"] = "该algorithm_type不能为空"
            return JsonResponse(response)
        if not value:
            response["message"] = "该value不能为空"
            return JsonResponse(response)
        if len(str(value).split(";")) <= 1:
            response["message"] = "该value必须包含英文分号;"
            return JsonResponse(response)
        if key not in C_Find_Parameter_List:
            response["message"] = "该key：%s不规范无法设置" % key
            return JsonResponse(response)
        queryset = ConfigSet.objects.filter(key=key, is_delete=False, algorithm_type=algorithm_type)
        if queryset:
            queryset.update(is_delete=True)
        try:
            uuid_str = str(uuid.uuid1())
            ConfigSet.objects.create(uuid=uuid_str,
                                     key=key,
                                     value=value,
                                     algorithm_type=algorithm_type)
        except Exception:
            log.error(traceback.format_exc())
            response["message"] = "设置失败"
            return JsonResponse(response)
        response["status"] = True
        response["code"] = 200
        response["message"] = "设置成功"
        return JsonResponse(response)

    def delete(self, request, pk):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        if not pk:
            response["message"] = "设置uuid 不能为空"
            return JsonResponse(response)
        queryset = ConfigSet.objects.filter(uuid=pk, is_delete=False)
        if not queryset:
            response["message"] = "该数据已删除"
            return JsonResponse(response)
        queryset.update(is_delete=True)
        response["status"] = True
        response["code"] = 200
        response["message"] = "删除成功"
        return JsonResponse(response)


class WaitTimeSetView(BaseView):
    """
    设置和查询被动接收等待时间（对应orthanc字段是StableAge）
    """

    def get(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        queryset = RecordTime.objects.filter(category="callback_set")
        if not queryset:
            response["data"] = {}
            response["code"] = 200
            response["status"] = True
            return JsonResponse(response)
        response["status"] = True
        response["code"] = 200
        response["data"] = queryset.first().to_dict()
        return JsonResponse(response)

    def post(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        data = request.POST
        time = data.get("time", "")
        if time in [None, "", " "]:
            response["message"] = "设置时间不能为空"
            return JsonResponse(response)
        try:
            time = int(time)
        except Exception:
            response["message"] = "设置时间转换失败"
            return JsonResponse(response)
        if time <= 0:
            response["message"] = "设置时间不能为负数或者是0"
            return JsonResponse(response)
        queryset = RecordTime.objects.filter(category="callback_set")
        if queryset:
            queryset.delete()
        RecordTime.objects.create(category="callback_set", consume_time=time)
        status, message = wirte_config_file(config_path=settings.ORTHANC_CONFIG_FILE,
                                            wirte_data=time,
                                            key="StableAge")
        if not status:
            response["message"] = message
            return JsonResponse(response)
        _docker_monitor = DockerMonitor()
        status, info = _docker_monitor.restart_containers_server(name="cloud_platform_pacs")
        if not status:
            response["message"] = info
            return JsonResponse(response)
        response["message"] = "设置成功"
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)


class MonitorLogView(BaseView):
    """
    查看服务log信息
    """

    def get(self, request):
        response = self.response
        creator = check_auth_code(request)
        if not creator:
            response['code'] = 413
            response['message'] = '操作验证失败'
            return JsonResponse(response)
        data = request.GET
        container_id = data.get("container_id", None)
        date_time = data.get("time", "")
        tail = data.get("tail", 2000)
        if not container_id:
            response["message"] = "请传入服务id"
            return JsonResponse(response)
        try:
            tail = int(tail)
        except Exception:
            response["message"] = "行数必须是数字"
            return JsonResponse(response)
        query = dict(name=container_id, tail=tail)
        if date_time:
            time_list = date_time.split("/")
            if len(time_list) == 2:
                start_time = datetime.datetime.strptime(time_list[0], "%Y-%m-%d")
                end_time = datetime.datetime.strptime(time_list[1], "%Y-%m-%d")
                query.update(dict(until=end_time, since=start_time, tail=tail))

        GE_CALLBACK_START = SystemConfigUtils().getConfigValue(code="GE_CALLBACK_START", def_value=0)
        if GE_CALLBACK_START == "1":  # ge开关调用k8s api
            response["data"] = K8sMonitor().get_pod_logs(container_id)
        else:
            _monitor = DockerMonitor()
            response["data"] = (_monitor.get_conntainer_logs(**query)).decode()
        response["code"] = 200
        response["status"] = True
        return JsonResponse(response)


class ReportDicomUploadView(BaseView):
    """
    主要是通过orthanc api 内部做c_move报告回传
    修改记录： 2022-10-8 王刚 增加对cta手动回传的功能的兼容
    """

    def post(self, request):
        creator = check_auth_code(request)
        if not creator:
            return self.fail(413, "操作验证失败")
        data = request.POST
        log.debug("manual return > body:{}".format(data))
        patient_info = data.get("patient_info", [])
        query_type = data.get("query_type", "SERIES")
        if query_type not in ("SERIES", "STUDY", "PATIENT", "INSTANCE"):
            return self.fail(412, "query_type 与预设类型不匹配")
        if not patient_info:
            return self.fail(412, "query_type 不能为空")
        status, message = self.validate_field(patient_info)
        if not status:
            return self.fail(412, message)
        aet_queryset = PacsServer.objects.filter(alie_name="target_pacs")
        if not aet_queryset.exists():
            return self.fail(412, "请到设置页添加ae信息， 没有查询到ae信息")
        # 判断算法是否完成
        study_uid = patient_info[0].get("StudyInstanceUID", None)
        if not study_uid:
            return self.fail(412, "StudyInstanceUID不能为空")
        study = Study.objects.filter(study_instance_uid=study_uid).first()
        if not study:
            return self.fail(412, "无效StudyInstanceUID")
        result_series_uids = [item.get("SeriesInstanceUID") for item in patient_info]
        if not result_series_uids:
            return self.fail(412, "SeriesInstanceUID不能为空")
        result_series_queryset = Series.objects.filter(series_instance_uid__in=result_series_uids)
        if result_series_queryset.count() != len(result_series_uids):
            return self.fail(412, "无效SeriesInstanceUID")
        algorithm_type = result_series_queryset.first().type.split("_")[0]
        # 同步耗时，改为异步线程
        threading.Thread(target=self.back, args=(study, algorithm_type, result_series_uids)).start()
        return self.ok(message="回传报告执行完毕，请在接收报告处查看是否已收到")

    @staticmethod
    def back(study, algorithm_type, result_series_uids):
        """
        报告手动回传

        :param study: 检查
        :param algorithm_type: 算法类型
        :param result_series_uids: 结果序列
        :return:
        """
        study_instance_uid = study.study_instance_uid
        # 东芝CTP
        if algorithm_type == Const.ALGORITHM_TYPE_CTP and study.toshiba:
            log.info("manual report > study:{}, toshiba series".format(study_instance_uid))
            original_series_uids = Series.objects.filter(
                study_id=study.id, type=Const.ALGORITHM_TYPE_CTP).values_list("series_instance_uid", flat=True).distinct()
            series_uids = list(original_series_uids)
            message = dict(studyInstanceUID=study_instance_uid, seriesInstanceUIDs=series_uids,
                           algorithmType=algorithm_type, BackReport=True, manualReturn=True)
            try:
                RabbitMQProducer.simple_send(queue_name="algorithm_ctp_aspects_result", message=message)
            except Exception:
                log.error(f"Series[callback] send message error: {traceback.format_exc()}")
            return
        # 非东芝CTP
        original_series_uids = Series.objects.filter(
            series_instance_uid__in=result_series_uids).values_list("original_series", flat=True).distinct()
        for series_uid in original_series_uids:
            message = dict(studyInstanceUID=study_instance_uid, seriesInstanceUID=series_uid,
                           algorithmType=algorithm_type, BackReport=True, manualReturn=True)
            try:
                RabbitMQProducer.simple_send(queue_name="algorithm_ctp_aspects_result", message=message)
            except Exception:
                log.error(f"Series[callback] send message error: {traceback.format_exc()}")
        log.debug("manual report > done")

    @staticmethod
    def validate_field(patient_info):
        """
        校验是否符合规则
        :param patient_info: 带校验字段对象
        :return:(status， message)
        """
        if not patient_info:
            return False, "patient_info 不能为空"
        if not isinstance(patient_info, list):
            return False, "patient_info 必须为数组"
        for patient in patient_info:
            if not isinstance(patient, dict):
                return False, "病人报告字段列表中有不是对象"
            for key, value in patient.items():
                if key not in settings.C_Find_Parameter_List:
                    return False, "dicom tag 与配置文件不符合"
                if value in ("", None, " "):
                    return False, "dicom tag 值为空"
        return True, None


class ClearPatientDataView(BaseView):
    """
    删除病人下所有数据
    """

    @staticmethod
    def delete_mongo(clear_mongodb_id_list):
        """删除mongo对应数据"""
        _mongodb = MongoDB()
        clear_mongodb_id_list = [ObjectId(m) for m in clear_mongodb_id_list]
        _deletemongodb = _mongodb.bulk_delete(condition_list=clear_mongodb_id_list, condition_key="_id",
                                              table="algorithm")
        log.info("API[clearpatientdata] > mongodb bulk delete: %s", _deletemongodb)

    @staticmethod
    def delete_orthanc(study_instance_id_list):
        """
        删除orthanc数据
        """
        delete_status = delete_orthanc_resources(study_instance_id_list)
        log.info("API[clearpatientdata] > delete orthanc: %s", delete_status)
        if not delete_status:
            return False, "删除本地pacs失败"

    @staticmethod
    def delete_dcm_dir(clear_series_list):
        """
        删除dicom目录文件
        """
        for i in clear_series_list:
            folder = os.path.join(settings.DOCKER_CTP_ROOT_DIR, i)
            if os.path.exists(folder):
                shutil.rmtree(folder)
        log.info("API[clearpatientdata] > delete dcm success")

    @staticmethod
    def __delete_folder(folder):
        if os.path.exists(folder):
            shutil.rmtree(folder)
            log.info("API[clearpatientdata] > delete : {}".format(folder))

    @staticmethod
    def delete_result(study_instance_uid_list):
        for study_instance_uid in study_instance_uid_list:
            ClearPatientDataView.__delete_folder(F"/code/server/static/jpg/{study_instance_uid}")
            ClearPatientDataView.__delete_folder(F"{settings.DOCKER_DATA_BASE_DIR}/ctp/{study_instance_uid}")


    def delete_study_list(self, study_uid_list):
        log.info("API[clearpatientdata] > studies: %s", study_uid_list)
        series_queryset = Series.objects.filter(study__study_instance_uid__in=study_uid_list).all()
        if not series_queryset.exists():
            log.error("API[clearpatientdata] > series not found")
            return False, "没有要删除的序列信息"
        series_id_list = []
        series_uid_list = []
        for series in series_queryset:
            series_id_list.append(series.id)
            series_uid_list.append(series.series_instance_uid)
        study_series_uid_list = series_uid_list + study_uid_list
        algorithm_result_qs = AlgorithmResult.objects.filter(image_series__in=study_series_uid_list)
        delete_mongodb_id_list = []
        if algorithm_result_qs.exists():
            for result in algorithm_result_qs:
                if result.algorithm_result not in delete_mongodb_id_list:
                    delete_mongodb_id_list.append(result.algorithm_result)
        deleted, rows_count = algorithm_result_qs.delete()
        log.info("API[del] > delete algorithm result, result:{}, rows_count:{}".format(deleted, rows_count))
        deleted, rows_count = AlgorithmTask.objects.filter(series_uid__in=series_uid_list).delete()
        log.info("API[del] > delete algorithm task, result:{}, rows_count:{}".format(deleted, rows_count))
        deleted, rows_count = CallBackDICOM.objects.filter(study_instance_uid__in=study_uid_list).delete()
        log.info("API[del] > delete callback dicom, result:{}, rows_count:{}".format(deleted, rows_count))
        PDFReport.objects.filter(study_instance_uid__in=study_uid_list).delete()
        SeriesAlgorithm.objects.filter(series_instance_uid__in=series_uid_list).delete()
        FeatureMap.objects.filter(study_instance_uid__in=study_uid_list).delete()
        deleted, rows_count = Series.objects.filter(id__in=series_id_list).delete()
        log.info("API[del] > delete series, result:{}, rows_count:{}".format(deleted, rows_count))
        deleted, rows_count = Study.objects.filter(study_instance_uid__in=study_uid_list).delete()
        log.info("API[del] > delete studies, result:{}, rows_count:{}".format(deleted, rows_count))

        # 线程删除 mongo ，orthanc，dcm目录数据
        if delete_mongodb_id_list:
            threading.Thread(target=self.delete_mongo, args=(delete_mongodb_id_list,)).start()
        threading.Thread(target=self.delete_orthanc, args=(study_uid_list,)).start()
        threading.Thread(target=self.delete_dcm_dir, args=(study_series_uid_list,)).start()
        threading.Thread(target=self.delete_result, args=(study_uid_list,)).start()
        log.info("API[del] > done!")
        # 删除检查通知
        notify_message = {"action": "delete", "data": {"StudyInstanceUIDs": study_uid_list}}
        RabbitMQProducer.ws_notify("ws.notify.study_delete", notify_message)
        return True, ""

    def post(self, request):
        """
        按ctp/aspects/cta删除病人下所有数据
        seriesUidArray :
        """
        data = request.POST
        study_list = data.get("study", [])
        if not study_list or not isinstance(study_list, list):
            return self.fail(412, "参数传入错误")
        # 删除数据
        # status, message = self.delete_data_by_study(study_list)
        status, message = self.delete_study_list(study_list)
        if not status:
            return self.fail(412, message)
        return self.ok()

