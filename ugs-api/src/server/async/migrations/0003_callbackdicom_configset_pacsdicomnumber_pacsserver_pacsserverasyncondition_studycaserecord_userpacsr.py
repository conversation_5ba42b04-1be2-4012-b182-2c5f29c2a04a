# Generated by Django 2.0.5 on 2021-04-01 14:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('async', '0002_callbackdicom_configset_pacsdicomnumber_pacsserver_pacsserverasyncondition_recordtime_studycaserecor'),
    ]

    operations = [
        migrations.CreateModel(
            name='RecordTime',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('category', models.CharField(blank=True, max_length=64, null=True, verbose_name='类型')),
                ('series_instance_uid', models.Char<PERSON>ield(blank=True, max_length=256, null=True)),
                ('sop_instance_uid',
                 models.CharField(blank=True, max_length=256, null=True, verbose_name='instance uid')),
                ('consume_time', models.FloatField(null=True, verbose_name='耗时，单位s')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('create_time', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': '耗时记录表',
                'verbose_name_plural': '耗时记录表',
                'db_table': 'record_time',
            },
        ),
    ]
