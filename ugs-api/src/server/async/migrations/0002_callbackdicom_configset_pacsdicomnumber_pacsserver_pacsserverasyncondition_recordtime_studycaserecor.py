# Generated by Django 2.0.5 on 2021-03-31 15:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('async', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StudyViewModel',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('sop_instance_uid', models.TextField(blank=True, null=True, verbose_name='SOP序列uid')),
                ('study_instance_uid', models.TextField(blank=True, null=True, verbose_name='STUDY序列uid')),
                ('series_instance_uid', models.TextField(blank=True, null=True, verbose_name='Series序列uid')),
                ('patient_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='病人id')),
                ('patient_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='病人姓名')),
                ('updatetimestamp', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('number', models.IntegerField(default=0, verbose_name='series下文件数量')),
                ('is_pushed', models.BooleanField(default=False, verbose_name='是否完成推送')),
                ('download_status', models.BooleanField(default=False, verbose_name='下载进度')),
                ('timeout', models.CharField(blank=True, max_length=255, null=True, verbose_name='设置的被动接收超时时间')),
                ('aet', models.CharField(blank=True, max_length=255, null=True, verbose_name='目标aet')),
                ('is_first_download', models.BooleanField(default=False, verbose_name='是否是第一次下载')),
                ('ip', models.CharField(blank=True, max_length=255, null=True, verbose_name='目标ip')),
                ('port', models.CharField(blank=True, max_length=255, null=True, verbose_name='目标端口')),
                ('sop_orthanc_uuid', models.CharField(blank=True, max_length=255, null=True, verbose_name='sop orthanc uuid')),
                ('series_orthanc_uuid', models.CharField(blank=True, max_length=255, null=True, verbose_name='series orthanc uuid')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('study_description', models.TextField(blank=True, null=True, verbose_name='Study描述')),
                ('modality', models.TextField(blank=True, null=True, verbose_name='设备类型')),
                ('series_description', models.TextField(blank=True, null=True, verbose_name='series 描述')),
                ('protocol_name', models.TextField(blank=True, null=True, verbose_name='协议名')),
                ('slice_thickness', models.TextField(blank=True, null=True, verbose_name='厚度')),
                ('task_uuid', models.CharField(blank=True, max_length=255, null=True, verbose_name='任务id')),
                ('finish_percent', models.IntegerField(default=0, verbose_name='完成百分比')),
                ('algorithm_type', models.CharField(blank=True, max_length=255, null=True, verbose_name='算法类型')),
            ],
            options={
                'verbose_name': 'study级别任务进度视图',
                'verbose_name_plural': 'study级别任务进度视图',
                'db_table': 'study_view',
                'managed': False,
            },
        ),

        migrations.CreateModel(
            name='RecordTime',
            fields=[
                ('uuid', models.CharField(max_length=255, primary_key=True, serialize=False, verbose_name='uuid')),
                ('category', models.CharField(blank=True, max_length=64, null=True, verbose_name='类型')),
                ('series_instance_uid', models.CharField(blank=True, max_length=256, null=True)),
                ('sop_instance_uid', models.CharField(blank=True, max_length=256, null=True, verbose_name='instance uid')),
                ('consume_time', models.FloatField(null=True, verbose_name='耗时，单位s')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('create_time', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': '耗时记录表',
                'verbose_name_plural': '耗时记录表',
                'db_table': 'record_time',
            },
        ),

    ]
