import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")
import django
import importlib

django.setup()
async_models = importlib.import_module('server.async.models')


class ConfigSetProcess(object):
    def __init__(self):
        self.data_queryset = async_models.ConfigSet.objects.filter(is_delete=False)
        print("动态加载推送条件已加载")

    @property
    def data_queryset(self):
        return self._data_queryset

    @data_queryset.setter
    def data_queryset(self, data):
        if data.count() >= 40:
            self._data_queryset = []
            print(ValueError("error configset more than 40"))
        self._data_queryset = data

    @staticmethod
    def all_str_expr_part(key, value):
        expr = """
        for key, value in compare_data.items():
            data.append(any([True for text in list_text if value.strip().upper().find(text.strip().upper())!=-1]))
            data.append(value>d and value<a)



        """
        return expr
        pass

    @staticmethod
    def str_expr_part(tag_type, data):
        _str = ""
        if tag_type == "int":
            if data[0] not in ["", None] and data[1] not in ["", None]:
                _str = F"""value>{str(data[0])} and value<{str(data[1])}"""
            elif data[0] not in [" ", None]:
                _str = F"""value>{str(data[0])}"""
            elif data[1] not in [" ", None]:
                _str = F"""value<{str(data[1])}"""
        else:
            _str = F"""any([True for text in {str(
                data)}if value.strip().upper().find(text.strip().upper())!=-1])"""
        return _str

    def config_process(self):
        data = list()
        data_queryset = self.data_queryset
        for i in data_queryset:
            temp_value = i.value
            _str = ''
            if temp_value:
                value_list = temp_value.split(";")
                if len(value_list) > 2:
                    value_list = [j for j in value_list if j]
                    _str = self.str_expr_part(tag_type="str", data=value_list)
                elif len(value_list) == 2:
                    try:
                        condition = dict(tag_type="int", data=[float(value_list[0]), float(value_list[1])])
                    except Exception:
                        try:
                            condition = dict(tag_type="int", data=[float(value_list[0]), ""])
                        except Exception:
                            try:
                                condition = dict(tag_type="int", data=["", float(value_list[1])])
                            except Exception:
                                value_list = [j for j in value_list if j]
                                condition = dict(tag_type="str", data=value_list)
                    _str = self.str_expr_part(**condition)
            if _str:
                data.append([i.algorithm_type, i.key, _str])
                # yield i.algorithm_type, i.key, _str
        return data


# def get_config_cache(clear_cache=False):
#     try:
#         pool = redis.ConnectionPool(host=settings.REDIS_HOST,
#                                     port=settings.REDIS_PORT,
#                                     password=settings.REDIS_PASSWORD,
#                                     db=0)
#         db = redis.Redis(connection_pool=pool)
#     except Exception:
#         print(traceback.format_exc())
#         _configsetpro = ConfigSetProcess()
#         PUSH_CONDITION = _configsetpro.config_process()
#         return PUSH_CONDITION
#     if clear_cache:
#         _configsetpro = ConfigSetProcess()
#         PUSH_CONDITION = _configsetpro.config_process()
#         db.set("condition_key", json.dumps(PUSH_CONDITION), ex=3600 * 24)
#         return PUSH_CONDITION
#     else:
#         if db.exists("condition_key"):
#             return json.loads(db.get("condition_key").decode())
#         else:
#             _configsetpro = ConfigSetProcess()
#             PUSH_CONDITION = _configsetpro.config_process()
#             db.set("condition_key", json.dumps(PUSH_CONDITION), ex=3600 * 24)
#             return PUSH_CONDITION
