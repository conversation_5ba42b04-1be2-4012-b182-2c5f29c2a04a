import datetime
import json

from django.db import models

MAX_LENGTH = 255


# 数据同步和过滤功能存储数据库

class PacsServer(models.Model):
    id = models.CharField(primary_key=True,
                          verbose_name='uuid',
                          max_length=MAX_LENGTH)
    aet = models.CharField(verbose_name='aet',
                           max_length=MAX_LENGTH)
    ip_address = models.CharField(verbose_name='ip地址',
                                  max_length=MAX_LENGTH)
    port = models.IntegerField(verbose_name='端口',
                               default=0,
                               null=True,
                               blank=True)
    alie_name = models.CharField(verbose_name='别名',
                                 max_length=MAX_LENGTH,
                                 null=True,
                                 blank=True)

    class Meta:
        db_table = 'pacs_server'
        verbose_name = 'pacs服务器'
        verbose_name_plural = 'pacs服务器'

    def __str__(self):
        return self.aet

    def to_dict(self):
        return dict(
            id=self.id,
            aet=self.aet,
            ip_address=self.ip_address,
            port=self.port,
            alie_name=self.alie_name
        )


class RecordTime(models.Model):
    """
    用与记录 pacs instance 传输时间 和 算法计算时间
    """
    uuid = models.CharField(primary_key=True, verbose_name='uuid', max_length=MAX_LENGTH)
    category = models.CharField('类型', max_length=64, null=True, blank=True)
    series_instance_uid = models.CharField(max_length=256, null=True, blank=True)
    sop_instance_uid = models.CharField('instance uid', max_length=256, null=True, blank=True)
    consume_time = models.FloatField('耗时，单位s', null=True)
    comment = models.TextField('备注', null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'record_time'
        verbose_name = '耗时记录表'
        verbose_name_plural = '耗时记录表'

    def to_dict(self):
        return dict(consume_time=int(self.consume_time))


class ConfigSet(models.Model):
    """
    ctp 和aspect 动态设置触发推送条件
    """
    uuid = models.CharField(primary_key=True, verbose_name='uuid', max_length=MAX_LENGTH)
    algorithm_type = models.CharField(verbose_name='算法类型',
                                      max_length=MAX_LENGTH,
                                      null=True,
                                      blank=True
                                      )
    key = models.TextField(verbose_name="key值",
                           null=True,
                           blank=True)
    value = models.TextField(verbose_name="value值",
                             null=True,
                             blank=True)
    timestamp = models.DateTimeField(verbose_name='创建时间',
                                     auto_now_add=True)
    comment = models.TextField('备注', null=True, blank=True)
    is_delete = models.BooleanField(default=False, verbose_name='是否更新，如果更新设置为删除状态',
                                    blank=True)

    class Meta:
        db_table = 'push_config'
        verbose_name = '推送配置表'
        verbose_name_plural = '推送配置表'

    def to_dict(self):
        return dict(
            uuid=self.uuid,
            algorithm_type=self.algorithm_type,
            key=self.key,
            value=self.value,
            comment=self.comment,
            timestamp=self.timestamp.strftime("%Y-%m-%d")
        )
