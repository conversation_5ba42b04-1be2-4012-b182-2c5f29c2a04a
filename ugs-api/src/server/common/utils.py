from pydicom import read_file
from pydicom.dataset import Dataset
from pynetdicom import (AE,
                        VerificationPresentationContexts,
                        StoragePresentationContexts,
                        QueryRetrievePresentationContexts)
from server import settings
import pydicom
import SimpleITK as sitk
import requests
import os
import uuid


class PACSUtil(object):

    def __init__(self, **kwargs):
        if kwargs:
            self.pacs_address = kwargs['target_ip']
            self.aet = kwargs['target_ae']
            self.port = kwargs['target_port']
            self.local_aet = settings.LOCAL_AET
            # self.is_page = True
            # self.username = settings.ORTHANC_WADO_USERNAME
            # self.password = settings.ORTHANC_WADO_PASSWORD
        else:
            self.pacs_address = settings.ORTHANC_HOST
            # self.pacs_address = "************"
            self.aet = settings.ORTHANC_AET
            self.port = settings.ORTHANC_PORT
            self.username = settings.ORTHANC_WADO_USERNAME
            self.password = settings.ORTHANC_WADO_PASSWORD
            self.local_aet = settings.LOCAL_AET

    @staticmethod
    def get_index_interval(per_page_count, current_index):
        if current_index - 1 <= 0:
            return 0, per_page_count
        start = (current_index - 1) * per_page_count
        end = current_index * per_page_count
        return start, end

    @staticmethod
    def dict_to_dataset(patient_info, queryRetrievel='STUDY'):
        data = Dataset()
        # data[0x0020, 0x1209] = DataElement(0x00201209, 'testadmin', 0)
        data.QueryRetrieveLevel = queryRetrievel
        for key in settings.C_Find_Parameter_List:
            # if key == "PatientName":
            #     continue
            setattr(data, key, patient_info.get(key, ''))
        return data

    @staticmethod
    def dataset_to_dict(identifier):
        # print(identifier)
        pacs_content = dict()
        for key in settings.C_Find_Parameter_List:
            if key == "PatientName":
                patient_name = identifier.get('PatientName', '')
                if patient_name:
                    # print(patient_name.original_string)
                    if not patient_name.original_string:
                        pacs_content["PatientName"] = ""
                    else:
                        if isinstance(patient_name.original_string, str):
                            pacs_content['PatientName'] = patient_name.original_string
                            # print(pacs_content["PatientName"])
                        if isinstance(patient_name.original_string, bytes):
                            try:
                                pacs_content['PatientName'] = bytes.decode(patient_name.original_string)
                            except Exception:
                                try:
                                    pacs_content['PatientName'] = (patient_name.original_string).decode('gbk')
                                except Exception:
                                    return dict()
            elif key == "PixelSpacing":
                pixel_spacing_tag = identifier.get('PixelSpacing', '')
                if pixel_spacing_tag:
                    pacs_content['PixelSpacing'] = "/".join(
                        [str(pixel) for pixel in pixel_spacing_tag])
            else:
                pacs_content[key] = identifier.get(key, '')
        return pacs_content

    @staticmethod
    def query_aspect_ctp(self):
        pass

    def split_page_patient(self, patient_info, queryRetrievel='STUDY'):
        self.ae = AE(ae_title=self.local_aet)
        self.ae.requested_contexts = QueryRetrievePresentationContexts
        self.assoc = self.ae.associate(self.pacs_address, self.port, ae_title=self.aet)
        index = 1
        current_index = patient_info.get("current_index", 1)
        per_page_count = patient_info.get("per_page_count", 10)
        start, end = self.get_index_interval(per_page_count, current_index)
        page_info = dict(has_next=False, has_previous=False)
        if self.assoc.is_established:
            data = self.dict_to_dataset(patient_info, queryRetrievel=queryRetrievel)
            value = self.assoc.send_c_find(data, query_model='S')
            if value:
                data_list = []
                series_list = []
                flag_error = False
                for (status, identifier) in value:
                    # print(status, "***********", identifier)
                    if status.get('Status', ''):
                        if status.Status == 65281 or status.Status == 65280:
                            if index <= start:
                                # print(identifier.get('StudyInstanceUID', ''))
                                page_info["has_previous"] = True
                                index += 1
                                continue
                            elif index > end:
                                index += 1
                                # print(identifier.get('StudyInstanceUID', ''), "hhhhhhhhhhhhhhh")
                                page_info["has_next"] = True
                                continue
                            pacs_content = self.dataset_to_dict(identifier)
                            if queryRetrievel == "SERIES":
                                _se = pacs_content.get("SeriesInstanceUID", None)
                                if _se:
                                    series_list.append(_se)
                            elif queryRetrievel == "STUDY":
                                _se = pacs_content.get("StudyInstanceUID", None)
                                if _se:
                                    print(_se)
                                    series_list.append(_se)
                            if pacs_content:
                                data_list.append(pacs_content)
                            index += 1
                        else:
                            print(status.Status)
                            flag_error = True
                self.assoc.release()
                page = int((index - 1) / per_page_count)
                page_info["page_count"] = page + 1 if int((index - 1) % per_page_count) else page
                page_info["page_index"] = current_index
                page_info["record"] = data_list
                return page_info, series_list, None if not flag_error else "pacs查询失败"
        else:
            print('pacs connect fail')
            return None, None, "pacs连接失败"

    def retrieval_some_image_new(self, patient_info, queryRetrievel='INSTANCE'):
        self.ae = AE(ae_title=self.local_aet)
        self.ae.add_requested_context('1.2.840.10008.*******.2.2.2')
        assoc = self.ae.associate(self.pacs_address, self.port, ae_title=self.aet)
        # self.ae.acse_timeout = 1
        # self.ae.dimse_timeout = 1
        # self.ae.network_timeout = 1
        message = dict(type=103, status=False, download_percent=0, data=dict(
            QueryRetrieveLevel=queryRetrievel,
            StudyInstanceUID=patient_info.get("StudyInstanceUID", ""),
            SeriesInstanceUID=patient_info.get("SeriesInstanceUID", ""),
            SOPInstanceUID=patient_info.get("SOPInstanceUID", "")
        ), message=None)
        if assoc.is_established:
            data = Dataset()
            data.QueryRetrieveLevel = queryRetrievel
            data.StudyInstanceUID = patient_info.get("StudyInstanceUID", "")
            data.SeriesInstanceUID = patient_info.get("SeriesInstanceUID", "")
            data.SOPInstanceUID = patient_info.get("SOPInstanceUID", "")
            value = assoc.send_c_move(data, self.local_aet, query_model="S", priority=0)
            is_first = True
            total = 0
            if value:
                for (status, identifier) in value:
                    # print(status, "****************", type(identifier))
                    if not status:
                        print("new____*******status: ", status, "*******identifier*****", identifier)
                        message['message'] = "new souce pacs aborted"
                        message['status'] = 2
                        assoc.abort()
                        assoc.release()
                        yield False, message
                        break
                    if status.get('Status', ''):
                        if status.Status == 65280 or status.Status == 65281:
                            if is_first:
                                total = int(status[0x0000, 0x1020].value) + int(status[0x0000, 0x1021].value)
                                message["total"] = total
                            completedNumber = int(status[0x0000, 0x1021].value)
                            message["download_percent"] = int((completedNumber / total) * 100)
                            message["status"] = 1
                            if completedNumber != 0:
                                is_first = False
                            result = yield True, message
                            if result == "abort":
                                assoc.abort()
                                assoc.release()
                                break
                        elif status.Status == 49152:
                            message['message'] = "local-pacs-server information is not add remoto-pacs-server" \
                                                 "，connection build fail"
                            message['status'] = 2
                            yield False, message
                            break
                        else:
                            message["status"] = 2
                            message["message"] = "c_move error"
                            yield False, message
                            break
                    elif status.Status == 0:
                        message["download_percent"] = 100
                        message["status"] = 0
                        yield True, message
                assoc.release()
        else:
            message["status"] = 2
            message['message'] = "souce pacs connect fail"
            print(message)
            yield False, message

    def retrieval_all_patient(self,
                              patient_info,
                              queryRetrievel='PATIENT',
                              ):
        self.ae = AE(ae_title=self.aet)
        self.ae.requested_contexts = QueryRetrievePresentationContexts
        self.assoc = self.ae.associate(self.pacs_address, self.port)
        # print(self.aet, "---",self.port,"---", self.pacs_address, "_----",queryRetrievel)
        if self.assoc.is_established:
            # print("hhhhhh")
            data = Dataset()
            # data[0x0020, 0x1209] = DataElement(0x00201209, 'testadmin', 0)
            data.PatientName = patient_info.get('PatientName', '')
            data.QueryRetrieveLevel = queryRetrievel
            data.PatientID = patient_info.get('PatientID', '')
            data.PatientSex = patient_info.get('PatientSex', '')
            data.StudyInstanceUID = patient_info.get('StudyInstanceUID', '')
            data.SeriesInstanceUID = patient_info.get('SeriesInstanceUID', '')
            data.PatientAge = patient_info.get('PatientAge', '')
            data.PatientWeight = patient_info.get('PatientWeight', '')
            data.PatientBirthDate = patient_info.get('PatientBirthDate', '')
            data.StudyDescription = patient_info.get('StudyDescription', '')
            data.StudyDate = patient_info.get('StudyDate', '')
            data.StudyTime = patient_info.get('StudyTime', '')
            data.SeriesDescription = patient_info.get('SeriesDescription', '')
            data.SeriesDate = patient_info.get('SeriesDate', '')
            data.SeriesTime = patient_info.get('SeriesTime', '')
            data.SeriesNumber = patient_info.get('SeriesNumber', '')
            data.InstitutionName = patient_info.get('InstitutionName', '')
            data.SOPInstanceUID = patient_info.get('SOPInstanceUID', '')
            data.NumberOfFrames = patient_info.get('NumberOfFrames', '')
            data.SliceThickness = patient_info.get('SliceThickness', '')
            data.PixelSpacing = patient_info.get('PixelSpacing', '')
            data.Rows = patient_info.get('Rows', '')
            data.Columns = patient_info.get('Columns', '')
            data.SliceLocation = patient_info.get('SliceLocation', '')
            data.Modality = patient_info.get('Modality', '')
            data.AcquisitionType = patient_info.get('AcquisitionType', '')
            # For scanner
            data.StationName = patient_info.get('StationName', '')
            data.InstanceNumber = patient_info.get('InstanceNumber', '')
            data.Manufacturer = patient_info.get("Manufacturer", "")
            data.ManufacturerModelName = patient_info.get("ManufacturerModelName", "")
            # 查询meta_file 需要的信息
            data.TransferSyntaxUID = ''
            data.SOPClassUID = ''
            value = self.assoc.send_c_find(data, query_model='S')
            if value:
                data_list = []
                for (status, identifier) in value:
                    # print(status, "***********", identifier)
                    if status.get('Status', ''):
                        if status.Status == 65280:
                            pacs_content = dict()
                            patient_name = identifier.get('PatientName', '')
                            if patient_name:
                                if isinstance(patient_name.original_string, str):
                                    pacs_content['PatientName'] = patient_name.original_string
                                if isinstance(patient_name.original_string, bytes):
                                    try:
                                        pacs_content['PatientName'] = bytes.decode(patient_name.original_string)
                                    except Exception:
                                        try:
                                            pacs_content['PatientName'] = (patient_name.original_string).decode('gbk')
                                        except Exception:
                                            # log.error(str(traceback.format_exc()), "*********c_find********")
                                            continue
                            pacs_content['QueryRetrieveLevel'] = identifier.get('QueryRetrieveLevel')
                            pacs_content['PatientID'] = identifier.get('PatientID', '')
                            pacs_content['PatientSex'] = identifier.get('PatientSex', '')
                            pacs_content['StudyInstanceUID'] = identifier.get('StudyInstanceUID')
                            pacs_content['SeriesInstanceUID'] = identifier.get('SeriesInstanceUID')
                            pacs_content['PatientAge'] = identifier.get('PatientAge', '')
                            pacs_content['PatientWeight'] = identifier.get('PatientWeight', '')
                            pacs_content['PatientBirthDate'] = identifier.get('PatientBirthDate', '')
                            pacs_content['StudyDescription'] = identifier.get('StudyDescription', '')
                            pacs_content['StudyDate'] = identifier.get('StudyDate', '')
                            pacs_content['StudyTime'] = identifier.get('StudyTime', '')
                            pacs_content['SeriesDescription'] = identifier.get('SeriesDescription', '')
                            pacs_content['SeriesDate'] = identifier.get('SeriesDate', '')
                            pacs_content['SeriesTime'] = identifier.get('SeriesTime', '')
                            pacs_content['SeriesNumber'] = identifier.get('SeriesNumber', '')
                            pacs_content['InstitutionName'] = identifier.get('InstitutionName', '')
                            pacs_content['SOPInstanceUID'] = identifier.get('SOPInstanceUID')
                            pacs_content['NumberOfFrames'] = identifier.get('NumberOfFrames', '')
                            pacs_content['SliceThickness'] = identifier.get('SliceThickness', '')
                            pacs_content['PixelSpacing'] = identifier.get('PixelSpacing', '')
                            pacs_content['SliceLocation'] = identifier.get('SliceLocation', '')
                            pacs_content['Modality'] = identifier.get('Modality', '')
                            pacs_content['AcquisitionType'] = identifier.get('AcquisitionType', '')
                            if pacs_content['PixelSpacing']:
                                pacs_content['PixelSpacing'] = "/".join(
                                    [str(pixel) for pixel in pacs_content['PixelSpacing']])
                            pacs_content['Rows'] = identifier.get('Rows', '')
                            pacs_content['Columns'] = identifier.get('Columns', '')
                            # For scanner
                            pacs_content['StationName'] = identifier.get('StationName', '')
                            pacs_content['InstanceNumber'] = identifier.get('InstanceNumber', '')
                            pacs_content['TransferSyntaxUID'] = identifier.get('TransferSyntaxUID', '')
                            pacs_content['SOPClassUID'] = identifier.get('SOPClassUID', '')
                            pacs_content['Manufacturer'] = identifier.get('Manufacturer', '')
                            pacs_content["ManufacturerModelName"] = identifier.get("ManufacturerModelName","")
                            # if (0x0020, 0x1209) in identifier:
                            #     pacs_content["test_name"] = identifier[0x0020, 0x1209]
                            data_list.append(pacs_content)
                self.assoc.release()
                return data_list
        else:
            print('pacs connect fail')

    def retrieval_all_image(self, patient_info, queryRetrievel='SERIES'):
        self.ae = AE(ae_title='CAOBO')
        self.ae.requested_contexts = QueryRetrievePresentationContexts
        self.assoc = self.ae.associate(self.pacs_address, self.port)
        if self.assoc.is_established:
            data = Dataset()
            data.PatientName = patient_info.get('PatientName', '')
            data.QueryRetrieveLevel = queryRetrievel
            data.PatientID = patient_info.get('PatientID', '')
            data.PatientSex = patient_info.get('PatientSex', '')
            data.StudyInstanceUID = patient_info.get('StudyInstanceUID', '')
            data.SeriesInstanceUID = patient_info.get('SeriesInstanceUID', '')
            data.PatientAge = patient_info.get('PatientAge', '')
            data.PatientWeight = patient_info.get('PatientWeight', '')
            data.PatientBirthDate = patient_info.get('PatientBirthDate', '')
            data.StudyDescription = patient_info.get('StudyDescription', '')
            data.StudyDate = patient_info.get('StudyDate', '')
            data.StudyTime = patient_info.get('StudyTime', '')
            data.SeriesDescription = patient_info.get('SeriesDescription', '')
            data.SeriesDate = patient_info.get('SeriesDate', '')
            data.SeriesTime = patient_info.get('SeriesTime', '')
            data.InstitutionName = patient_info.get('InstitutionName', '')
            data.SOPInstanceUID = patient_info.get('SOPInstanceUID', '')
            data.NumberOfFrames = patient_info.get('NumberOfFrames', '')
            data.SliceThickness = patient_info.get('SliceThickness', '')
            # For scanner
            data.SliceLocation = patient_info.get('SliceLocation', '')
            data.StationName = patient_info.get('StationName', '')
            data.InstanceNumber = patient_info.get('InstanceNumber', '')
            value = self.assoc.send_c_move(data, 'CAOBO', query_model='S')
            if value:
                print(value)
                data_list = []
                for (status, identifier) in value:
                    if status.get('Status', ''):
                        if status.Status == 65280:
                            name = '{}.dcm'.format(patient_info.get('SeriesInstanceUID', ''))
                            file = os.path.join(settings.LOCAL_DIC_PATH, name)
                            if not os.path.exists(file):
                                with open(file, 'wb') as f:
                                    f.write(identifier)
                self.assoc.release()
                return data_list
        else:
            print('pacs connect fail')

    def recive_pacs(self):
        ae = AE(ae_title='CAOBO')
        # Add the requested presentation contexts (Storage SCU)
        ae.requested_contexts = StoragePresentationContexts
        ae.supported_contexts = VerificationPresentationContexts
        # Add a supported presentation context (QR Move SCP)
        # Start listening for incoming association requests
        ae.start_server(('************', 6000))

    def get_instance_wado(self, study_uid, series_uid, instance_uid_raw):
        instance_uid = str()
        if instance_uid_raw.find('-') != -1:
            instance_uid = instance_uid_raw.split('-')[0]
        else:
            instance_uid = instance_uid_raw
        instance_url = 'http://' + self.pacs_address + ':' + str(
            settings.ORTHANC_Docker_PORT) + '/wado?' + 'requestType=WADO' + '&contentType=application/dicom' + \
                       '&studyUID=' + study_uid + '&seriesUID=' + \
                       series_uid + '&objectUID=' + instance_uid
        try:
            if instance_uid_raw.find('-') != -1:
                name = '{}.dcm'.format(instance_uid)
                file = os.path.join(settings.DOWNLOAD_PATH, name)
                if not os.path.exists(file):
                    response = requests.get(instance_url, auth=(self.username, self.password))
                    with open(file, 'wb') as f:
                        f.write(response.content)
                image = sitk.ReadImage(file)
                img = sitk.GetArrayFromImage(image)
                frame = int(instance_uid_raw.split('-')[1])
                ds = pydicom.dcmread(file)
                ds.PixelData = img[frame]
                path = os.path.join(settings.DOWNLOAD_PATH, '{}.dcm'.format(uuid.uuid1()))
                ds.save_as(path)
                with open(path, 'rb') as f:
                    image_data = f.read()
                os.remove(path)
                return image_data
            else:
                response = requests.get(instance_url, auth=(self.username, self.password))
                return response
        except SystemError as e:
            print(e)

    def save_dicom_img(self, files, normal):
        self.ae = AE(ae_title=self.aet)
        if normal:
            self.ae.requested_contexts = StoragePresentationContexts
        else:
            # for context in StoragePresentationContexts:
            self.ae.add_requested_context('1.2.840.10008.*******.1.2', '1.2.840.10008.********')
        # context_a = build_context(CTImageStorage, '1.2.840.10008.********')
        # self.ae.add_requested_context(context_a)
        self.assoc = self.ae.associate(self.pacs_address, self.port)
        series_uid = []
        if self.assoc.is_established:
            for file in files:
                dataset = read_file(file)
                series_uid = dataset.SeriesInstanceUID
                if series_uid not in series_uid:
                    series_uid.append(series_uid)
                value = self.assoc.send_c_store(dataset)
        self.assoc.release()
        return series_uid

    def get_study_series(self, study_id, queryRetrievel='SERIES'):
        """获取study 下面的所以series uid"""
        self.ae = AE(ae_title=self.aet)
        self.ae.requested_contexts = QueryRetrievePresentationContexts
        self.assoc = self.ae.associate(self.pacs_address, self.port)
        # print(self.aet, "---",self.port,"---", self.pacs_address, "_----",queryRetrievel)
        if self.assoc.is_established:
            data = Dataset()
            data.QueryRetrieveLevel = queryRetrievel
            data.StudyInstanceUID = study_id
            data.SeriesInstanceUID = ''
            value = self.assoc.send_c_find(data, query_model='S')
            if value:
                data_list = []
                for (status, identifier) in value:
                    # print(status, "***********", identifier)
                    if status.get('Status', ''):
                        if status.Status == 65280:
                            SeriesInstanceUID = identifier.get('SeriesInstanceUID')
                            data_list.append(SeriesInstanceUID)
                self.assoc.release()
                return data_list
        else:
            print('pacs connect fail')


if __name__ == '__main__':
    a = {
        "target_ip": "***********",
        "target_ae": "DockerOrthanc",
        "target_port": 4242
    }
    init = PACSUtil(**a)
    print(init.port, "--", init.aet, "--", init.local_aet, "---", init.pacs_address)
    serias_id = init.get_study_series(study_id='********.1107.5.1.4.53621.30000014070423572131200000418')
    print(serias_id)
    # init.recive_pacs()
    pa_info = dict(SeriesInstanceUID='********.1107.5.1.4.53621.30000014070423244203100005720')
    # print(pa_info)
    print(init.retrieval_all_patient(pa_info, queryRetrievel="STUDY"))
    # print(init.get_data())
    # print(init.get_series('********.1107.5.2.30.25223.2018102812501547821788575.0.0.0'))
    b = init.retrieval_some_image_new(pa_info, queryRetrievel="SERIES")
    for i in b:
        print(i)
        b.send("abort")
