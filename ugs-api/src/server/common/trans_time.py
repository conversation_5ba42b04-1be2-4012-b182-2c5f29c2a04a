from django.utils import timezone as django_timezone
from pytz import timezone
import datetime
import os
import sys


#
# def transport_time_fmt(t_datetime=None, fmt="%Y-%m-%d %H:%M:%S", is_fmt=False):
#     if not t_datetime:
#         t_datetime = datetime.datetime.utcnow()
#     if isinstance(t_datetime, str):
#         fmt_datetime = datetime.datetime.strptime(t_datetime, fmt)
#         current_tz = django_timezone.get_current_timezone()
#         s = fmt_datetime.astimezone(current_tz)
#         utc_zone = timezone('UTC')
#         return s.astimezone(utc_zone)
#
#     # 获取UTC国际标准时间时区
#     current_tz = timezone('UTC')
#     try:
#         t_datetime = current_tz.localize(t_datetime)
#     except Exception:
#         # 取的时候按django设置的时区上海时区
#         current_tz = django_timezone.get_current_timezone()
#         t_datetime = t_datetime.astimezone(current_tz)
#     if is_fmt:
#         t_datetime = t_datetime.strftime(fmt)
#     return t_datetime
def transport_time_fmt(t_datetime=None, fmt="%Y-%m-%d %H:%M:%S", is_fmt=False):
    if not t_datetime:
        t_datetime = datetime.datetime.now()
    if is_fmt:
        t_datetime = t_datetime.strftime(fmt)
    return t_datetime


if __name__ == '__main__':
    sys.path.append("D:\\code\\cloud-platform-backend")
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.settings")
    now = transport_time_fmt()
    print(now, "*******")
    print(transport_time_fmt(now, is_fmt=True))
