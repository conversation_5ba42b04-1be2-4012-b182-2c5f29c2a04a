#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : remote_api.py
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/4/15 10:41
"""
import json
import pika
import requests
from requests.auth import HTTP<PERSON>asic<PERSON>uth
from pynetdicom import AE, StoragePresentationContexts

from server import settings

import logging

log = logging.getLogger("django")


class OrthancApi:
    """ Orthanc API """

    _BASE_URL = F"http://{settings.ORTHANC_HOST}:{settings.ORTHANC_WEB_PORT}"
    _AUTH = HTTPBasicAuth(settings.ORTHANC_WADO_USERNAME, settings.ORTHANC_WADO_PASSWORD)
    _TIMEOUT = 30

    @staticmethod
    def delete_series(series_id):
        url = F"{OrthancApi._BASE_URL}/series/{series_id}"
        log.info("Orthanc[Delete] > url:{}".format(url))
        response = requests.delete(url=url, auth=OrthancApi._AUTH)
        log.info("Orthanc[Delete] > code:{}".format(response.status_code))

    @staticmethod
    def move(data):
        url = F"{OrthancApi._BASE_URL}/modalities/local/move"
        log.info("Orthanc[Post] > url:{}, data:{}".format(url, data))
        response = requests.post(url=url, json=data, auth=OrthancApi._AUTH)
        log.info("Orthanc[Post] > code:{}".format(response.status_code))
        return response.status_code == 200

    @staticmethod
    def find_tags(image_id):
        url = F"{OrthancApi._BASE_URL}/instances/{image_id}/tags?simplify=true"
        log.info("Orthanc[Get] > url:{}".format(url))
        response = requests.get(url=url, auth=OrthancApi._AUTH, timeout=OrthancApi._TIMEOUT)
        response_body = response.json()
        log.info("Orthanc[Get] > code:{}".format(response.status_code))
        return response_body

    @staticmethod
    def query_image(series_id):
        url = F"{OrthancApi._BASE_URL}/series/{series_id}/instances"
        log.info("Orthanc[Get] > url:{}".format(url))
        response = requests.get(url=url, auth=OrthancApi._AUTH, timeout=OrthancApi._TIMEOUT)
        response_body = response.json()
        log.info("Orthanc[Get] > code:{}".format(response.status_code))
        return response_body


class RabbitMQProducer:
    """ RabbitMQ API """

    _HOST = settings.MQ_HOST
    _PORT = settings.MQ_PORT
    _CREDENTIALS = pika.PlainCredentials(settings.MQ_USERNAME, settings.MQ_PASSWORD)
    _EXCHANGE_WEB_SOCKET = "ws_topic"

    @staticmethod
    def _get_connection():
        return pika.BlockingConnection(
            pika.ConnectionParameters(RabbitMQProducer._HOST, RabbitMQProducer._PORT,
                                      credentials=RabbitMQProducer._CREDENTIALS,
                                      blocked_connection_timeout=300)
        )

    @staticmethod
    def simple_send(queue_name, message):
        """
        发送消息到队列

        :param queue_name: 队列名称
        :param message: 消息
        :return:
        """

        connection = RabbitMQProducer._get_connection()
        channel = connection.channel()
        try:
            channel.queue_declare(queue=queue_name, durable=True)
        except:
            channel = connection.channel()
            channel.queue_delete(queue=queue_name)
            channel.queue_declare(queue=queue_name, durable=True)
        body = json.dumps(message)
        channel.basic_publish(exchange='', routing_key=queue_name,
                              properties=pika.BasicProperties(content_type="application/json", delivery_mode=2),
                              body=body)
        log.info("RabbitMQ[send] > queue: {}, body: {}".format(queue_name, body))
        connection.close()

    @staticmethod
    def ws_notify(routing_key, message):
        """
        WebSocket通知

        :param routing_key: 路由键
        :param message: 消息
        :return:
        """

        connection = RabbitMQProducer._get_connection()
        channel = connection.channel()
        channel.exchange_declare(exchange=RabbitMQProducer._EXCHANGE_WEB_SOCKET,
                                 exchange_type='topic', durable=True)
        body = json.dumps(message)
        channel.basic_publish(exchange=RabbitMQProducer._EXCHANGE_WEB_SOCKET,
                              routing_key=routing_key, body=body)
        log.info("RabbitMQ[WS] > exchange:{}, key:{}, body: {}".format(RabbitMQProducer._EXCHANGE_WEB_SOCKET,
                                                                       routing_key, body))
        connection.close()


class ModalityService:

    @staticmethod
    def send_image(ds: object, remote_host: str, remote_port: int):
        ae = AE(ae_title=settings.LOCAL_AET)
        ae.dimse_timeout = 60
        ae.requested_contexts = StoragePresentationContexts
        assoc = ae.associate(addr=remote_host, port=remote_port)
        if assoc.is_established:
            response = assoc.send_c_store(ds)
            status = response.get("Status", -1)
            log.info("store scu > send dicom to {}:{}, response: 0x{}".format(remote_host, remote_port, status))
            assoc.release()
            return status == 0
        else:
            raise RuntimeError("{}:{} connection failed".format(remote_host, remote_port))


# if __name__ == "__main__":
    # dd = OrthancApi.lookup("1.2.840.113619.2.340.3.*********.867.**********.520")
    # print(dd)
    # dd = OrthancApi.query_study("6336f78d-8bdd6f3b-7e27f28f-ed3392ab-714ae73c")
    # print(dd)
