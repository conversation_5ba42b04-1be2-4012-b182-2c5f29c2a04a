from server import settings
import os

from pydicom.uid import (
    ExplicitVR<PERSON><PERSON><PERSON>ndian,
    ImplicitVR<PERSON><PERSON><PERSON>ndian,
    DeflatedExplicitVRLittleEndian,
    ExplicitVRBigEndian,
    JPEGBaseline,
    JPEGExtended,
    JPEGLosslessP14,
    JP<PERSON><PERSON><PERSON>less,
    JP<PERSON><PERSON><PERSON><PERSON><PERSON>,
    JPEGLSLossy,
    JPEG<PERSON><PERSON><PERSON><PERSON><PERSON>,
    JPEG2000,
    JPEG2000Multi<PERSON>ompo<PERSON><PERSON><PERSON><PERSON>,
    JPEG2000MultiComponent,
    R<PERSON><PERSON><PERSON><PERSON>,
    UID
)
from pydicom import read_file
import traceback

Trancfortanx = [
    ImplicitVRLittleEndian,
    ExplicitVRLittleEndian,
    ExplicitVRBigEndian,
    DeflatedExplicitVRLittleEndian,
    JPEGBaseline,
    JPEGExtended,
    JPEGL<PERSON>lessP14,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    JPEG<PERSON>Lossy,
    JPEG20<PERSON>Lossless,
    JPEG2000,
    JPEG2000MultiComponentLossless,
    JPEG2000MultiComponent,
    RLELossless,
]

from pynetdicom import (AE, StoragePresentationContexts)


class PACSUtilNew(object):

    def __init__(self):
        self.pacs_address = settings.ORTHANC_HOST
        self.aet = settings.ORTHANC_AET
        self.port = settings.ORTHANC_PORT
        self.username = settings.ORTHANC_WADO_USERNAME
        self.password = settings.ORTHANC_WADO_PASSWORD
        self.local_ae = settings.LOCAL_AET

    def save_dicom_img(self, files, dataset=None):
        ae = AE(ae_title=self.local_ae)
        ae.requested_contexts = StoragePresentationContexts
        assoc = ae.associate(self.pacs_address, self.port, ae_title=self.aet)
        print(files)
        value = None
        if assoc.is_established:
            if not dataset:
                try:
                    dataset = read_file(files)
                    # print(dataset)
                except Exception:
                    print(traceback.format_exc())
                    return False, None
            for try_index, try_test in enumerate(Trancfortanx):
                try:
                    dataset.file_meta.TransferSyntaxUID = try_test
                    value = assoc.send_c_store(dataset)
                    if not value:
                        print(try_test)
                        continue
                except Exception:
                    print(traceback.format_exc())
                    print(try_test)
                    continue
                break
            if value:
                print(value)
                if value.get("Status") == 0:
                    assoc.release()
                    print("hhhh")
                    return True, dataset.get('SeriesInstanceUID', None)
                else:
                    assoc.release()
                    return False, None
            else:
                print(value)
                assoc.release()
                return False, None
        return False, None

    def delete_dicom_sop(self, SopInstanceUid):
        ae = AE(ae_title=self.local_ae)
        # PrintJobSOPClass:1.2.840.10008.********,
        # ae.requested_contexts = StoragePresentationContexts
        ae.add_requested_context("1.2.840.10008.*******.1.2")
        assoc = ae.associate(self.pacs_address, self.port, ae_title=self.aet)
        if assoc.is_established:
            print("hhhh")
            # status = assoc.send_n_delete("1.2.840.10008.*******.1.2", SopInstanceUid)
            # print(status)
            # if status.Status == 0x0000:
            #     assoc.release()
            #     return True
            # else:
            #     assoc.release()
            #     return False
        else:
            return False


if __name__ == '__main__':
    data_list = []

    init = PACSUtilNew()
    # init.delete_dicom_sop("1.3.46.670589.33.1.63666207527371373900001.4945162789150162979")
    # data_dir = os.path.join(os.path.dirname(os.path.abspath("__dir__")), "data", "CT")
    # data_dir = os.path.join(os.path.dirname(os.path.abspath("__dir__")), "data", "A006.P000001.D0001")
    data_dir = os.path.join(os.path.dirname(os.path.abspath("__dir__")), "data", "chen")

    for root, dir, files in os.walk(data_dir):
        for file in files:
            data_list.append(os.path.join(data_dir, file))
    print(data_list)
    error_list = []
    init = PACSUtilNew()
    for i in data_list:
        status, message = init.save_dicom_img(i)
        if not status:
            error_list.append(i)
    print(error_list)
