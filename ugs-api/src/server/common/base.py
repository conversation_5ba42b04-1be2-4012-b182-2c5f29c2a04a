# encoding: utf-8
import base64
import datetime
import logging
import re
import time
import traceback
from datetime import date

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.paginator import Paginator
from django.utils import six
from django.utils.crypto import salted_hmac, constant_time_compare
from django.utils.http import int_to_base36, base36_to_int

from server.user.auth import AuthVerify
from server.user.models import AuthorizationCode

log = logging.getLogger("django")

USER_MODEL = get_user_model()


class TokenGenerator(object):
    def __init__(self):
        self.token = None
        self.algorithm = 'pbkdf2_sha256'
        self.key_salt = 'django.contrib.auth.tokens.TokenGenerator'

    @staticmethod
    def days(date_):
        return (date_ - date(2001, 1, 1)).days

    @staticmethod
    def today():
        return date.today()

    @staticmethod
    def make_hash_value(user, timestamp):
        login_timestamp = '' if user.last_login is None else user.last_login.replace(microsecond=0, tzinfo=None)
        return six.text_type(user.pk) + user.password + six.text_type(login_timestamp) + six.text_type(timestamp)

    def _make_token_with_timestamp(self, user, timestamp):
        ts_b36 = int_to_base36(timestamp)
        hash_sha1 = salted_hmac(
            self.key_salt,
            self.make_hash_value(user, timestamp)).hexdigest()[::2]
        return "{}-{}-{}".format(user.username, ts_b36, hash_sha1)

    def make_token(self, user=None):
        self.token = self._make_token_with_timestamp(user, self.days(self.today()))
        return self.token

    def check_token(self, user, token):
        if not (user and token):
            return False
        # valid_char = re.findall(r'\w', token)
        # if user and len(valid_char) == 2:
        if user:
            _token = '-'.join(str(token).rsplit('-', 2)[1:])
        else:
            _token = token
        try:
            ts_b36 = _token.split('-')[0]
        except:
            return False
        try:
            ts = base36_to_int(ts_b36)
        except:
            return False
        if not constant_time_compare(
                self._make_token_with_timestamp(user, ts),
                token
        ):
            return False
        if (self.days(self.today()) - ts) > settings.PASSWORD_RESET_TIMEOUT_DAYS:
            return False
        return True


def page_split(object_list, page_index, page_per_count=None, serializer=True, is_more=False, is_tree=False,
               mysqlmodels=None):
    """
    分页模块
    :param object_list: 分页的查询结果集
    :param page_index: 当前页
    :param page_per_count: 每页显示条数,默认20
    :param serializer: 是否序列化
    :return:
    """
    if not page_per_count:
        page_per_count = settings.PER_PAGE_COUNT
    paginator_object = Paginator(object_list, page_per_count)
    page_objects = paginator_object.get_page(page_index)
    # print(page_objects.object_list)
    if serializer:
        record = []
        condition = {}
        for model in page_objects.object_list:
            if is_more:
                model_dict = model.to_more_dict()
            else:
                if is_tree:
                    info = model.to_dict()
                    condition["parent"] = model.uuid
                    condition["is_delete"] = False
                    model_dict = recusion_tree(mysqlmodels, condition, info)
                else:
                    model_dict = model.to_dict()
            record.append(model_dict)
        # record = [model.to_dict() for model in page_objects.object_list]
    else:
        record = [i for i in page_objects]
    return {
        "record": record,
        "page_index": page_index,
        "page_count": paginator_object.num_pages,
        "has_previous": page_objects.has_previous(),
        "has_next": page_objects.has_next()
    }


def check_user_info(request):
    if request:
        username = ''
        token = request.META.get('HTTP_AUTHORIZATION', '')
        if token:
            token = str(token).split()[-1]
        if not token:
            return None
        valid_char = re.findall(r'\W', str(token))
        if len(valid_char) == 2:
            username = str(token).split('-', 2)[0]
        if username:
            user = USER_MODEL.objects.filter(username=username).first()
            if user:
                token_check = TokenGenerator()
                success = token_check.check_token(user, token)
                if success:
                    return user
    return None


def tree_creation(models, condition):
    # 传入models：表明
    # condition：dict（过滤条件）
    models_queryset = models.objects.filter(**condition)
    result = []
    if not models_queryset:
        return None
    for queryset in models_queryset:
        info = queryset.to_dict()
        condition["parent"] = queryset.uuid
        condition["is_delete"] = False
        recusion_info = recusion_tree(models, condition, info)
        result.append(recusion_info)
    return result


def recusion_tree(models, condition, info):
    # condition为过滤条件，进行搜索使用:
    # dict(parent = null) 来进行第一级别查找
    # 进行流程以及组织架构的递归统一处理
    models_queryset = models.objects.filter(**condition)
    info['children'] = []
    if not models_queryset:
        return info
    for model in models_queryset:
        data = model.to_dict()
        condition["parent"] = model.uuid
        condition["is_delete"] = False
        children_info = recusion_tree(models, condition, data)
        info['children'].append(children_info)
    return info


def tree_area_list(models=None, data=None, info=dict(), Field=None):
    if not models or not data:
        return None
    queryset = models.objects.filter(**data)
    for i in queryset:
        data = dict()
        if not Field:
            uuid = i.to_dict().get("uuid", None)
            save = i.to_dict()
        else:
            uuid = i.to_dict().get(Field, None)
            save = None
        if not info.get(uuid, None):
            if save:
                info[uuid] = save
            else:
                info[uuid] = None
        data["parent__uuid"] = i.uuid
        data["is_delete"] = False
        tree_area_list(models, data, info, Field=Field)
    # print(data)
    return info.keys() if Field else info.values()


def get_ip(request):
    try:
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]  # 所以这里是真实的ip
            print(ip, "************11111")
        else:
            ip = request.META.get('REMOTE_ADDR')  # 这里获得代理ip
    except:
        ip = None
    return ip


# 授权码的生成以及验证
class AUTHGenerator(object):
    """
    :param:
    startDate:'2020-02-01'
    period:10
    authCode:''

    :return:
    startDate:'2020-02-01'
    period:
    authCode:''
    """

    def __init__(self,
                 startDate='',
                 period=0,
                 authCode=None,
                 machine_code=None):
        self.key = 'U@io%Str#ng' if not machine_code else machine_code
        self.startDate = startDate
        self.period = period
        self.authCode = authCode
        # 预处理machine_code
        if machine_code:
            self.process_machine_code()

    def encrypt(self):
        # 1.对收到的startDate进行解析，分解成年月日
        # 2.对每一位的年月日分别从key中取出对应的值
        # 3.在新生成的字符串的第一个位置加入'-'加入周期长度
        # 4.进行base64位的encode
        start_date = datetime.datetime.strptime(self.startDate, "%Y-%m-%d")
        period_list = list(str(self.period).rjust(5, '0'))
        month_str = str(start_date.month).rjust(2, '0')
        day_str = str(start_date.day).rjust(2, '0')
        start_date_str = str(start_date.year) + period_list[0] + month_str + \
                         period_list[1] + day_str + "".join(period_list[2:])
        salt_str = self.add_salt(start_date_str)
        return self.str_to_base(salt_str)

    def decrypt(self):
        decode_string = self.base_to_str(self.authCode)
        result_string = self.sub_salt(decode_string)
        period_index_list = [4, 7, 10, 11, 12]
        period_content_list = [str(result_string[index]) for index in period_index_list]
        date_content_list = ["-" if index in period_index_list else str(x) for index, x in enumerate(result_string)]
        print(date_content_list)
        period = int(''.join(period_content_list))
        start_date = ''.join(date_content_list)
        start_time = datetime.datetime.strptime(start_date[:10], "%Y-%m-%d")
        print(start_time)
        return [start_date[:10], period]

    def base_to_str(self, code):
        code = code.encode(encoding="utf-8")
        return base64.b64decode(code).decode().split('-')[0]

    def str_to_base(self, str_code):
        str_code = str_code + '-' + str(int(time.time()))
        str_code = str_code.encode(encoding="utf-8")
        return base64.b64encode(str_code).decode()

    def add_salt(self, start_date_str):
        start_date_list = list(start_date_str)
        code_string_list = [self.key[int(date)] for date in start_date_list]
        return ''.join(code_string_list)

    def sub_salt(self, decode_str):
        decode_string_list = list(decode_str)
        string_list = [self.key.find(_str) for _str in decode_string_list]
        return string_list

    def process_machine_code(self):
        """
        机器码：在使用前进行预处理
        :return:
        """
        order_data = []
        for i in self.key:
            if i not in order_data:
                order_data.append(i)
        use_key = order_data + list("@$&*^#)+{?")
        self.key = "".join(use_key)

# def check_auth_code(request):
#     if request:
#         queryset = AuthorizationCode.objects.all()
#         if queryset:
#             _auth = queryset.first()
#             end_time = _auth.auth_end_date
#             now_time = datetime.datetime.now()
#             total_sec = (now_time - end_time).total_seconds()
#             if total_sec >= 0:
#                 return None
#             return _auth.create_user
#         # token = request.META.get('HTTP_AUTHORIZATION', '')
#         # if token:
#         #     token = str(token).split()[-1]
#         #     _auth = AUTHGenerator(authCode=token)
#         #     auth_list = _auth.decrypt()
#         #     if auth_list:
#         #         now_time = datetime.datetime.now()
#         #         end_time = datetime.datetime.strptime(auth_list[0], "%Y-%m-%d") + datetime.timedelta(days=auth_list[1])
#         #         total_sec = (now_time - end_time).total_seconds()
#         #         if total_sec >= 0:
#         #             return None
#         #         return token
#     return None


def check_auth_code(request):
    queryset = AuthorizationCode.objects.all()
    if not queryset.exists():
        return None
    auth_obj = queryset.first()
    create_user = auth_obj.create_user
    start_date = auth_obj.auth_start_date
    end_date = auth_obj.auth_end_date
    current_time = datetime.datetime.now()

    if not (current_time >= start_date and current_time <= end_date):
        return None
    try:
        auth_generator = AuthVerify()
        # 机器码获取的频率暂定每天更新一次，  在一天的范围内是否已经重新获取了机器码和解密
        result = auth_generator.check_need_get_machine_code(current_time, create_user)
        return result
    except:
        log.info(f"check_auth_code > error: {traceback.format_exc()}")
        return None


