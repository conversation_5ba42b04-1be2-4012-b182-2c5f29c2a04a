#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : views
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/19 11:23
"""
import logging
import datetime

from django.db.models import F
# Create your views here.
from django.http import JsonResponse

from server.common.base import check_auth_code
from server.common.code import RetCode, Const
from server.common.remote_api import RabbitMQProducer
from server.common.views import BaseView
from server.study.models import Study
from server.study.service import StudyCallbackService, StudySeriesService, StudyRecalculateService, StudyService, \
    DeleteViewTools, StudyReportService

log = logging.getLogger("django")


class StudyView(BaseView):
    """检查视图"""

    def get(self, request):
        """
        检查列表查询

        :param request:
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        params = request.GET
        log.info("Study[list] > params:{}".format(params))
        ret_code, data = StudyService.list(params)
        return self.of(ret_code, data=data)

    def put(self, request):
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        body = request.PUT
        studyUid = body.get("studyUid")
        is_confirmed = body.get("is_confirmed")
        if not studyUid:
            return self.of(RetCode.INCOMPLETE_PARAMETERS)
        study_qs = Study.objects.filter(study_instance_uid=studyUid)
        if not study_qs.exists():
            return self.of(RetCode.STUDY_NOT_FOUND)
        study_qs.update(is_confirmed=is_confirmed, gmt_modified=F("gmt_modified"))
        return self.of(RetCode.OK)


class StudyCallbackView(BaseView):
    """检查回调重构"""

    def post(self, request):
        """
        检查回调

        :param request:
        :return:
        """

        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        # 参数校验
        hospital_uid = request_body.get("hospitalUID", Const.DEFAULT_HOSPITAL_UID)
        hospital_name = request_body.get("hospitalName", "")
        study_orthanc_id = request_body.get("studyId", None)
        tags = request_body.get("tags", None)
        if not study_orthanc_id:
            return self.of(RetCode.STUDY_ORTHANC_ID_IS_EMPTY)
        if not tags:
            return self.of(RetCode.STUDY_TAGS_IS_EMPTY)
        study_instance_uid = tags.get("StudyInstanceUID", "")
        if not study_instance_uid:
            return self.of(RetCode.STUDY_INSTANCE_UID_IS_EMPTY)
        log.info("Study[{}] > start callback".format(study_instance_uid))
        callback_service = StudyCallbackService(study_instance_uid, study_orthanc_id, hospital_uid, hospital_name)
        ret_code = callback_service.do_post()
        log.info("Study[callback] > response: {}".format(ret_code.msg))
        return self.of(ret_code)


class StudySeriesView(BaseView):

    def get(self, request, study_instance_uid):
        """
        获取检查下序列

        :param request:
        :param study_instance_uid: 检查UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        log.info("Study[series] > studyInstanceUID: {}, get series".format(study_instance_uid))
        ret_code, data = StudySeriesService.get_series(study_instance_uid)
        return self.of(ret_code, data=data)


class StudySeriesV2(BaseView):

    def get(self, request, study_instance_uid):
        """
        获取检查下序列

        :param request:
        :param study_instance_uid: 检查UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        log.info("Study[series v2] > studyInstanceUID: {}, get series".format(study_instance_uid))
        ret_code, data = StudySeriesService.get_series_v2(study_instance_uid)
        return self.of(ret_code, data=data)


class StudyRecalculateView(BaseView):

    def post(self, request, study_instance_uid):
        """
        重新计算

        :param request:
        :param study_instance_uid: 检查UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        log.info("Study[recalculate] > studyInstanceUID: {}, body:{}".format(
            study_instance_uid, request_body))
        ret_code = StudyRecalculateService.recalculate(study_instance_uid, request_body)
        return self.of(ret_code)


class StudyReportView(BaseView):
    def post(self, request, study_id):
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        log.info("Study[report] > studyId: {}, body:{}".format(study_id, request_body))
        ret_code, data = StudyReportService.get_report(study_id, request_body)
        return self.of(ret_code, data=data)


class StudyErrorMsgById(BaseView):

    def get(self, request, study_id):
        """
        根据study主键id查询序列的异常信息

        :param request:
        :param study_id: 检查主键id
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        log.info("Study[errorCode] > studyId: {}".format(study_id))
        data = StudyService.error_list(study_id)
        return self.of(RetCode.OK, data=data)


class DeleteStudyView(BaseView):
    def get(self, request):
        """重构删除接口"""

        response = self.response
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.GET
        period = request_body.get("period", "")

        start_date, end_date = period.split("-")
        start_date = datetime.datetime.strptime(start_date, "%Y%m%d")
        end_date = datetime.datetime.strptime(end_date, "%Y%m%d")
        study_qs = Study.objects.filter(gmt_modified__date__range=(start_date, end_date)).count()
        return self.of(RetCode.OK, data={"count": study_qs})

    def post(self, request):
        """重构删除接口"""

        response = self.response
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        study_list = request_body.get("studyList", [])
        period = request_body.get("period", "")
        delete_study_id = DeleteViewTools.create_delete_data(study_list, period)
        if delete_study_id == -1:
            message = "study not exists"
            return self.fail(400, message=message)
        if delete_study_id is None:
            message = "params can not empty"
            return self.fail(400, message=message)
        # 组装消息
        message = dict(studyList=study_list, period=period, delete_study_id=delete_study_id)
        queue_name = "study_delete_task"
        try:
            RabbitMQProducer.simple_send(queue_name, message)
            log.info(f"Study[delete] > studyList:{study_list} queue_name: {queue_name}, message:{message}")
            response["message"] = "删除成功"
            response["code"] = 200
            response["status"] = True
        except:
            response["message"] = "delete study error"
            response["code"] = 400
            response["status"] = False
        return JsonResponse(response)
