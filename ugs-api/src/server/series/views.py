#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : views
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/18 14:23
"""
import logging

from server.series.models import Series
# Create your views here.
from server.common.base import check_auth_code
from server.common.code import RetCode, Const
from server.common.views import BaseView
from server.series.service import SeriesCallbackService, SeriesImageService, SeriesResultService, AlgoResultService

log = logging.getLogger("django")


class SeriesCallbackView(BaseView):
    """序列回调重构"""

    def post(self, request):
        """
        序列回调

        :param request:
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        request_body = request.POST
        # 参数校验
        hospital_uid = request_body.get("hospitalUID", Const.DEFAULT_HOSPITAL_UID)
        hospital_name = request_body.get("hospitalName", "")
        series_orthanc_id = request_body.get("seriesId", None)
        tags = request_body.get("tags", None)
        if not series_orthanc_id:
            return self.of(RetCode.SERIES_ORTHANC_ID_IS_EMPTY)
        if not tags:
            return self.of(RetCode.SERIES_TAGS_IS_EMPTY)
        series_instance_uid = tags.get("SeriesInstanceUID", "")
        if not series_instance_uid:
            return self.of(RetCode.SERIES_INSTANCE_UID_IS_EMPTY)
        if tags.get("Manufacturer", "") == Const.ALGORITHM_RESULT_MANUFACTURER:
            log.info("Series[callback] > series:{}, ignore result callback".format(series_instance_uid))
            return self.ok(message="result callback")
        log.info("Series[{}] > start callback".format(series_instance_uid))
        callback_service = SeriesCallbackService(series_orthanc_id, tags, hospital_uid, hospital_name)
        ret_code = callback_service.do_post()
        log.info("Series[callback] > response: {}".format(ret_code.msg))
        return self.of(ret_code)


class SeriesImageView(BaseView):

    def get(self, request, series_instance_uid):
        """
        获取序列下图像

        :param request:
        :param series_instance_uid: 序列UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        log.debug("Series[image] > seriesInstanceUID:{}, get images".format(series_instance_uid))
        ret_code, data = SeriesImageService.get_image(series_instance_uid)
        return self.of(ret_code, data=data)


class SeriesResultView(BaseView):

    def get(self, request, series_instance_uid):
        """
        获取算法结果

        :param request:
        :param series_instance_uid: 序列UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        ret_code, data = SeriesResultService.get_result(series_instance_uid)
        return self.of(ret_code, data=data)

    def put(self, request, series_instance_uid):
        """
        更新算法结果

        :param request:
        :param series_instance_uid: 序列UID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        body = request.PUT
        log.info("Series[result] > seriesInstanceUID:{}, body:{}, update text result".format(series_instance_uid, body))
        ret_code = SeriesResultService.update_result(series_instance_uid, body)
        return self.of(ret_code)


class SeriesCprView(BaseView):

    def get(self, request, series_instance_uid):
        """
        血管段查询

        :param request:
        :param series_instance_uid:
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        log.info("Series[cpr] > seriesInstanceUID:{}, get vessel segmentation".format(series_instance_uid))
        ret_code, data = AlgoResultService.get_cpr(series_instance_uid)
        return self.of(ret_code, data=data)


class SeriesTdcView(BaseView):

    def get(self, request, series_id):
        """
        血管段查询

        :param request:
        :param series_id: 序列ID
        :return:
        """
        creator = check_auth_code(request)
        if not creator:
            return self.of(RetCode.UNAUTHORIZED)
        params = request.GET
        idx = params.get("idx", "")
        log.info("Series[TDC] > seriesId:{}, idx:{}, get TDC curve".format(series_id, idx))
        if not series_id or not idx:
            return self.of(RetCode.INCOMPLETE_PARAMETERS)
        idx_array = idx.split(",")
        if len(idx_array) != 3:
            log.info("Series[TDC] > invalid idx: {}".format(idx))
            return self.of(RetCode.INCOMPLETE_PARAMETERS)
        series = Series.objects.filter(id=series_id).first()
        if not series:
            log.info("Series[TDC] > series not found: {}".format(series_id))
            return self.of(RetCode.SERIES_NOT_FOUND)
        if series.type != Const.ALGORITHM_TYPE_CTP:
            log.info("Series[TDC] > invalid series : {}".format(series_id))
            return self.of(RetCode.SERIES_INVALID_ALGORITHM_TYPE)
        idx_array = [int(i) for i in idx_array]
        ret_code, data = AlgoResultService.get_tdc(series, idx_array)
        return self.of(ret_code, data=data)

