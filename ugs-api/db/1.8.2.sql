-- ----------------------------
-- Database
-- ----------------------------
DROP DATABASE IF EXISTS `algorithm`;
CREATE DATABASE `algorithm` DEFAULT COLLATE utf8mb4_unicode_ci;

DROP DATABASE IF EXISTS `cloud`;
CREATE DATABASE `cloud` DEFAULT COLLATE utf8mb4_unicode_ci;

USE `algorithm`;

-- ----------------------------
-- Table structure for algorithm_task
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_task`;
CREATE TABLE `algorithm_task`
(
    `uuid`           varchar(255) NOT NULL,
    `series_uid`     varchar(255) NOT NULL,
    `user_id`        varchar(255) NOT NULL,
    `algorithm_type` varchar(255) NOT NULL,
    `finish_percent` int(11) NOT NULL,
    `error_code`     int(11) NOT NULL DEFAULT '0' COMMENT '错误码',
    `finish_dated`   datetime(6) NOT NULL,
    `start_dated`    datetime(6) NOT NULL,
    `total_task`     int(11) NOT NULL,
    `finish_task`    int(11) NOT NULL,
    `series_id`      varchar(255) DEFAULT NULL,
    PRIMARY KEY (`uuid`),
    UNIQUE KEY `uk_algorithm_task_series_uid` (`series_uid`)
);

-- ----------------------------
-- Table structure for algorithm_result
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_result`;
CREATE TABLE `algorithm_result`
(
    `uuid`             varchar(255) NOT NULL,
    `image_series`     varchar(255) NOT NULL,
    `algorithm_type`   varchar(255) NOT NULL,
    `algorithm_result` longtext NULL,
    `mask_url`         longtext NULL,
    `index`            int(11) NOT NULL,
    `create_time`      datetime(6) NULL DEFAULT NULL,
    `task_id`          varchar(255) NULL DEFAULT NULL,
    PRIMARY KEY (`uuid`) USING BTREE,
    INDEX              `algorithm_result_task_id_9e9fea2c_fk_algorithm_task_uuid`(`task_id` ASC) USING BTREE,
    CONSTRAINT `algorithm_result_task_id_9e9fea2c_fk_algorithm_task_uuid` FOREIGN KEY (`task_id`) REFERENCES `algorithm_task` (`uuid`) ON DELETE RESTRICT ON UPDATE RESTRICT
);

COMMIT;

USE `cloud`;

-- ----------------------------
-- Table structure for t_config
-- ----------------------------
DROP TABLE IF EXISTS `t_config`;
CREATE TABLE `t_config`
(
    `id`            varchar(255) NOT NULL COMMENT '主键',
    `code`          varchar(64)  NOT NULL DEFAULT '' COMMENT '配置项编码',
    `value`         varchar(512) NOT NULL DEFAULT '' COMMENT '配置项值',
    `default_value` varchar(512) NOT NULL DEFAULT '' COMMENT '配置项默认值',
    `format`        varchar(8)   NOT NULL DEFAULT 'text' COMMENT '属性值格式（text、bool、int、float、json）',
    `category`      varchar(32)  NOT NULL DEFAULT '' COMMENT '分类（platform、ctp、aspects、cta）',
    `tag`           varchar(32)  NOT NULL DEFAULT '' COMMENT '标签，二级分类（init：传给算法的配置；为空表示平台用的参数）',
    `custom`        tinyint      NOT NULL DEFAULT '0' COMMENT '自定义配置（1:是;0:否）',
    `description`   varchar(64)  NOT NULL DEFAULT '' COMMENT '配置描述',
    `gmt_create`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_code` (`code`)
);

-- ----------------------------
-- Records of t_config
-- ----------------------------
-- Platform 配置
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'minCTPcount', '200', '200', 'int', 'platform', '', 'CTP最小图像张数');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'algorithmProcessMode', '1', '1', 'int', 'platform', '', '算法处理方式');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'colorTable', 'jet', 'jet', 'text', 'platform', '', 'Viewer默认伪彩');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'canReportBack', 'False', 'False', 'bool', 'platform', '', '报告回传开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportAspectsSummary', 'True', 'True', 'bool', 'platform', '', 'ASPECTS结果图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtpSummary', 'True', 'True', 'bool', 'platform', '', 'CTP结果图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtpColorMap', 'True', 'True', 'bool', 'platform', '', 'CTP伪彩图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtpMip', 'True', 'True', 'bool', 'platform', '', 'CTP MIP图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtaVr', 'True', 'True', 'bool', 'platform', '', 'CTA VR图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtaMip', 'True', 'True', 'bool', 'platform', '', 'CTA 三维MIP图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportCtaCsMip', 'True', 'True', 'bool', 'platform', '', 'CTA 侧枝循环MIP图');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportBackMethod', 'orthanc', 'orthanc', 'text', 'platform', '', '报告回传方式');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'toshibaSwitch', '0', '0', 'int', 'platform', '', '东芝开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'viewerExtraToolSwitch', '1', '1', 'int', 'platform', '', '额外工具开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reportViewSwitch', '1', '1', 'int', 'platform', '', '报告查看开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'aspectSliceThicknessMin', '2', '2', 'int', 'platform', '', 'ASPECT单帧厚度最小值');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'aspectSliceThicknessMax', '6', '6', 'int', 'platform', '', 'ASPECT单帧厚度最大值');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'autoDeleteSwitch', 'False', 'False', 'bool', 'platform', '', '自动删除开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'diskThreshold', '95', '95', 'int', 'platform', '', '删除的磁盘阈值');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'DeleteStudyNumber', '10', '10', 'int', 'platform', '', '检查每次删除个数');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'algorithmResultModality', 'OT', 'OT', 'text', 'platform', '', '算法结果设备类型');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'oneStopScanSwitch', '0', '0', 'int', 'platform', '', '一站式扫描开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'ctaMultiPhaseSwitch', '0', '0', 'int', 'platform', '', 'CTA多期相开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'ctp_pcolor_source', 'nifti', 'nifti', 'text', 'platform', '', 'CTP伪彩显示源');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'GE_CALLBACK_START', '0', '0', 'int', 'platform', '', 'GE开关');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'isFlightCheck', '0', '0', 'int', 'platform', '', '型检开关');
-- CTP 配置
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Spatial-Smooth-Kernal-Size', '32', '32', 'float', 'ctp', 'init', 'Spatial-Smooth-Kernal-Size');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Spatial-Smooth-Sigma', '2.5', '2.5', 'float', 'ctp', 'init', 'Spatial-Smooth-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Time-Smooth', 'True', 'True', 'bool', 'ctp', 'init', 'Time-Smooth');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Time-Smooth-Kernal-Size', '-1', '-1', 'int', 'ctp', 'init', 'Time-Smooth-Kernal-Size');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Time-Smooth-Sigma', '2', '2', 'float', 'ctp', 'init', 'Time-Smooth-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Gamma-Fit', 'False', 'False', 'bool', 'ctp', 'init', 'Gamma-Fit');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Self-Defined-Interval', 'False', 'False', 'bool', 'ctp', 'init', 'Self-Defined-Interval');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Time-Interval', '1.6', '1.6', 'float', 'ctp', 'init', 'Time-Interval');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Total-Scan-Time', '50', '50', 'float', 'ctp', 'init', 'Total-Scan-Time');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Image-Resample', 'True', 'True', 'bool', 'ctp', 'init', 'Image-Resample');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Generate-Maps-Method', '5', '5', 'int', 'ctp', 'init', 'Generate-Maps-Method');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Feature-Smooth-Type', '-1', '-1', 'int', 'ctp', 'init', 'Feature-Smooth-Type');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-CBF-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-CBF-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-CBV-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-CBV-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-MTT-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-MTT-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-TMax-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-TMax-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-TTP-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-TTP-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Smooth-PS-Sigma', '0.5,0.5,0', '0.5,0.5,0', 'text', 'ctp', 'init', 'Smooth-PS-Sigma');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF15-Optimize-Thresholds', '2,0,5', '2,0,5', 'text', 'ctp', 'init', 'CBF15-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF30-Optimize-Thresholds', '2,0,5', '2,0,5', 'text', 'ctp', 'init', 'CBF30-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF34-Optimize-Thresholds', '2,0,5', '2,0,5', 'text', 'ctp', 'init', 'CBF34-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF38-Optimize-Thresholds', '2,0,5', '2,0,5', 'text', 'ctp', 'init', 'CBF38-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF50-Optimize-Thresholds', '2,0,5', '2,0,5', 'text', 'ctp', 'init', 'CBF50-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'CBF70-Optimize-Thresholds', '2,0,5', '2,0,5', 'text', 'ctp', 'init', 'CBF70-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'TMax10-Optimize-Thresholds', '2,0,5', '2,0,5', 'text', 'ctp', 'init', 'TMax10-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'TMax8-Optimize-Thresholds', '2,0,5', '2,0,5', 'text', 'ctp', 'init', 'TMax8-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'TMax6-Optimize-Thresholds', '2,0,5', '2,0,5', 'text', 'ctp', 'init', 'TMax6-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'TMax4-Optimize-Thresholds', '2,0,5', '2,0,5', 'text', 'ctp', 'init', 'TMax4-Optimize-Thresholds');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Remove-Csf', 'False', 'False', 'bool', 'ctp', 'init', 'Remove-Csf');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Remove-CsfC', 'False', 'False', 'bool', 'ctp', 'init', 'Remove-CsfC');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Remove-Vessel', 'False', 'False', 'bool', 'ctp', 'init', 'Remove-Vessel');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Generate-Ill-Images-Summary', 'False', 'False', 'bool', 'ctp', 'init', 'Generate-Ill-Images-Summary');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Draw-Ill-Images-Index', 'True', 'True', 'bool', 'ctp', 'init', 'Draw-Ill-Images-Index');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Mip-LW', '80,150', '80,150', 'text', 'ctp', 'init', 'Mip-LW');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Motion-Correction', 'True', 'True', 'bool', 'ctp', 'init', 'Motion-Correction');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Threshold-Segmentation-Range', '20,100', '20,100', 'float', 'ctp', 'init', '阈值分割范围');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'ctpReportMerge', '2', '2', 'int', 'ctp', '', 'CTP报告合并');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'ctpFalseColor', 'jet', 'jet', 'text', 'ctp', 'init', 'CTP参数图伪彩');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Sort-Tags', '0x0020|0x0013,0x0020|0x0012,0x0008|0x0032,0x0018|0x1060,0x0008|0x0018', '0x0020|0x0013,0x0020|0x0012,0x0008|0x0032,0x0018|0x1060,0x0008|0x0018', 'text', 'ctp', 'init', 'Sort-Tags');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'Tmax-Keep-Affected-Side', 'False', 'False', 'bool', 'ctp', 'init', '去除健侧低灌注（True：去除健侧；False：不去除健侧）');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `custom`, `description`) VALUES (UUID(), 'Paint-Debug-Info', 'False', 'False', 'bool', 'ctp', 'init', 1, 'Paint-Debug-Info');
-- ASPECTS 配置
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'keep_largest_component', '1', '1', 'int', 'aspects', 'init', 'keep_largest_component');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'm1_m6_fixed', '1', '1', 'int', 'aspects', 'init', 'm1_m6_fixed');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'thre', '0.96', '0.96', 'float', 'aspects', 'init', 'thre');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'thre_pc', '0.96', '0.96', 'float', 'aspects', 'init', 'thre_pc');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'confidences_thre', '0.9946,0.9981,0.9956,0.8867,0.9975', '0.9946,0.9981,0.9956,0.8867,0.9975', 'text', 'aspects', 'init', 'confidences_thre');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'reverse', '0', '0', 'int', 'aspects', 'init', 'reverse');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'volume_nidus_thre', '0.4', '0.4', 'float', 'aspects', 'init', 'volume_nidus_thre');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'volume_thre', '1', '1', 'int', 'aspects', 'init', 'volume_thre');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'full_conv', '0', '0', 'int', 'aspects', 'init', 'full_conv');
INSERT INTO `t_config` (`id`, `code`, `value`, `default_value`, `format`, `category`, `tag`, `description`) VALUES (UUID(), 'max_brain_length', '180', '180', 'int', 'aspects', 'init', 'max_brain_length');

-- ----------------------------
-- Table structure for t_hospital
-- ----------------------------
DROP TABLE IF EXISTS `t_hospital`;
CREATE TABLE `t_hospital`
(
    `id`            varchar(255) NOT NULL COMMENT '主键',
    `hospital_uid`  varchar(64)  NOT NULL DEFAULT '' COMMENT '医院标识',
    `hospital_name` varchar(128) NOT NULL DEFAULT '' COMMENT '医院名称',
    `gmt_create`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_hospital_uid` (`hospital_uid`)
);

-- ----------------------------
-- Table structure for t_study
-- ----------------------------
DROP TABLE IF EXISTS `t_study`;
CREATE TABLE `t_study`
(
    `id`                 varchar(255)  NOT NULL COMMENT '主键',
    `hospital_uid`       varchar(64)  NOT NULL DEFAULT '0' COMMENT '医院标识',
    `study_instance_uid` varchar(128) NOT NULL DEFAULT '' COMMENT '检查实例唯一标识符',
    `study_id`           varchar(32)  NOT NULL DEFAULT '' COMMENT '检查编号',
    `study_datetime`     datetime              DEFAULT NULL COMMENT '检查日期时间',
    `study_description`  varchar(255) NOT NULL DEFAULT '' COMMENT '检查描述',
    `patient_id`         varchar(64)  NOT NULL DEFAULT '' COMMENT '患者编号',
    `patient_name`       varchar(64)  NOT NULL DEFAULT '' COMMENT '患者名称',
    `patient_sex`        varchar(32)  NOT NULL DEFAULT '' COMMENT '患者性别',
    `patient_age`        varchar(32)  NOT NULL DEFAULT '' COMMENT '患者年龄',
    `patient_weight`     varchar(32)  NOT NULL DEFAULT '' COMMENT '患者体重',
    `patient_birthdate`  varchar(64)  NOT NULL DEFAULT '' COMMENT '患者出生日期',
    `api_version`        varchar(32)  NOT NULL DEFAULT '' COMMENT '接口版本',
    `toshiba`            tinyint(1) NOT NULL DEFAULT '0' COMMENT '东芝数据（0:否;1:是）',
    `orthanc_id`         varchar(255) NOT NULL DEFAULT '' COMMENT 'Orthanc标识',
    `is_confirmed`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否确认',
    `gmt_create`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_study_instance_uid` (`study_instance_uid`),
    KEY                  `idx_study_gmt_modified` (`gmt_modified`),
    KEY                  `idx_study_datetime` (`study_datetime`)
);
-- ----------------------------
-- Table structure for t_series
-- ----------------------------
DROP TABLE IF EXISTS `t_series`;
CREATE TABLE `t_series`
(
    `id`                  varchar(255) NOT NULL COMMENT '主键',
    `hospital_uid`       varchar(64)  NOT NULL DEFAULT '0' COMMENT '医院标识',
    `series_instance_uid` varchar(128) NOT NULL DEFAULT '' COMMENT '序列实例唯一标识符',
    `modality`            varchar(64)  NOT NULL DEFAULT '' COMMENT '设备类型',
    `series_description`  varchar(255) NOT NULL DEFAULT '' COMMENT '序列描述',
    `series_number`       varchar(16)  NOT NULL DEFAULT '' COMMENT '序列编号',
    `series_date`         varchar(8)   NOT NULL DEFAULT '' COMMENT '序列拍摄日期',
    `series_time`         varchar(16)  NOT NULL DEFAULT '' COMMENT '序列拍摄时间',
    `body_part_examined`  varchar(32)  NOT NULL DEFAULT '' COMMENT '检查部位',
    `slice_thickness`     varchar(16)  NOT NULL DEFAULT '' COMMENT '层厚',
    `type`                varchar(32)  NOT NULL DEFAULT '' COMMENT '序列类型（aspects/aspects_ar/ctp/ctp_ar/cta/cta_ar/other）',
    `downloaded`          tinyint(1) NOT NULL DEFAULT '0' COMMENT '下载完成（0:否;1:是）',
    `finish_percent`      smallint(6) NOT NULL DEFAULT '0' COMMENT '算法进度',
    `thumbnail`           varchar(255) NOT NULL DEFAULT '' COMMENT '缩略图',
    `thumbnail_path`      varchar(512) NOT NULL DEFAULT '' COMMENT '缩略图地址',
    `original_series`     varchar(128) NOT NULL DEFAULT '' COMMENT '原始序列',
    `geapi_status`        smallint         NULL            COMMENT 'GE api状态',
    `orthanc_id`          varchar(255) NOT NULL DEFAULT '' COMMENT 'Orthanc标识',
    `study_id`            varchar(255) NOT NULL COMMENT '检查ID',
    `gmt_create`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_series_instance_uid` (`series_instance_uid`),
    KEY                   `fk_series_study_id` (`study_id`),
    CONSTRAINT `fk_series_study_id` FOREIGN KEY (`study_id`) REFERENCES `t_study` (`id`)
);

-- ----------------------------
-- Table structure for t_series_algorithm
-- ----------------------------
DROP TABLE IF EXISTS `t_series_algorithm`;
CREATE TABLE `t_series_algorithm`
(
    `id`                  varchar(255)  NOT NULL COMMENT '主键',
    `series_instance_uid` varchar(128)  NOT NULL DEFAULT '' COMMENT '序列实例唯一标识符',
    `comment`             varchar(1024) NOT NULL DEFAULT '' COMMENT '算法匹配说明',
    `gmt_create`          datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`        datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                   `idx_series_alg_instance_uid` (`series_instance_uid`)
);

-- ----------------------------
-- Table structure for t_series_split
-- ----------------------------
DROP TABLE IF EXISTS `t_series_split`;
CREATE TABLE `t_series_split`
(
    `id`                  varchar(255)  NOT NULL COMMENT '主键',
    `series_instance_uid` varchar(128)  NOT NULL DEFAULT '' COMMENT '序列实例唯一标识',
    `original_series`     varchar(128)  NOT NULL DEFAULT '' COMMENT '原始序列唯一标识',
    `image_number`        int           NOT NULL DEFAULT '0' COMMENT '图像数量',
    `type`                smallint      NOT NULL DEFAULT '0' COMMENT '拆分类型（1:一站式CTP;2:多期相CTA）',
    `gmt_create`          datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`        datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                   `idx_series_split_instance_uid` (`series_instance_uid`)
);

-- ----------------------------
-- Table structure for t_series_algorithm
-- ----------------------------
DROP TABLE IF EXISTS `t_feature_map`;
CREATE TABLE `t_feature_map`
(
    `id`                  varchar(255) NOT NULL COMMENT '主键',
    `study_instance_uid`  varchar(128) NOT NULL DEFAULT '' COMMENT '检查实例唯一标识符',
    `series_instance_uid` varchar(128) NOT NULL DEFAULT '' COMMENT '序列实例唯一标识符',
    `type`                varchar(16)  NOT NULL DEFAULT '' COMMENT '类型（cbf, cbv, mtt, tmax, ttp, ps）',
    `window_width`        varchar(32)  NOT NULL DEFAULT '' COMMENT '自适应窗宽',
    `window_level`        varchar(32)  NOT NULL DEFAULT '' COMMENT '自适应窗位',
    `path`                varchar(512) NOT NULL DEFAULT '' COMMENT '保存路径',
    `gmt_create`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                   `idx_feature_map_study_series` (`study_instance_uid`,`series_instance_uid`)
);

-- ----------------------------
-- Table structure for authorizationcode
-- ----------------------------
DROP TABLE IF EXISTS `authorizationcode`;
CREATE TABLE `authorizationcode`
(
    `uuid`            varchar(255) NOT NULL,
    `create_user`     varchar(255) NOT NULL,
    `auth_local`      varchar(255) NOT NULL,
    `auth_code`       longtext     NOT NULL,
    `auth_period`     int(11) NOT NULL,
    `auth_start_date` datetime(6) NULL DEFAULT NULL,
    `auth_end_date`   datetime(6) NULL DEFAULT NULL,
    `timestamp`       datetime(6) NULL DEFAULT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for callback_dicom
-- ----------------------------
DROP TABLE IF EXISTS `callback_dicom`;
CREATE TABLE `callback_dicom`
(
    `uuid`                varchar(255) NOT NULL,
    `hospital_uid`        varchar(64)  NOT NULL DEFAULT '0' COMMENT '医院标识',
    `sop_instance_uid`    varchar(128) NOT NULL DEFAULT '' COMMENT '图像标识',
    `study_instance_uid`  varchar(128) NOT NULL DEFAULT '' COMMENT '检查标识',
    `series_instance_uid` varchar(128) NOT NULL DEFAULT '' COMMENT '序列标识',
    `instance_number`     smallint(6) NOT NULL DEFAULT '0' COMMENT '影像编号',
    `slice_thickness`     varchar(16)  NOT NULL DEFAULT '0' COMMENT '切片层厚',
    `slice_position`      varchar(16)  NOT NULL DEFAULT '0' COMMENT '切片位置',
    `path`                varchar(512) NOT NULL DEFAULT '' COMMENT '图像地址',
    `sop_orthanc_uuid`    varchar(255) NOT NULL DEFAULT '' COMMENT 'OrthancID',
    `protocol_name`       varchar(255) NOT NULL DEFAULT '' COMMENT '协议名称',
    `timestamp`           datetime(6) NOT NULL,
    `updatetimestamp`     datetime(6) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE,
    UNIQUE KEY `uk_callback_dicom_sop_uid` (`sop_instance_uid`),
    KEY                   `idx_callback_dicom_001` (`study_instance_uid`,`series_instance_uid`) USING BTREE
);

-- ----------------------------
-- Table structure for mail_auto
-- ----------------------------
DROP TABLE IF EXISTS `mail_auto`;
CREATE TABLE `mail_auto`
(
    `uuid`             varchar(255) NOT NULL,
    `type`             varchar(255) NOT NULL,
    `status`           varchar(255) NULL DEFAULT NULL,
    `auto`             int(11) NOT NULL,
    `method`           int(11) NOT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `comment`          longtext NULL,
    `is_delete`        tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

INSERT INTO `cloud`.`mail_auto` (`uuid`, `type`, `status`, `auto`, `method`, `timestamp`, `update_timestamp`, `comment`, `is_delete`) VALUES (UUID(), 'email', NULL, 0, 0, NOW(), NOW(), NULL, 0);

-- ----------------------------
-- Table structure for mail_config
-- ----------------------------
DROP TABLE IF EXISTS `mail_config`;
CREATE TABLE `mail_config`
(
    `uuid`             varchar(255) NOT NULL,
    `address`          varchar(255) NULL DEFAULT NULL,
    `password`         varchar(255) NULL DEFAULT NULL,
    `type`             varchar(255) NOT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `comment`          longtext NULL,
    `is_delete`        tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for mail_history
-- ----------------------------
DROP TABLE IF EXISTS `mail_history`;
CREATE TABLE `mail_history`
(
    `uuid`             varchar(255) NOT NULL,
    `send_address`     varchar(255) NULL DEFAULT NULL,
    `receive_address`  longtext NULL,
    `send_time`        datetime(6) NOT NULL,
    `send_status`      int(11) NOT NULL,
    `object_id`        longtext NULL,
    `message`          varchar(255) NULL DEFAULT NULL,
    `theme`            varchar(255) NULL DEFAULT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `comment`          longtext NULL,
    `is_delete`        tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for pacs_dicom_num
-- ----------------------------
DROP TABLE IF EXISTS `pacs_dicom_num`;
CREATE TABLE `pacs_dicom_num`
(
    `uuid`                varchar(255) NOT NULL,
    `number`              int(11) NOT NULL,
    `aet`                 varchar(255) NULL DEFAULT NULL,
    `ip`                  varchar(255) NULL DEFAULT NULL,
    `port`                varchar(255) NULL DEFAULT NULL,
    `timestamp`           datetime(6) NOT NULL,
    `comment`             longtext NULL,
    `series_instance_uid` longtext NULL,
    `study_instance_uid`  longtext NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for pacs_server
-- ----------------------------
DROP TABLE IF EXISTS `pacs_server`;
CREATE TABLE `pacs_server`
(
    `id`         varchar(255) NOT NULL,
    `aet`        varchar(255) NOT NULL,
    `ip_address` varchar(255) NOT NULL,
    `port`       int(11) NULL DEFAULT NULL,
    `alie_name`  varchar(255) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
);

-- ----------------------------
-- Table structure for push_config
-- ----------------------------
DROP TABLE IF EXISTS `push_config`;
CREATE TABLE `push_config`
(
    `uuid`           varchar(255) NOT NULL,
    `algorithm_type` varchar(255) NULL DEFAULT NULL,
    `key`            longtext NULL,
    `value`          longtext NULL,
    `timestamp`      datetime(6) NOT NULL,
    `comment`        longtext NULL,
    `is_delete`      tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for record_time
-- ----------------------------
DROP TABLE IF EXISTS `record_time`;
CREATE TABLE `record_time`
(
    `uuid`                varchar(255) NOT NULL,
    `category`            varchar(64) NULL DEFAULT NULL,
    `series_instance_uid` varchar(256) NULL DEFAULT NULL,
    `sop_instance_uid`    varchar(256) NULL DEFAULT NULL,
    `consume_time`        double NULL DEFAULT NULL,
    `comment`             longtext NULL,
    `create_time`         datetime(6) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);

-- ----------------------------
-- Table structure for version
-- ----------------------------
DROP TABLE IF EXISTS `version`;
CREATE TABLE `version`
(
    `uuid`             varchar(255) NOT NULL,
    `server_type`      varchar(255) NOT NULL,
    `version`          varchar(255) NULL DEFAULT NULL,
    `timestamp`        datetime(6) NOT NULL,
    `update_timestamp` datetime(6) NOT NULL,
    `comment`          longtext NULL,
    `is_delete`        tinyint(1) NOT NULL,
    PRIMARY KEY (`uuid`) USING BTREE
);


-- ----------------------------
-- Table structure for t_pdf_report
-- ----------------------------
DROP TABLE IF EXISTS `t_pdf_report`;
CREATE TABLE `t_pdf_report`
(
    `id`                 varchar(255) NOT NULL,
    `patient_name`       varchar(255)          DEFAULT NULL,
    `male`               varchar(255)          DEFAULT NULL,
    `age`                varchar(255) NOT NULL,
    `check_no`           varchar(255)          DEFAULT NULL,
    `image_no`           varchar(255) NOT NULL,
    `check_datetime`     varchar(64)           DEFAULT NULL,
    `create_datetime`    datetime(6) NOT NULL,
    `update_time`        datetime(6) NOT NULL,
    `cta_report_text`    longtext,
    `cta_image`          longtext COMMENT 'CTA报告图片',
    `cta_conclusion`     varchar(255) NOT NULL DEFAULT '' COMMENT 'CTA影像诊断意见',
    `ctp_report_text`    longtext,
    `ctp_image`          longtext COMMENT 'CTP报告图片',
    `ctp_conclusion`     varchar(255) NOT NULL DEFAULT '' COMMENT 'CTP影像诊断意见',
    `ncct_report_text`   longtext,
    `ncct_image`         longtext COMMENT 'NCCT报告图片',
    `ncct_conclusion`    varchar(255) NOT NULL DEFAULT '' COMMENT 'NCCT影像诊断意见',
    `study_instance_uid` varchar(255)          DEFAULT NULL,
    `study_id`           varchar(255) NOT NULL DEFAULT '' COMMENT '检查ID',
    `report_date`        varchar(32)           DEFAULT NULL,
    `conclusion`         varchar(255)          DEFAULT NULL,
    `audit_doctor`       varchar(32)           DEFAULT NULL,
    `report_doctor`      varchar(32)           DEFAULT NULL,
    PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `t_delete_study`;
CREATE TABLE `t_delete_study`
(
    `id`            INTEGER AUTO_INCREMENT NOT NULL PRIMARY KEY COMMENT '主键id',
    `count`         INTEGER UNSIGNED NOT NULL COMMENT '数量',
    `deleted_count` INTEGER UNSIGNED NOT NULL COMMENT '已删除数量',
    `start_date`    datetime ( 6 ) NULL COMMENT '开始时间',
    `end_date`      datetime ( 6 ) NULL COMMENT '结束时间',
    `study_list`    LONGTEXT NOT NULL COMMENT '删除的检查',
    `is_finished`   bool     NOT NULL COMMENT '是否完成',
    `gmt_create`    datetime ( 6 ) NOT NULL COMMENT '创建时间',
    `gmt_modified`  datetime ( 6 ) NOT NULL COMMENT '更新时间'
);

DROP TABLE IF EXISTS `t_delete_study_detail`;
CREATE TABLE `t_delete_study_detail`
(
    `id`                 int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `study_instance_uid` varchar(255) NOT NULL COMMENT '检查表id',
    `mysql`              tinyint(1) DEFAULT NULL COMMENT 'mysql是否删除',
    `mongo`              tinyint(1) DEFAULT NULL COMMENT 'mongo是否删除',
    `static`             tinyint(1) DEFAULT NULL COMMENT 'static是否删除',
    `orthanc`            tinyint(1) DEFAULT NULL COMMENT 'orthanc是否删除',
    `gmt_create`         datetime(6) NOT NULL COMMENT '创建时间',
    `gmt_modified`       datetime(6) NOT NULL COMMENT '更新时间',
    `delete_study_id`    int(11) NOT NULL COMMENT '检删除查表id',
    `data_info`          longtext COMMENT '数据信息',
    PRIMARY KEY (`id`),
    KEY                  `idx_t_delete_study_detai_delete_study_id_fk` (`delete_study_id`),
    CONSTRAINT `idx_t_delete_study_detai_delete_study_id_fk` FOREIGN KEY (`delete_study_id`) REFERENCES `t_delete_study` (`id`)
);

DROP TABLE IF EXISTS `t_user`;
CREATE TABLE `t_user`
(
    `id`        int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `account`   varchar(255) NOT NULL COMMENT '用户名',
    `password`  varchar(255) NOT NULL COMMENT '密码',
    `is_delete` tinyint(1) NOT NULL COMMENT '是否删除',
    PRIMARY KEY (`id`)
);

INSERT INTO `cloud`.`t_user` (`account`, `password`, `is_delete`) VALUES ('admin', 'YWRtaW4=', 0);


DROP TABLE IF EXISTS `authorization_record`;
CREATE TABLE `authorization_record`
(
    `id`           INTEGER AUTO_INCREMENT NOT NULL PRIMARY KEY COMMENT "主键id",
    `auth_code`    LONGTEXT NOT NULL COMMENT "授权码",
    `gmt_create`   datetime ( 6 ) NOT NULL COMMENT "创建时间",
    `gmt_modified` datetime ( 6 ) NOT NULL COMMENT "更新时间"
);

-- ----------------------------
-- Table structure for det_user
-- ----------------------------
DROP TABLE IF EXISTS `det_user`;
CREATE TABLE `det_user`
(
    `id`           varchar(36) NOT NULL COMMENT '主键',
    `nickname`     varchar(64) NOT NULL DEFAULT '' COMMENT '用户昵称',
    `username`     varchar(64) NOT NULL DEFAULT '' COMMENT '用户名',
    `password`     varchar(64) NOT NULL DEFAULT '' COMMENT '密码',
    `gmt_create`   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_det_username` (`username`)
);
INSERT INTO `det_user` (`id`, `nickname`, `username`, `password`, `gmt_create`, `gmt_modified`) VALUES (UUID(), 'DEToolUser', 'DEToolUser', '$2b$12$O06b90zvRHnGH8RlulOcSutlzwaykCEQAYi9JS25yFdtPx/W4uf9O', NOW(), NOW());

-- ----------------------------
-- Table structure for det_task
-- ----------------------------
DROP TABLE IF EXISTS `det_task`;
CREATE TABLE `det_task`
(
    `id`           varchar(36)  NOT NULL COMMENT '主键',
    `status`       smallint(6) NOT NULL DEFAULT '0' COMMENT '任务状态(-1:下载失败;0:已创建;1:打包中;2:待下载)',
    `url`          varchar(255) NOT NULL DEFAULT '' COMMENT '下载地址',
    `study_id`     varchar(36)  NOT NULL DEFAULT '' COMMENT '检查ID',
    `gmt_create`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_det_task_study_id` (`study_id`)
);

COMMIT;
