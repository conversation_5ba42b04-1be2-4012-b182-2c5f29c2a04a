version: "3"
services:
  ugs-transport:
    container_name: "ugs-transport"
    image: harbor.unionstrongtech.com/ugs/backend:0.0.4
    volumes:
      - ./dist:/ugs_transport/dist
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    working_dir: /ugs_transport/
    env_file:
      - .env
    command: bash -c "pip install --force-reinstall dist/*.whl && ugstransport"
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true

