#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : setup.py
@Project : ugs-transport
<AUTHOR> mingxing
@Date    : 2023/3/29 11:21
"""
from setuptools import setup, Extension
from setuptools.command.build_py import build_py as build_py_orig
from Cython.Build import cythonize
import src.ugs_transport.package_info as pi


ext_modules = [
    Extension("ugs_transport.*", ["src/ugs_transport/*.py"]),
    Extension("ugs_transport.utils.*", ["src/ugs_transport/utils/*.py"])
]


class BuildPy(build_py_orig):
    def build_packages(self):
        pass


setup(
    name=pi.name,
    author=pi.author,
    url=pi.url,
    cmdclass={"build_py": BuildPy},
    ext_modules=cythonize(ext_modules, language_level="3"),
    version=pi.version,
    packages=[pi.name],
    package_dir={"": "src"},
    platforms=pi.platforms,
    description=pi.description,
    long_description=pi.long_description,
    license="MIT",
    entry_points={
        "console_scripts": [
            "ugstransport = ugs_transport.consumer:main"
        ]
    }
)
