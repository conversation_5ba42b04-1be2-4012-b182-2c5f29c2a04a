#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2021/3/30 下午2:23
# @File    : transport_algorithm.py
# 此文件用于算法串行和并行的任务中转用
import json
import os
import sys
import threading
import time
import traceback
import socket
import pika

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(BASE_DIR)

from utils.logger import MyLogger
from utils.remote_api import RabbitMQProducer
from utils.const import Config
from utils.connection_manager import RabbitMQConnectionManager
import package_info as pi

log = MyLogger()

_env = os.environ
MQ_HOST = _env.get('MQ_HOST', '************')
MQ_PORT = _env.get('MQ_PORT', 5673)
MQ_USERNAME = _env.get('MQ_USERNAME', 'unionstrong')
MQ_PASSWORD = _env.get('MQ_PASSWORD', 'UnionStrong@2020')

# 全局变量,串行任务时，判断任务是否在运行
IS_WORKING = False


class TransportAlgorithmThreading(threading.Thread):
    """
    分发算法任务的线程 - 使用改进的连接管理器
    """

    def __init__(self):
        super().__init__()
        self.connection_manager = None
        self.should_stop = False

    def process_data_callback(self, channel, method, properties, body):
        """处理接收到的消息"""
        log.info("transport > receive message: {}".format(body.decode("utf-8")))
        global IS_WORKING

        try:
            body = json.loads(body)
            task_type = body.get('taskType')
            algorithm_type = body.get('algorithmType', '')

            # 支持更多算法类型
            if algorithm_type not in ["ctp", "aspects", "cta", "center_line"]:
                log.error('transport > invalid algorithm type: {}'.format(algorithm_type))
                return

            body["tags"] = dict()

            # 串行任务处理
            if task_type == '1':
                log.info('transport > serial task, working status: {}'.format(IS_WORKING))
                while IS_WORKING:
                    time.sleep(1)
                RabbitMQProducer.simple_send(F"algorithm_{algorithm_type}_task", body)
                IS_WORKING = True
            elif task_type == '2':
                log.info('transport > parallel task')
                RabbitMQProducer.simple_send(F"algorithm_{algorithm_type}_task", body)
            else:
                log.warning("transport > unknown task type: {0}".format(task_type))

        except Exception as e:
            log.error("transport > message processing error: {0}".format(e))

    def run(self):
        """启动消费者线程"""
        log.info("waiting for message To exit press CTRL+C, {}:{}".format(Config.SERVICE_NAME, pi.version))

        # 发送版本信息
        try:
            version_info = dict(version=pi.version, serverType=Config.SERVICE_NAME)
            RabbitMQProducer.simple_send("algorithm_ctp_aspects_result", version_info)
        except Exception as e:
            log.error("transport > update version error: {0}".format(e))

        # 创建连接管理器
        self.connection_manager = RabbitMQConnectionManager(
            queue_name="transport_algorithm_task",
            callback=self.process_data_callback,
            connection_name="transport-algorithm"
        )

        # 开始消费消息
        try:
            self.connection_manager.start_consuming()
        except KeyboardInterrupt:
            log.info("transport > received interrupt signal")
        except Exception as e:
            log.error("transport > unexpected error: {0}".format(e))
        finally:
            self.stop()

    def stop(self):
        """停止消费者"""
        self.should_stop = True
        if self.connection_manager:
            self.connection_manager.stop()

    def get_status(self):
        """获取连接状态"""
        if self.connection_manager:
            return self.connection_manager.get_status()
        return {"status": "not_initialized"}


class ChangeWorkingStatusThreading(threading.Thread):
    """
    改变算法容器运行状态的线程 - 使用改进的连接管理器
    """

    def __init__(self):
        super().__init__()
        self.connection_manager = None
        self.should_stop = False

    def process_data_callback(self, channel, method, properties, body):
        """处理工作状态变更消息"""
        log.info("transport > receive message: {}".format(body.decode("utf-8")))

        try:
            # 更新标识算法容器的状态
            global IS_WORKING
            body = json.loads(body)
            is_working = body.get('is_working')
            IS_WORKING = is_working
            log.info('transport > change status to {}'.format(IS_WORKING))
        except Exception as e:
            log.error("transport > status message processing error: {0}".format(e))

    def run(self):
        """启动状态监听线程"""
        log.info("transport > starting working status listener")

        # 创建连接管理器
        self.connection_manager = RabbitMQConnectionManager(
            queue_name="algorithm_working_status",
            callback=self.process_data_callback,
            connection_name="transport-status"
        )

        # 开始消费消息
        try:
            self.connection_manager.start_consuming()
        except KeyboardInterrupt:
            log.info("transport > status listener received interrupt signal")
        except Exception as e:
            log.error("transport > status listener unexpected error: {0}".format(e))
        finally:
            self.stop()

    def stop(self):
        """停止状态监听器"""
        self.should_stop = True
        if self.connection_manager:
            self.connection_manager.stop()

    def get_status(self):
        """获取连接状态"""
        if self.connection_manager:
            return self.connection_manager.get_status()
        return {"status": "not_initialized"}


def main():
    """
    主函数 - 启动所有服务线程
    """
    log.info("ugs-transport > starting service")

    # 创建线程实例
    transport_thread = TransportAlgorithmThreading()
    status_thread = ChangeWorkingStatusThreading()

    try:
        # 启动服务线程
        transport_thread.start()
        status_thread.start()

        log.info("ugs-transport > all services started successfully")

        # 主线程保持运行
        while True:
            time.sleep(60)

    except KeyboardInterrupt:
        log.info("ugs-transport > received shutdown signal")
    except Exception as e:
        log.error("ugs-transport > unexpected error: {0}".format(e))
    finally:
        # 优雅关闭
        log.info("ugs-transport > shutting down services")

        # 停止服务线程
        transport_thread.stop()
        status_thread.stop()

        log.info("ugs-transport > service stopped")


if __name__ == '__main__':
    main()
