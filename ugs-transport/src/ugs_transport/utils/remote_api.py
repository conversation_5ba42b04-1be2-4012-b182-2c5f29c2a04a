#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : remote_api
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/8/23 11:34
"""
import json
import os

import pika

from utils.logger import MyLogger
_env = os.environ

log = MyLogger()


class RabbitMQProducer:
    """ RabbitMQ API """

    _HOST = _env.get('MQ_HOST', '************')
    _PORT = _env.get('MQ_PORT', 5673)
    _CREDENTIALS = pika.PlainCredentials(_env.get('MQ_USERNAME', 'unionstrong'),
                                         _env.get('MQ_PASSWORD', 'UnionStrong@2020'))
    _EXCHANGE_WEB_SOCKET = "ws_topic"

    @staticmethod
    def _get_connection():
        return pika.BlockingConnection(
            pika.ConnectionParameters(RabbitMQProducer._HOST, RabbitMQProducer._PORT,
                                      credentials=RabbitMQProducer._CREDENTIALS,
                                      blocked_connection_timeout=300)
        )

    @staticmethod
    def simple_send(queue_name, message):
        """
        发送消息到队列

        :param queue_name: 队列名称
        :param message: 消息
        :return:
        """

        connection = RabbitMQProducer._get_connection()
        channel = connection.channel()
        try:
            channel.queue_declare(queue=queue_name, durable=True)
        except:
            channel = connection.channel()
            channel.queue_delete(queue=queue_name)
            channel.queue_declare(queue=queue_name, durable=True)
        body = json.dumps(message)
        channel.basic_publish(exchange='', routing_key=queue_name,
                              properties=pika.BasicProperties(content_type="application/json", delivery_mode=2),
                              body=body)
        log.info("RabbitMQ[send] > queue: {}, body: {}".format(queue_name, body))
        connection.close()