#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
RabbitMQ连接管理器 - 简化版本，兼容pika 0.12.0
只保留心跳检测和重试机制
"""
import os
import socket
import time
import traceback

import pika

from utils.logger import MyLogger

log = MyLogger()

_env = os.environ
MQ_HOST = _env.get('MQ_HOST', '************')
MQ_PORT = _env.get('MQ_PORT', 5673)
MQ_USERNAME = _env.get('MQ_USERNAME', 'unionstrong')
MQ_PASSWORD = _env.get('MQ_PASSWORD', 'UnionStrong@2020')


class RabbitMQConnectionManager:
    """
    RabbitMQ连接管理器 - 简化版本，兼容pika 0.12.0
    只保留心跳检测和重试机制
    """

    def __init__(self, queue_name, callback, connection_name="default"):
        """
        初始化连接管理器

        :param queue_name: 队列名称
        :param callback: 消息处理回调函数
        :param connection_name: 连接名称（用于日志标识）
        """
        self.queue_name = queue_name
        self.callback = callback
        self.connection_name = connection_name

        # 连接相关
        self.connection = None
        self.channel = None
        self.should_stop = False
        
    def _create_connection(self):
        """
        创建RabbitMQ连接 - 简化版本，兼容pika 0.12.0
        """
        try:
            credentials = pika.PlainCredentials(MQ_USERNAME, MQ_PASSWORD)
            connection_params = pika.ConnectionParameters(
                host=MQ_HOST,
                port=int(MQ_PORT),
                virtual_host='/',
                credentials=credentials,
                heartbeat=0  # pika 0.12.0中设置为0禁用心跳
            )

            self.connection = pika.BlockingConnection(connection_params)

            # 设置socket keepalive
            try:
                if hasattr(self.connection, '_impl') and hasattr(self.connection._impl, 'socket'):
                    conn_socket = self.connection._impl.socket
                    conn_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            except:
                pass  # 忽略socket设置错误

            self.channel = self.connection.channel()
            self.channel.queue_declare(queue=self.queue_name, durable=True)

            log.info("{0} > connection established".format(self.connection_name))
            return True

        except Exception as e:
            log.error("{0} > connection failed: {1}".format(self.connection_name, e))
            return False
    
    def _close_connection(self):
        """
        关闭连接
        """
        try:
            if self.channel:
                self.channel.close()
            if self.connection:
                self.connection.close()
        except:
            pass  # 忽略关闭错误
        finally:
            self.channel = None
            self.connection = None
    
    def _reconnect(self):
        """
        重新连接 - 简化版本
        """
        log.info("{0} > attempting to reconnect...".format(self.connection_name))
        self._close_connection()

        # 尝试重新连接，最多3次
        for attempt in range(3):
            log.info("{0} > reconnection attempt {1}/3".format(self.connection_name, attempt + 1))

            if self._create_connection():
                log.info("{0} > reconnection successful".format(self.connection_name))
                return True

            if attempt < 2:  # 不是最后一次尝试
                time.sleep(5)  # 等待5秒后重试

        log.error("{0} > reconnection failed after 3 attempts".format(self.connection_name))
        return False

    def start_consuming(self):
        """
        开始消费消息 - 简化版本，兼容pika 0.12.0
        """
        log.info("{0} > starting consumer".format(self.connection_name))

        while not self.should_stop:
            try:
                # 确保连接可用
                if not self.connection or not self.channel:
                    if not self._create_connection():
                        log.error("{0} > failed to establish connection, retrying in 10s".format(self.connection_name))
                        time.sleep(10)
                        continue

                # 开始消费 (pika 0.12.0格式)
                log.info("{0} > start consuming messages".format(self.connection_name))
                self.channel.basic_consume(
                    self.callback,  # 直接使用回调函数
                    queue=self.queue_name,
                    no_ack=True
                )

                self.channel.start_consuming()

            except Exception:
                log.error("{0} > consuming error: {1}".format(self.connection_name, traceback.format_exc()))
                self._close_connection()

                if not self.should_stop:
                    log.info("{0} > retrying in 10s".format(self.connection_name))
                    time.sleep(10)
    
    def stop(self):
        """
        停止消费者
        """
        log.info("{0} > stopping consumer".format(self.connection_name))
        self.should_stop = True

        if self.channel:
            try:
                self.channel.stop_consuming()
            except:
                pass

        self._close_connection()
        log.info("{0} > consumer stopped".format(self.connection_name))
