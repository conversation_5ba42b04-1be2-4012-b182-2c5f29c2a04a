version: "3"
services:
  ugs-transport:
    container_name: "ugs-transport"
    image: 172.16.30.132:808/ugs/backend:0.0.4
    volumes:
      - ./src/ugs_transport:/ugs_transport
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    working_dir: /ugs_transport/
    env_file:
      - .env
    command: python3 -u consumer.py
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true

