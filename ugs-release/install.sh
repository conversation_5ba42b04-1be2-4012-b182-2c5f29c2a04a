#!/bin/bash
ROOT_DIR="/home/<USER>"
SERVICES=("ugs-frontend" "ugs-api" "ugs-transport" "ugs-result" "ugs-delete" "ugs-aspects" "ugs-ctp" "ugs-cta" "ugs-det-api" "ctp4d" "ugs-center-line")
echo "token:${UGS_API_TOKEN}, ciApi:${CI_API_V4_URL}, releaseId:${UGS_RELEASE_ID}, device:${DEVICE_USER}:${DEVICE_PASS}@${DEVICE_HOST}"
CUR_DIR=$(pwd)
SSHPASS="sshpass -p$DEVICE_PASS"
DO_SSH="${SSHPASS} ssh -o StrictHostKeyChecking=no ${DEVICE_USER}@${DEVICE_HOST}"

function prop() {
    grep "${1}" package.properties | cut -d'=' -f2 | head -1
}

VERSION=$(prop "ugs-version")

function download() {
  cd $CUR_DIR
  package_name=$(prop "${1}")
  service_dir="${CUR_DIR}/temp/${1}"
  echo "download $package_name to $service_dir"
  mkdir -p $service_dir && cd $service_dir
  wget --no-check-certificate --quiet --method GET --header "PRIVATE-TOKEN: ${UGS_API_TOKEN}" \
    "${CI_API_V4_URL}/projects/${UGS_RELEASE_ID}/packages/generic/ugs/${VERSION}/${package_name}"
  unzip -qo $package_name && rm $package_name
}

function init_mysql() {
  sql_file="${CUR_DIR}/ugs.sql"
  $SSHPASS scp $sql_file $DEVICE_USER@$DEVICE_HOST:$ROOT_DIR
  $DO_SSH "sudo docker exec -i ugs-mysql mysql -uroot -pUnionStrong@2020 < $ROOT_DIR/ugs.sql"
}

function init_mongo() {
  sql_file="${CUR_DIR}/ugs-mongo.json"
  $SSHPASS scp $sql_file $DEVICE_USER@$DEVICE_HOST:$ROOT_DIR
  $DO_SSH "sudo docker cp $ROOT_DIR/ugs-mongo.json ugs-mongodb:/ugs-mongo.json"
  $DO_SSH "sudo docker exec -i ugs-mongodb \
    mongoimport --uri='*********************************************************************************' \
    --collection=score_text \
    --file=ugs-mongo.json \
    --type=json"
}

function init_upgrade() {
  upgrade_dir="${CUR_DIR}/ugs-upgrade"
  $SSHPASS scp -r $upgrade_dir $DEVICE_USER@$DEVICE_HOST:$ROOT_DIR
  $DO_SSH "sudo sed -i -e '\$i \nohup python3 $ROOT_DIR/ugs-upgrade/consumer.py &\n' /etc/rc.local"
}

function copy_version() {
  version_file="${CUR_DIR}/version.txt"
  $SSHPASS scp $version_file $DEVICE_USER@$DEVICE_HOST:$ROOT_DIR
  package_file="${CUR_DIR}/package.properties"
  $SSHPASS scp $package_file $DEVICE_USER@$DEVICE_HOST:$ROOT_DIR
}

function main() {
  echo "install ugs"
  download_dir="${CUR_DIR}/temp"
  if [ -d $download_dir ];then
    rm -rf $download_dir
  fi
  for service in ${SERVICES[@]}; do
    download $service
  done;
  echo "deploy ugs app"
  $DO_SSH "sudo rm -rf $ROOT_DIR/* /clouddata/* /data/ctpdata/*"
  $SSHPASS scp -r $download_dir/* $DEVICE_USER@$DEVICE_HOST:$ROOT_DIR
  for service in ${SERVICES[@]}; do
    if [ "${service}" == "ugs-frontend" ]; then
      continue
    fi
    $DO_SSH "cd ${ROOT_DIR}/${service};sudo docker-compose rm -fs;sudo docker-compose up -d"
  done;
  echo "init mysql"
  init_mysql
  echo "init mongo"
  init_mongo
  echo "init upgrade"
  init_upgrade
  echo "copy version"
  copy_version
}

main