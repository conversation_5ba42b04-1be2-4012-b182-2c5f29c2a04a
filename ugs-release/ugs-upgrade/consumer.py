#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import json
import logging
import logging.handlers
import os
import time
import traceback
 
import pika as pika
 
 
def init():
    log_fmt = "%(asctime)s[%(levelname)s][%(process)d-%(thread)d][%(filename)s:%(lineno)s]: %(message)s"
    log_level = "INFO"
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(log_fmt, datefmt="%Y/%m/%d %H:%M:%S"))
    console_handler.setLevel(log_level)
 
    path = os.path.join("/data/ctpdata/log/upgrade-runner")
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)
    log_path = F"{path}/upgrade-runner.log"
    file_handler = logging.handlers.RotatingFileHandler(log_path, maxBytes=10 * 1024 * 1024,
                                                        backupCount=5, encoding="utf8")
    file_handler.setFormatter(logging.Formatter(log_fmt))
    file_handler.setLevel(log_level)
    logging.basicConfig(
        level=log_level,
        handlers=[console_handler, file_handler]
    )
 
 
def callback(channel, method_frame, properties, body):
    logging.info("receive message: {}".format(body.decode("utf-8")))
    body = json.loads(body)
    action = body.get("action", "")
    if action == "start":
        up_py = "/mnt/ugs_up.py"
        if os.path.exists(up_py):
            logging.info("upgrade start")
            try:
                # 开始升级流程
                os.system(F"python3 {up_py}")
                logging.info("upgrade success")
            except Exception as e:
                logging.info("upgrade failed")
                logging.error(traceback.format_exc())
        else:
            logging.info("USB flash disk is not mounted, ignore message")
        channel.basic_ack(delivery_tag=method_frame.delivery_tag)
        return
    logging.warning("invalid action: {}".format(action))
    channel.basic_ack(delivery_tag=method_frame.delivery_tag)
 
 
if __name__ == "__main__":
    init()
    retry_number = 0
    while True:
        try:
            logging.info("consumer: connect for {} times".format(retry_number+1))
            connection = pika.BlockingConnection(
                pika.ConnectionParameters("localhost", 5673, '/',
                                          credentials=pika.PlainCredentials("unionstrong", "UnionStrong@2020"),
                                          heartbeat=0)
            )
            channel = connection.channel()
            channel.basic_qos(prefetch_count=1)
            queue_name = "ugs_upgrade"
            channel.queue_declare(queue=queue_name, durable=True)
            channel.basic_consume(callback, queue=queue_name, no_ack=False)
            retry_number = 0
            channel.start_consuming()
        except Exception as e:
            logging.error(traceback.format_exc())
            retry_number += 1
            time.sleep(5 * retry_number)
