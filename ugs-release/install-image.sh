#!/bin/bash
ROOT_DIR="/home/<USER>"
SERVICES=("upixel-station-backend" "uguard_aspects" "uguard_ctp")
echo "device:${DEVICE_USER}:${DEVICE_PASS}@${DEVICE_HOST}"
CUR_DIR=$(pwd)
SSHPASS="sshpass -p$DEVICE_PASS"
DO_SSH="${SSHPASS} ssh -o StrictHostKeyChecking=no ${DEVICE_USER}@${DEVICE_HOST}"

function main() {
  echo "install ugs"
  $DO_SSH "sudo rm -rf $ROOT_DIR/* /clouddata/* /data/ctpdata/*"
  rm install*.sh
  $SSHPASS scp -r $CUR_DIR/* $DEVICE_USER@$DEVICE_HOST:$ROOT_DIR
  for service in ${SERVICES[@]}; do
    $DO_SSH "cd ${ROOT_DIR}/${service};sudo docker-compose rm -fs;sudo docker-compose up -d"
    sleep 5
  done;
  for i in {1..10}
  do
    echo "MySQL is unavailable, waiting for it...😴"
    sleep 4
  done
  echo "initializing MySQL"
  $DO_SSH "sudo docker exec -i cloud_platform_mysql mysql -uroot -pUnionStrong@2020 < $ROOT_DIR/ugs.sql"
}

main