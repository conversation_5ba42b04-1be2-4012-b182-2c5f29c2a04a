variables:
  PUBLISH:
    value: "Y"
    description: "是否发布"
    options:
      - "Y"
      - "N"
  PACKAGE_HOST:
    value: "************:180"
    description: "请输入生产的软件仓库地址"
  DEVICE_HOST:
    value: "************"
    description: "请输入服务器的主机地址"
    options:
      - "************"
      - "************"
      - "************"
      - "************"
      - "*************"
  DEVICE_USER:
    value: "unionstrong"
    description: "请输入服务器的登录用户名"
  DEVICE_PASS:
    value: "UnionStrong@2021"
    description: "请输入服务器的登录密码"
  WHEEL_PACKAGE:
    value: "Y"
    description: "是否加密包"
    options:
      - "Y"
      - "N"
default:
  image: *************:808/uguard/ugs-ubuntu:18.04
  tags:
    - k8s-runner
  before_script:
    - CUR_DIR=$(pwd)
    - ls -alrt
install-wheel:
  stage: deploy
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $PUBLISH == "N" && $WHEEL_PACKAGE == "Y" && $DEVICE_HOST && $DEVICE_USER && $DEVICE_PASS
  script:
    - ls -alrt
    - bash install.sh
    - echo "${DEVICE_HOST} deploy ${CI_COMMIT_BRANCH} successfully!"
install-image:
  stage: deploy
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $PUBLISH == "N" && $WHEEL_PACKAGE == "N" && $DEVICE_HOST && $DEVICE_USER && $DEVICE_PASS
  script:
    - ls -alrt
    - bash install-image.sh
    - echo "${DEVICE_HOST} deploy ${CI_COMMIT_BRANCH} successfully!"
publish-wheel:
  stage: deploy
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $PUBLISH == "Y" && $WHEEL_PACKAGE == "Y" && $PACKAGE_HOST
  script:
    - |
      echo "${UGS_API_TOKEN},${PACKAGE_HOST}@${PROD_REPOSITORY_TOKEN}"
      CUR_DIR=$(pwd)
      cd .. && mkdir temp && cd temp
      git clone http://ugs-api-token:${PROD_REPOSITORY_TOKEN}@${PACKAGE_HOST}/uguard-stroke/ugs-release.git
      cd ugs-release
      TARGET_BRANCH=$(git ls-remote origin $CI_COMMIT_BRANCH)
      if [ "${TARGET_BRANCH}" ]; then
        echo "branch $CI_COMMIT_BRANCH already exists"
        git checkout $CI_COMMIT_BRANCH
      else
        echo "branch $CI_COMMIT_BRANCH create"
        git checkout -b $CI_COMMIT_BRANCH
      fi
      ls -alrt
      cp -rf $CUR_DIR/* .
      ls -alrt
      git config --global user.email "ugs-api-token"
      git config --global user.name "${PROD_REPOSITORY_TOKEN}"
      git add .
      git commit -m "update ${CI_COMMIT_BRANCH}"
      git push origin $CI_COMMIT_BRANCH
      echo "sync ${CI_COMMIT_BRANCH} successfully"
      cd $CUR_DIR
      SERVICES=("ugs-frontend" "ugs-api" "ugs-transport" "ugs-result" "ugs-delete" "ugs-aspects" "ugs-ctp" "ugs-cta" "ugs-det-api" "ctp4d" "ugs-center-line")
      VERSION=$(grep ugs-version package.properties | cut -d'=' -f2)
      SOURCE_PACKAGE_REGISTRY="${CI_API_V4_URL}/projects/${UGS_RELEASE_ID}/packages/generic/ugs/${VERSION}"
      TARGET_PACKAGE_REGISTRY="http://${PACKAGE_HOST}/api/v4/projects/6/packages/generic/ugs/${VERSION}"
      for SERVICE_NAME in ${SERVICES[@]}; do
        PACKAGE_NAME=$(grep $SERVICE_NAME package.properties | cut -d'=' -f2 | head -1)
        echo "download $PACKAGE_NAME"
        wget --no-check-certificate --quiet --method GET --header "PRIVATE-TOKEN: ${UGS_API_TOKEN}" "${SOURCE_PACKAGE_REGISTRY}/${PACKAGE_NAME}"
        curl -H "PRIVATE-TOKEN: ${PROD_REPOSITORY_TOKEN}" -T ${PACKAGE_NAME} ${TARGET_PACKAGE_REGISTRY}/${PACKAGE_NAME}
        echo "upload ${PACKAGE_NAME} successfully"
      done;
      echo "publish ${CI_COMMIT_BRANCH} successfully"
publish-image:
  stage: deploy
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $PUBLISH == "Y" && $WHEEL_PACKAGE == "N" && $PACKAGE_HOST
  script:
    - |
      echo "${UGS_API_TOKEN},${PACKAGE_HOST}@${PROD_REPOSITORY_TOKEN}"
      CUR_DIR=$(pwd)
      cp -f install-image.sh install.sh && rm install-image.sh
      cd .. && mkdir temp && cd temp
      git clone http://ugs-api-token:${PROD_REPOSITORY_TOKEN}@${PACKAGE_HOST}/uguard-stroke/ugs-release.git
      cd ugs-release
      WORK_DIR=$(pwd)
      TARGET_BRANCH=$(git ls-remote origin $CI_COMMIT_BRANCH)
      if [ "${TARGET_BRANCH}" ]; then
        echo "branch $CI_COMMIT_BRANCH already exists"
        git checkout $CI_COMMIT_BRANCH
      else
        echo "branch $CI_COMMIT_BRANCH create"
        git checkout -b $CI_COMMIT_BRANCH
      fi
      cp -rf $CUR_DIR/* .
      ls -alrt
      git config --global user.email "ugs-api-token"
      git config --global user.name "${PROD_REPOSITORY_TOKEN}"
      git add .
      git commit -m "update ${CI_COMMIT_BRANCH}"
      git push origin $CI_COMMIT_BRANCH
      echo "publish ${CI_COMMIT_BRANCH} successfully"
