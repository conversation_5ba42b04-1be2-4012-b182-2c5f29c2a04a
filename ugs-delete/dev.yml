version: "3"
services:
  ugs-delete:
    container_name: "ugs-delete"
    image: 172.16.30.132:808/ugs/backend:0.0.4
    volumes:
      - ./src/ugs_delete:/code/ugs_delete
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
      - /home/<USER>/upixel-station-backend/server/static/cpr:/code/server/static/cpr
      - /home/<USER>/upixel-station-backend/server/static/jpg:/code/server/static/jpg
      - /home/<USER>/upixel-station-backend/server/static/vti:/code/server/static/vti

    working_dir: /code/ugs_delete/
    env_file:
      - .env
    command: python3 -u consumer.py
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true
