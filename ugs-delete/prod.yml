version: "3"
services:
  ugs-delete:
    container_name: "ugs-delete"
    image: harbor.unionstrongtech.com/ugs/backend:0.0.4
    volumes:
      - ./dist:/code/ugs_delete/dist
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
      - /home/<USER>/upixel-station-backend/server/static:/code/server/static
    working_dir: /code/ugs_delete/
    env_file:
      - .env
    command: bash -c "pip install --force-reinstall dist/*.whl && ugsdelete"
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true
