#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : models.py
@Project : ugs-backend
<AUTHOR> mingxing
@Date    : 2023/3/20 14:17
"""
import datetime


class Study:
    def __init__(self, id=None, study_instance_uid=None, gmt_modified=None, orthanc_id=None, ):
        self.id = id
        self.study_instance_uid = study_instance_uid
        self.gmt_modified = gmt_modified
        self.orthanc_id = orthanc_id

    class Meta:
        db_name = "cloud"
        table_name = "t_study"
        id_name = "id"
        id_auto = True


class DeleteStudy:
    def __init__(self, id=None, count=None, deleted_count=None, study_list=None, is_finished=None, ):
        self.id = id
        self.count = count
        self.deleted_count = deleted_count
        self.study_list = study_list
        self.is_finished = is_finished
        self.gmt_create = datetime.datetime.now()
        self.gmt_modified = datetime.datetime.now()

    class Meta:
        db_name = "cloud"
        table_name = "t_delete_study"
        id_name = "id"
        id_auto = True


class DeleteStudyDetail:
    def __init__(self, id=None, delete_study_id=None, study_instance_uid=None, data_info=None, ):
        self.id = id
        self.delete_study_id = delete_study_id
        self.study_instance_uid = study_instance_uid
        self.data_info = data_info
        self.gmt_create = datetime.datetime.now()
        self.gmt_modified = datetime.datetime.now()

    class Meta:
        db_name = "cloud"
        table_name = "t_delete_study_detail"
        id_name = "id"
        id_auto = True






class RecordTimeModel:
    def __init__(self, uuid=None, category=None, series_instance_uid=None, consume_time=None, create_time=None):
        self.uuid = uuid
        self.category = category
        self.series_instance_uid = series_instance_uid
        self.consume_time = consume_time
        self.create_time = create_time

    class Meta:
        db_name = "cloud"
        table_name = "record_time"
        id_name = "uuid"
        id_auto = True


class SystemConfig:
    def __init__(self, id=None, code=None, value=None, default_value=None, format=None, category=None, tag=None,
                 custom=None, description=None):
        self.id = id
        self.code = code
        self.value = value
        self.default_value = default_value
        self.format = format
        self.category = category
        self.tag = tag
        self.custom = custom
        self.description = description

    class Meta:
        db_name = "cloud"
        table_name = "t_config"
        id_name = "id"
        id_auto = True


class FeatureMap:
    def __init__(self, id=None, study_instance_uid=None, series_instance_uid=None, type=None, window_width=None,
                 window_level=None, path=None):
        self.id = id
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.type = type
        self.window_width = window_width
        self.window_level = window_level
        self.path = path

    class Meta:
        db_name = "cloud"
        table_name = "t_feature_map"
        id_name = "id"
        id_auto = True


class StudyModel:
    def __init__(self, id=None, study_instance_uid=None):
        self.id = id
        self.study_instance_uid = study_instance_uid

    class Meta:
        db_name = "cloud"
        table_name = "t_study"
        id_name = "id"
        id_auto = True


class Series:
    def __init__(self, id=None, series_instance_uid=None, modality=None, series_description=None, original_series=None,
                 type=None,
                 study_id=None, thumbnail_path=None, gmt_create=None, gmt_modified=None):
        self.id = id
        self.series_instance_uid = series_instance_uid
        self.thumbnail_path = thumbnail_path
        self.modality = modality
        self.series_description = series_description
        self.original_series = original_series
        self.type = type
        self.study_id = study_id
        self.gmt_create = gmt_create
        self.gmt_modified = gmt_modified

    class Meta:
        db_name = "cloud"
        table_name = "t_series"
        id_name = "id"
        id_auto = True


class CallBackDICOM:
    def __init__(self, uuid, sop_instance_uid, study_instance_uid=None, series_instance_uid=None, instance_number=None,
                 patient_id=None, patient_name=None, study_description=None, sop_orthanc_uuid=None, path=None,
                 is_first_download=None, modality=None, series_description=None, slice_thickness=None,
                 protocol_name=None, ip=None, aet=None, comment=None, number=None, is_pushed=None, download_status=None,
                 timestamp=None, updatetimestamp=None):
        self.uuid = uuid
        self.sop_instance_uid = sop_instance_uid
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.instance_number = instance_number
        self.patient_id = patient_id
        self.patient_name = patient_name
        self.study_description = study_description
        self.sop_orthanc_uuid = sop_orthanc_uuid
        self.path = path
        self.is_first_download = is_first_download
        self.modality = modality
        self.series_description = series_description
        self.slice_thickness = slice_thickness
        self.protocol_name = protocol_name
        self.ip = ip
        self.aet = aet
        self.comment = comment
        self.number = number
        self.is_pushed = is_pushed
        self.download_status = download_status
        self.timestamp = timestamp
        self.updatetimestamp = updatetimestamp

    class Meta:
        db_name = "cloud"
        table_name = "callback_dicom"
        id_name = "uuid"
        id_auto = True


class AlgorithmTask:
    def __init__(self, uuid=None, series_uid=None, algorithm_type=None, finish_percent=None, error_code=None):
        self.uuid = uuid
        self.series_uid = series_uid
        self.algorithm_type = algorithm_type
        self.finish_percent = finish_percent
        self.error_code = error_code

    class Meta:
        db_name = "algorithm"
        table_name = "algorithm_task"
        id_name = "uuid"
        id_auto = True


class Algorithm_result:
    def __init__(self, uuid=None, image_series=None, algorithm_type=None, algorithm_result=None, task_id=None,
                 index=None, create_time=None):
        self.uuid = uuid
        self.image_series = image_series
        self.algorithm_type = algorithm_type
        self.algorithm_result = algorithm_result
        self.task_id = task_id
        self.index = index
        self.create_time = create_time

    class Meta:
        db_name = "algorithm"
        table_name = "algorithm_result"
        id_name = "uuid"
        id_auto = True


class PDFReport:

    class Meta:
        db_name = "cloud"
        table_name = "t_pdf_report"
        id_name = "id"
        id_auto = True


class SeriesAlgorithm:

    class Meta:
        db_name = "cloud"
        table_name = "t_series_algorithm"
        id_name = "id"
        id_auto = True
