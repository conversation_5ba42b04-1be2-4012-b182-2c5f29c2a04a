#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import json
import shutil
import pika
import requests
from requests.auth import HTT<PERSON><PERSON>asic<PERSON>uth
from pynetdicom import AE, StoragePresentationContexts

from utils.const import Env

import logging

log = logging.getLogger("ugs-delete")


class RabbitMQProducer:
    """ RabbitMQ API """

    _HOST = Env.MQ_HOST
    _PORT = Env.MQ_PORT
    _CREDENTIALS = pika.PlainCredentials(Env.MQ_USERNAME, Env.MQ_PASSWORD)
    _EXCHANGE_WEB_SOCKET = "ws_topic"

    @staticmethod
    def _get_connection():
        return pika.BlockingConnection(
            pika.ConnectionParameters(RabbitMQProducer._HOST, RabbitMQProducer._PORT,
                                      credentials=RabbitMQProducer._CREDENTIALS,
                                      blocked_connection_timeout=300)
        )

    @staticmethod
    def simple_send(queue_name, message):
        """
        发送消息到队列

        :param queue_name: 队列名称
        :param message: 消息
        :return:
        """

        connection = RabbitMQProducer._get_connection()
        channel = connection.channel()
        try:
            channel.queue_declare(queue=queue_name, durable=True)
        except:
            channel = connection.channel()
            channel.queue_delete(queue=queue_name)
            channel.queue_declare(queue=queue_name, durable=True)
        body = json.dumps(message)
        channel.basic_publish(exchange='', routing_key=queue_name,
                              properties=pika.BasicProperties(content_type="application/json", delivery_mode=2),
                              body=body)
        log.info("RabbitMQ[send] > queue: {}, body: {}".format(queue_name, body))
        connection.close()

    @staticmethod
    def ws_notify(routing_key, message):
        """
        WebSocket通知

        :param routing_key: 路由键
        :param message: 消息
        :return:
        """

        connection = RabbitMQProducer._get_connection()
        channel = connection.channel()
        channel.exchange_declare(exchange=RabbitMQProducer._EXCHANGE_WEB_SOCKET,
                                 exchange_type='topic', durable=True)
        body = json.dumps(message)
        channel.basic_publish(exchange=RabbitMQProducer._EXCHANGE_WEB_SOCKET,
                              routing_key=routing_key, body=body)
        log.info("RabbitMQ[WS] > exchange:{}, key:{}, body: {}".format(RabbitMQProducer._EXCHANGE_WEB_SOCKET,
                                                                       routing_key, body))
        connection.close()
