import traceback

import pymysql
from contextlib import contextmanager
import logging

from utils.const import Env

log = logging.getLogger("ugs-delete")


class MySQLClient():
    # 获取数据库连接

    def __init__(self):
        self.conn = None
        try:
            self.conn = pymysql.connect(
                host=Env.DB_HOST, port=Env.DB_PORT,
                user=Env.DB_USER, passwd=Env.DB_PASSWORD, charset="utf8",
                cursorclass=pymysql.cursors.DictCursor
            )
        except:
            log.error(f"study[delete] > conn error :{traceback.format_exc()}")

    def select(self, obj, fields=None, conditions: dict = None, sorts: dict = None):
        if not conditions:
            conditions = {}
        if not sorts:
            sorts = {}
        sql = ""
        try:
            where = self._get_where_clause(conditions)
            if conditions:
                sql = f"SELECT {','.join(fields) if fields else '*'} FROM `{obj.Meta.db_name}`.`{obj.Meta.table_name}` WHERE {where}"
            else:
                sql = f"SELECT {','.join(fields) if fields else '*'} FROM `{obj.Meta.db_name}`.`{obj.Meta.table_name}`"
            order = ""
            for key, value in sorts.items():
                if key and str(value):
                    order = F"{order} {key} {'desc' if value else 'asc'}, "
            if order:
                order = " order by " + order[:-2]
            sql = f"{sql} {order}"
            with self.conn.cursor() as cur:
                # cur = conn.cursor()
                cur.execute(sql, tuple(conditions.values()))
                results = cur.fetchall()
                return results
        except:
            log.error("MySQL[select] > sql:{}, error:{}".format(sql, traceback.format_exc()))
            return []

    def insert_custom(self, obj, data):
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['%s'] * len(data))
        sql = f'INSERT INTO `{obj.Meta.db_name}`.`{obj.Meta.table_name}` ({columns}) VALUES ({placeholders})'

        with self.conn.cursor() as cur:
            cur.execute(sql, tuple(data.values()))
            lastrowid = cur.lastrowid
            self.conn.commit()
            return lastrowid

    def update_by_conditions(self, obj, data, conditions):
        if not conditions:
            conditions = {}
        sql = ""
        try:
            where = self._get_where_clause(conditions)
            set_clause = ', '.join([f'{key}=%s' for key in data])
            sql = f'UPDATE `{obj.Meta.db_name}`.`{obj.Meta.table_name}` SET {set_clause} WHERE {where}'
            with self.conn.cursor() as cur:
                cur.execute(sql, tuple(data.values()) + tuple(conditions.values()))
                self.conn.commit()
        except:
            log.error("MySQL[update] > sql:{}, error:{}".format(sql, traceback.format_exc()))
            return []

    def delete_by_condition(self, obj, conditions: dict = None):
        if not conditions:
            conditions = {}
        sql = ""
        try:
            where = self._get_where_clause(conditions)
            sql = f"DELETE FROM `{obj.Meta.db_name}`.`{obj.Meta.table_name}` WHERE {where}"
            with self.conn.cursor() as cur:
                cur.execute(sql, tuple(conditions.values()))
                self.conn.commit()
        except:
            log.error("MySQL[delete] > sql:{}, error:{}".format(sql, traceback.format_exc()))
            return []

    @staticmethod
    def _get_where_clause(kwargs):
        result = []
        for key, value in kwargs.items():
            if "__gte" in key:
                key = key.split("__")[0]
                result.append(f"DATE(`{key}`) >= %s")
                continue
            if "__lte" in key:
                key = key.split("__")[0]
                result.append(f"DATE(`{key}`) <= %s")
                continue
            if isinstance(value, list):
                result.append(F"`{key}` in %s")
                continue
            else:
                result.append(f'{key}=%s')
        if result:
            return ' AND '.join(result)
        else:
            return ""

    def close(self):
        log.debug("MySQL > close connection")
        if self.conn:
            self.conn.close()
if __name__ == '__main__':
    import datetime

    # 初始化数据库连接池
    # conn = Connection().get_connection()
    # a = User(conn).select(
    #     fields=["id", "study_list"],
    #     conditions={
    #         # "id": [12, 13],
    #                 "gmt_create__gte":"2023-03-18 13:28:01.264667",
    #                 "gmt_create__lte": "2023-03-21 13:28:01.264667"},
    #
    # sorts={"id": False})
    # print(11111111, a)
    # User(conn).update_by_conditions(
    #     data={"deleted_count":222222},
    #     conditions={"id": [8,9]})
    # User1(conn).delete_by_condition(
    #
    #     conditions={"id": [13]})
    # User(conn).delete_by_condition(
    #
    #     conditions={"id": [2]})
    #
    # User(conn).insert_custom(data={"count": 11111, "deleted_count": 0,
    #                                                        "study_list": ",".join(["111,", "22222"]),
    #                                                        "is_finished": 0, "gmt_create": datetime.datetime.now(),
    #                                                        "gmt_modified": datetime.datetime.now()})
    mysql_client = MySQLClient()
    from delete_study.utils.models import DeleteStudy, DeleteStudyDetail, Study

    delete_study = mysql_client.select(DeleteStudy, fields=["id", "study_list"],
                                       sorts={"gmt_create": True})
    print(delete_study)
    # mysql_client.insert_custom(DeleteStudy, data={"count": 11111, "deleted_count": 0,
    #                          "study_list": ",".join(["111,", "22222"]),
    #                          "is_finished": 0, "gmt_create": datetime.datetime.now(),
    #                          "gmt_modified": datetime.datetime.now()})
    #
    #
    # mysql_client.update_by_conditions(DeleteStudy,
    #     data={"deleted_count":6666},
    #     conditions={"id": [8,9]})
    # mysql_client.delete_by_condition(DeleteStudyDetail,
    #
    #     conditions={"id": [14]})
    # mysql_client.delete_by_condition(DeleteStudy,
    #
    #     conditions={"id": [3]})


    study_exists = mysql_client.select(Study, fields=["id"], )
    print(not study_exists)
