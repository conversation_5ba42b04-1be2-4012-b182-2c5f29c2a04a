#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import os

_env = os.environ


class Env:
    DB_HOST = _env.get('DB_HOST', '*************')
    DB_PORT = int(_env.get('DB_PORT', "3310"))
    DB_USER = _env.get('DB_USER', 'root')
    DB_PASSWORD = _env.get('DB_PASSWORD', 'UnionStrong@2020')

    MQ_HOST = _env.get("MQ_HOST", "*************")
    MQ_PORT = _env.get("MQ_PORT", 5673)
    MQ_USERNAME = _env.get("MQ_USERNAME", "unionstrong")
    MQ_PASSWORD = _env.get("MQ_PASSWORD", "UnionStrong@2020")

    ORTHANC_HOST = _env.get("ORTHANC_HOST", "*************")
    ORTHANC_AET = _env.get("ORTHANC_AET", "DockerOrthanc")
    ORTHANC_PORT = int(_env.get("ORTHANC_PORT", 4242))
    ORTHANC_WADO_PORT = int(_env.get("ORTHANC_WADO_PORT", 8042))
    ORTHANC_WADO_USERNAME = _env.get("ORTHANC_WADO_USERNAME", "unionstrong")
    ORTHANC_WADO_PASSWORD = _env.get("ORTHANC_WADO_PASSWORD", "UnionStrong@2020")
    LOCAL_AET = _env.get("LOCAL_AET", "UNIONSTRONG")

    MONGODB_HOST = _env.get('MONGODB_HOST', '*************')
    MONGODB_PORT = int(_env.get('MONGODB_PORT', '27018'))
    MONGODB_USER = _env.get('MONGO_INITDB_ROOT_USERNAME', "unionstrong")
    MONGODB_PASSWORD = _env.get('MONGO_INITDB_ROOT_PASSWORD', "UnionStrong@2020")


class Version:
    SERVICE_NAME = "ugs-delete"
