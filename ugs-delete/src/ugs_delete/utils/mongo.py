# 封装MongoBD使用中的CURD操作
# C-Create
# return 结果可以用 result.inserted_id或者result.inserted_ids获取
# 保存的id列表和信息
# U-Update
# return 更新结果 传入需要更新的filter字段和更新的内容，可以用与多条记录更新
# 或者单挑记录更新
# R-Retrieve
# 查询操作，return结果，可通过pymongo对内容进行二次操作
# D-Delete
# 删除操作，通过批量删除和独立删除进行操作
import logging

from pymongo import MongoClient
from bson.objectid import ObjectId

from utils.const import Env
log = logging.getLogger("ugs-delete")


class MongoDB(object):

    def __init__(self):
        self.client = MongoClient(Env.MONGODB_HOST, int(Env.MONGODB_PORT), username=Env.MONGODB_USER,
                                  password=Env.MONGODB_PASSWORD)
        self.db = self.client.erecord

    # Create方法
    def create(self, body, table):
        if not isinstance(table, str):
            table = str(table)
        if isinstance(body, dict):
            result = self.db[table].insert_one(body)
            return result
        if isinstance(body, list):
            result = self.db[table].insert_many(body)
            return result

    # Update方法,还需要添加对于条件的控制
    def update(self, body, table,
               condition, condition_action=None,
               is_many=False):
        if not isinstance(table, str):
            table = str(table)
        set = '$set'
        if condition_action:
            set = '${}'.format(condition_action)
        content = {set: body}
        if is_many:
            result = self.db[table].update_many(condition, content)
            return result
        result = self.db[table].update_one(condition, content)
        return result

    # Retrieve方法，进行查询操作
    def query(self, condition, table, condition_action=None):
        if not isinstance(table, str):
            table = str(table)
        if isinstance(condition, list):
            set = '{}'.format(condition_action)
            search = {set: {"$in": condition}}
            log.info("MongoDB[query] > {}".format(search))
            result = self.db[table].find(search)
            return result
        if condition_action:
            set = '{}'.format(condition_action)
            search = {set: condition}
            log.info("MongoDB[query] > {}".format(search))
            result = self.db[table].find(search)
            return result

    # Delete方法
    def delete(self, condition, table, is_many=False, count=0):
        if not isinstance(table, str):
            table = str(table)
        if isinstance(condition, dict):
            if not is_many:
                self.db[table].remove(condition)
            else:
                self.db[table].remove(condition, count)

    def bulk_delete(self, condition_list, table, condition_key):
        condition = {condition_key: {"$in": condition_list}}
        return self.db[table].remove(condition)

    def get(self, table, _id):
        return self.db[table].find_one({"_id": ObjectId(_id)})


if __name__ == '__main__':
    # a = MongoDB()
    # mongodb = MongoDB()
    # results = mongodb.query(ObjectId("5f647eba54e028001133ad5f"), 'record', '_id')
    # print(results)
    # if results:
    #    print("HHHHH")
    # for i in results:
    #     print(i.get("content"))
    mongodb = MongoDB()
    # mongodb.db=
    # record_object_id = query.record_detail_id
    results = mongodb.query(ObjectId("5f652fa65a4fee000136cfe2"), 'algorithm', '_id')
    for i in results:
        print(i.get("result"))
