#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : remote_api.py
@Project : upixel-station-backend
<AUTHOR> mingxing
@Date    : 2022/4/15 10:41
"""
import json
import shutil
import pika
import requests
from requests.auth import HTTP<PERSON>asic<PERSON>uth
from pynetdicom import AE, StoragePresentationContexts

from utils.const import Env

import logging

log = logging.getLogger("ugs-delete")


class OrthancService:
    """ Orthanc API """

    _BASE_URL = F"http://{Env.ORTHANC_HOST}:{Env.ORTHANC_WADO_PORT}"
    _AUTH = HTTPBasicAuth(Env.ORTHANC_WADO_USERNAME, Env.ORTHANC_WADO_PASSWORD)
    _TIMEOUT = 30

    @staticmethod
    def lookup(data):
        url = F"{OrthancService._BASE_URL}/tools/lookup"
        log.debug("Orthanc[request] > url:{}, data:{}".format(url, data))
        response = requests.post(url=url, data=data, auth=OrthancService._AUTH, timeout=OrthancService._TIMEOUT)
        response_body = response.json()
        return response_body

    @staticmethod
    def delete_studies(series_id):
        url = F"{OrthancService._BASE_URL}/studies/{series_id}"
        log.debug("Orthanc[request] > delete, url:{}".format(url))
        response = requests.delete(url=url, auth=OrthancService._AUTH)
        return response

