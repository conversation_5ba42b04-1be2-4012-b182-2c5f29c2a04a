#! /usr/bin/env python
import json
import os
import sys
import re
import threading
import traceback
from ast import literal_eval
import socket

from concurrent.futures import ThreadPoolExecutor, as_completed
import datetime
import shutil
import time

import pika
from bson.objectid import ObjectId

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(BASE_DIR)

from utils.pacs import OrthancService
from utils.const import Env, Version
from utils.logger import MyLogger
from utils.models import Study, DeleteStudy, SystemConfig, Series, AlgorithmTask, DeleteStudyDetail, \
    FeatureMap, Algorithm_result, CallBackDICOM, PDFReport, SeriesAlgorithm
from utils.mongo import MongoDB
from utils.mysql import MySQLClient
from utils.rabbitmq import RabbitMQProducer
import package_info as pi

log = MyLogger()


class DeleteTools(object):
    pool = ThreadPoolExecutor(max_workers=2, thread_name_prefix="deleteStudyThread")

    def start_delete_study(self, delete_study_id, study_slices, count, study_list, period):
        if study_list or period:
            log.info("Study[delete] > munual delete begining")
        mysql_client = MySQLClient()
        if not mysql_client:
            log.info("Study[delete] > mysql connection error")
            return
        index = 1  # 统计删除的个数
        success_study_list = []
        failed_study_list = []
        log.info(
            f"Study[delete] > delete_study_id:{delete_study_id}， study_slices：{study_slices}, count:{count}, study_list:{study_list}, period:{period}")
        for item in study_slices:
            # 创建删除记录
            for study_instance_uid, orthanc_id in item:
                mysql_client.insert_custom(
                    DeleteStudyDetail,
                    data={"delete_study_id": delete_study_id, "study_instance_uid": study_instance_uid,
                          "gmt_create": datetime.datetime.now(), "gmt_modified": datetime.datetime.now()})
            all_task = []
            log.debug(f"Study[delete] > threading.activeCount {threading.activeCount()}")
            for study_instance_uid, orthanc_id in item:
                params = {"study_instance_uid": study_instance_uid, "orthanc_id": orthanc_id}
                task = DeleteTools.pool.submit(self.delete_data_by_study_instance_uid, params)
                all_task.append(task)
            success_list = []
            failed_list = []
            for future in as_completed(all_task):
                study_instance_uid, data = future.result()
                log.debug(
                    f"Study[delete] > total count:{count}, {index}, study_instance_uid:{study_instance_uid}, result: {data}")
                index += 1
                try:
                    delete_study_detail_qs = mysql_client.select(
                        DeleteStudyDetail(),
                        conditions={"delete_study_id": delete_study_id, "study_instance_uid": study_instance_uid},
                        fields=["id"],
                    )

                    status = False
                    data_info = data.get("data_info", {})
                    if data_info:
                        data["data_info"] = json.dumps(data_info)
                    if delete_study_detail_qs:
                        mysql_client.update_by_conditions(
                            DeleteStudyDetail, data=data, conditions={"id": delete_study_detail_qs[0].get("id")})
                    if all(data.values()):
                        success_list.append(study_instance_uid)
                        success_study_list.append(study_instance_uid)
                        status = True
                    else:
                        failed_list.append(study_instance_uid)
                        failed_study_list.append(study_instance_uid)
                    if study_list:
                        notify_message = {
                            "action": "delete",
                            "data": {
                                "StudyInstanceUIDs": [study_instance_uid],
                                "status": status
                            }
                        }
                        queue_name = "ws.notify.study_delete"
                        RabbitMQProducer.ws_notify(queue_name, notify_message)
                        log.info(f"Study[delete] > delete notify, queue:{queue_name}, data: {notify_message}")
                except:
                    log.error(f"Study[delete] > delete notify, error :{traceback.format_exc()}")

            if period:
                notify_message = {
                    "action": "delete",
                    "data": {
                        "successStudyInstanceUIDs": success_list,
                        "failedStudyInstanceUIDs": failed_list,
                    }
                }
                queue_name = "ws.notify.study_bulk_delete"
                try:
                    RabbitMQProducer.ws_notify(queue_name, notify_message)
                    log.info(f"Study[delete] > delete notify, queue:{queue_name}, data: {notify_message}")
                except:
                    log.error(
                        f"Study[delete] > delete notify, queue:{queue_name}, data: {notify_message}  error :{traceback.format_exc()}")

        mysql_client.update_by_conditions(
            DeleteStudy, data={"is_finished": 1, "deleted_count": len(success_study_list)},
            conditions={"id": delete_study_id})
        log.info(f"Study[delete] > callback, end_time : {datetime.datetime.now()}")
        mysql_client.close()

    def delete_data_by_study_instance_uid(self, params):

        result = {
            "mysql": 0,
            "mongo": 0,
            "score": 0,
            "water_uptake_rate": 0,
            "center_line": 0,
            "static": 0,
            "orthanc": 0
        }
        study_instance_uid = params.get("study_instance_uid")
        orthanc_id = params.get("orthanc_id")
        mysql_client = MySQLClient()
        if not mysql_client:
            log.info("Study[delete] > mysql connection error")
            return study_instance_uid, result
        study_ids = mysql_client.select(Study, conditions={"study_instance_uid": study_instance_uid}, fields=["id"])
        if not study_ids:
            return study_instance_uid, result
        study_id = study_ids[0].get("id")
        study_series_uid_dict_list = mysql_client.select(Series, conditions={"study_id": study_id},
                                                         fields=["series_instance_uid"])
        study_series_uid_list = [study_series_uid_dict.get("series_instance_uid") for study_series_uid_dict in
                                 study_series_uid_dict_list]
        result["data_info"] = {
            "series_instance_uid": study_series_uid_list,
            "orthanc_id": orthanc_id
        }
        study_series_uid_list.append(study_instance_uid)
        clear_mongodb_id_list = mysql_client.select(Algorithm_result,
                                                    conditions={"image_series": study_series_uid_list},
                                                    fields=["algorithm_result"])
        clear_mongodb_id_list = [result_dict.get("algorithm_result") for result_dict in clear_mongodb_id_list]
        result["data_info"]["mongo_id"] = list(clear_mongodb_id_list)
        # 删mongodb
        result["mongo"] = self.delete_mongo(clear_mongodb_id_list, study_instance_uid)
        # 删评分结果
        result["score"] = self.delete_score(study_instance_uid)
        # 删除水摄取率结果
        result["water_uptake_rate"] = self.delete_water_uptake_rate(study_series_uid_list)
        # 删除中心线结果
        result["center_line"] = self.delete_center_line(study_series_uid_list)
        # 删static
        result["static"] = self.delete_folder_by_study_instance_uid(study_instance_uid, study_series_uid_list)
        # 删orthanc
        result["orthanc"] = self.delete_orthanc(orthanc_id, study_instance_uid)
        try:
            mysql_client.delete_by_condition(CallBackDICOM, conditions={"study_instance_uid": study_instance_uid})
            mysql_client.delete_by_condition(PDFReport, conditions={"study_instance_uid": study_instance_uid})
            mysql_client.delete_by_condition(FeatureMap, conditions={"study_instance_uid": study_instance_uid})
            mysql_client.delete_by_condition(SeriesAlgorithm, conditions={"series_instance_uid": study_series_uid_list})

            mysql_client.delete_by_condition(Algorithm_result, conditions={"image_series": study_series_uid_list})
            mysql_client.delete_by_condition(AlgorithmTask, conditions={"series_uid": study_series_uid_list})
            mysql_client.delete_by_condition(Series, conditions={"study_id": study_id})
            mysql_client.delete_by_condition(Study, conditions={"study_instance_uid": study_instance_uid})
            result["mysql"] = 1
            log.info(f"Study[delete] > mysql delete study: {study_instance_uid} success")
            mysql_client.close()
        except:
            log.error(f"Study[delete] > delete mysql study: {study_instance_uid} error:{traceback.format_exc()}")
            result["mysql"] = 0

            return study_instance_uid, result

        return study_instance_uid, result

    @staticmethod
    def delete_center_line(study_series_uid_list):
        """删除中心线结果"""
        status = 1
        try:
            mongodb = MongoDB()
            result = mongodb.bulk_delete(
                condition_list=study_series_uid_list,
                condition_key="series_instance_uid",
                table="center_line"
            )
            log.info(f"Study[delete] > delete center line data for study: {study_series_uid_list}, result: {result}")
        except:
            log.error(f"Study[delete] > delete center line data error for study: {study_series_uid_list}, error: {traceback.format_exc()}")
            status = 0
        return status
    
    @staticmethod
    def delete_water_uptake_rate(study_series_uid_list):
        """删除水摄取率结果"""
        status = 1
        try:
            mongodb = MongoDB()
            result = mongodb.bulk_delete(
                condition_list=study_series_uid_list,
                condition_key="series_instance_uid",
                table="water_uptake_rate"
            )
            log.info(f"Study[delete] > delete water uptake rate data for study: {study_series_uid_list}, result: {result}")
        except:
            log.error(f"Study[delete] > delete water uptake rate data error for study: {study_series_uid_list}, error: {traceback.format_exc()}")
            status = 0
        return status
    
    @staticmethod
    def delete_score(study_instance_uid):
        """删除评分结果"""
        status = 1
        try:
            mongodb = MongoDB()
            result = mongodb.delete(
                condition={'study_instance_uid': study_instance_uid},
                table='score'
            )
            log.info(f"Study[delete] > delete score data for study: {study_instance_uid}, result: {result}")
        except:
            log.error(f"Study[delete] > delete score data error for study: {study_instance_uid}, error: {traceback.format_exc()}")
            status = 0
        return status

    @staticmethod
    def delete_mongo(clear_mongodb_id_list, study_instance_uid):
        """删除mongo对应数据"""
        status = 1
        try:
            _mongodb = MongoDB()
            clear_mongodb_id_list = [ObjectId(m) for m in clear_mongodb_id_list]
            _deletemongodb = _mongodb.bulk_delete(condition_list=clear_mongodb_id_list, condition_key="_id",
                                                  table="algorithm")
            log.info(f"Study[delete] > mongodb: bulk delete study: {study_instance_uid}, success:{clear_mongodb_id_list}, result: {_deletemongodb}", )
        except:
            log.error(f"Study[delete] >  mongodb bulk delete study:{study_instance_uid} error:{traceback.format_exc()}")
            status = 0
        return status

    @staticmethod
    def delete_orthanc(orthanc_id, study_instance_uid):
        """
        删除orthanc数据
        """
        status = 1
        try:
            if not orthanc_id:
                response_body = OrthancService.lookup(data=study_instance_uid)
                if response_body:
                    orthanc_id = response_body[0]["ID"]
                log.info("Study[delete] > orthancID is empty, get ID:{}".format(orthanc_id))
            response = OrthancService.delete_studies(orthanc_id)
            log.info(f"Study[delete] > delete orthanc id: {orthanc_id}, study_instance_uid: {study_instance_uid} "
                     f"finish, response:{response.status_code}")
            if response.status_code == 404:
                return 1
            if response.status_code != 200:
                return 0
        except:
            log.error(f"Study[delete] > delete orthanc id: {orthanc_id}, error:{traceback.format_exc()}")
            status = 0
        return status

    @staticmethod
    def delete_folder(folder):
        """
        判断目录是否存在，然后删除
        """
        status = 1
        try:
            if os.path.exists(folder):
                shutil.rmtree(folder)
        except:
            log.error(f"Study[delete] > delete {folder} error:{traceback.format_exc()}")
            status = 0
        return status

    @staticmethod
    def delete_folder_by_study_instance_uid(study_instance_uid, clear_series_list):

        """
        删除静态文件
        /data/ctpdata下目录说明：
            aspects/结果图
            cta/算法结果文件
            ctp/结果图
            dcm/:下载的dicom原图
            static/：CTP参数的灰度图

        /home/<USER>/upixel-station-backend/server/static下的目录说明：
             cpr/ CTA的CPR血管图
             jpg/ CTA的VR
             vti/ CTA的vti文件
        """
        status = 1
        # 删除 dcm (dicom原图)
        for item in [study_instance_uid, *clear_series_list]:
            try:
                dcm_folder = os.path.join("/data/ctpdata", "dcm", item)
                if os.path.exists(dcm_folder):
                    shutil.rmtree(dcm_folder)
            except:
                log.error(f"Study[delete] > delete study:{study_instance_uid} dcm error:{traceback.format_exc()}")
                status = 0
        aspects_folder = os.path.join("/data/ctpdata", "aspects", study_instance_uid)  # aspects 结果图
        cta_folder = os.path.join("/data/ctpdata", "cta", study_instance_uid)  # cta 结果图数据
        ctp_folder = os.path.join("/data/ctpdata", "ctp", study_instance_uid)  # ctp 结果图数据
        ctp_static_folder = os.path.join("/data/ctpdata", "static", study_instance_uid)  # ctp 参数灰度数据
        cta_cpr_folder = os.path.join("/code/server/static", "cpr", study_instance_uid)  # cta cpr血管图数据
        cta_jpg_folder = os.path.join("/code/server/static", "jpg", study_instance_uid)  # cta vp 血管图数据
        cta_vti_folder = os.path.join("/code/server/static", "vti", study_instance_uid)  # cta vti 血管图数据
        cta_vti_folder_list = [os.path.join("/code/server/static", "vti", series_uid) for series_uid in
                               clear_series_list]

        for folder in [aspects_folder, cta_folder, ctp_folder, ctp_static_folder, cta_cpr_folder, cta_jpg_folder,
                       cta_vti_folder, *cta_vti_folder_list]:
            result = DeleteTools.delete_folder(folder)
            if not result:
                status = 0
        log.info(f"Study[delete] > delete static study:{study_instance_uid} finish")
        return status

    @staticmethod
    def list_slice(result_list, number):
        """
        列表切分
        """
        for item in range(0, len(result_list), number):
            yield result_list[item:item + number]


class DeleteStudyTool(threading.Thread):
    pool = ThreadPoolExecutor(max_workers=2, thread_name_prefix="deleteStudyThread")

    def run(self):
        log.info("update version , {}:{}".format(Version.SERVICE_NAME, pi.version))
        try:
            version_info = dict(version=pi.version, serverType=Version.SERVICE_NAME)
            RabbitMQProducer.simple_send("algorithm_ctp_aspects_result", version_info)
        except:
            log.info(f"update version error:{traceback.format_exc()}")
        while True:
            try:
                log.info("delete satrt connection")
                credentials = pika.PlainCredentials(Env.MQ_USERNAME, Env.MQ_PASSWORD)
                connection = pika.BlockingConnection(
                    pika.ConnectionParameters(Env.MQ_HOST, Env.MQ_PORT, '/', credentials, heartbeat=0))
                conn_socket = connection._impl.socket
                conn_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                channel = connection.channel()
                queue_name = "study_delete_task"
                channel.queue_declare(queue=queue_name, durable=True)
                channel.basic_consume(self.callback, queue=queue_name, no_ack=True)
                channel.start_consuming()
            except:
                log.error(f"delete connection error:{traceback.format_exc()}")
            time.sleep(10)
            log.info(f"delete connection retry, please check connection")

    def callback(self, channel, method, properties, body):
        try:
            self.handle_delete(channel, method, properties, body)
        except:
            log.error(f"delete task error{traceback.format_exc()}")

    def handle_delete(self, channel, method, properties, body):

        log.info(f"Study[delete] > callback, starttime : {datetime.datetime.now()}, body: {body}")
        body = json.loads(body)
        period = body.get("period", "")
        study_list = body.get("studyList", [])
        delete_study_id = body.get("delete_study_id")
        mysql_client = MySQLClient()
        if not mysql_client:
            log.info("mysql connection error")
            for study_instance_uid in study_list:
                notify_message = {
                    "action": "delete",
                    "data": {
                        "StudyInstanceUIDs": [study_instance_uid],
                        "status": False
                    }
                }
                RabbitMQProducer.ws_notify("ws.notify.study_delete", notify_message)
            return
        if study_list:
            study_qs = mysql_client.select(Study, conditions={"study_instance_uid": study_list},
                                           fields=["study_instance_uid", "orthanc_id"])
        else:
            start_date, end_date = period.split("-")
            start_date = datetime.datetime.strptime(start_date, "%Y%m%d")
            end_date = datetime.datetime.strptime(end_date, "%Y%m%d")
            study_qs = mysql_client.select(Study, conditions={
                "gmt_modified__gte": start_date,
                "gmt_modified__lte": end_date
            }, fields=["study_instance_uid", "orthanc_id"])

        if not study_qs:
            log.info("Study[delete] > study not exists")
            # 如果提交的检查列表都已经删除或者不存在，也需要发删除成功消息
            for study_instance_uid in study_list:
                notify_message = {
                    "action": "delete",
                    "data": {
                        "StudyInstanceUIDs": [study_instance_uid],
                        "status": True
                    }
                }
                RabbitMQProducer.ws_notify("ws.notify.study_delete", notify_message)

        study_instance_uids = [(study.get("study_instance_uid"), study.get("orthanc_id")) for study in study_qs]
        study_instance_uids = list(set(study_instance_uids))
        count = len(study_instance_uids)

        # 当出现提交的检查已经在之前的任务被删除后，也需要发删除成功消息
        if study_list and count < len(study_list):
            need_delete_study = [item[0] for item in study_instance_uids]
            not_found_set = set(study_list).difference(need_delete_study)
            for study_instance_uid in not_found_set:
                notify_message = {
                    "action": "delete",
                    "data": {
                        "StudyInstanceUIDs": [study_instance_uid],
                        "status": True
                    }
                }
                RabbitMQProducer.ws_notify("ws.notify.study_delete", notify_message)

        study_slices = DeleteTools.list_slice(study_instance_uids, 5)  # todo
        log.debug("Study[delete] > study_slices")
        DeleteTools().start_delete_study(delete_study_id, study_slices, len(study_qs), study_list, period)
        mysql_client.close()


class TimedDeleteTask(threading.Thread):
    """
    1、查询sql获取当前的设置阈值
    2、获取当前磁盘大小及总额
    3.5 如果当前已经没有数据了，直接contiunue
    3、查询当前是否有正在处理的任务 或者正在删除的数据
    4、删除数据

    """

    def run(self):
        while True:
            try:
                mysql_client = MySQLClient()
                log.info("TimedDeleteTask > auto delete task begining, auto delete task finish")
                if not mysql_client:
                    log.info("TimedDeleteTask > mysql connection error, auto delete task finish")
                    time.sleep(3600)
                    continue
                disk_precent = self.__get_disk_percent()
                # 没有设置磁盘阈值大小
                if not disk_precent:
                    log.info("TimedDeleteTask > not set disk threshold, auto delete task finish")
                    time.sleep(3600)
                    continue
                auto_delete_switch = self.__get_systemconfig_by_code(mysql_client, code="autoDeleteSwitch")
                # 设置的自动删除开关没有打开
                if not auto_delete_switch:
                    log.info("TimedDeleteTask > not set autoDeleteSwitch, auto delete task finish")
                    time.sleep(3600)
                    continue
                disk_threshold = self.__get_systemconfig_by_code(mysql_client, code="diskThreshold")
                # 磁盘阈值没有设置
                if disk_threshold is None:
                    log.info("TimedDeleteTask > not set disk threshold, auto delete task finish")
                    time.sleep(3600)
                    continue
                study_exists = self.__get_study_exists(mysql_client)
                # 当前没有检查数据
                if not study_exists:
                    log.info("TimedDeleteTask > study data not exists, auto delete task finish")
                    time.sleep(3600)
                    continue
                # 磁盘大小小于阈值
                used_percent = int(disk_precent.get("used_percent", 0))
                if used_percent <= disk_threshold:
                    log.info(f"TimedDeleteTask > used_percent: {used_percent} < disk_threshold: {disk_threshold}")
                    time.sleep(3600)
                    continue
                is_working = self.__get_is_working(mysql_client)
                # 当前正在处理数据
                if is_working:
                    log.info(f"TimedDeleteTask > study data is processing, auto delete task finish")
                    time.sleep(3600)
                    continue
                is_deleting = self.__get_is_handle_delete(mysql_client)
                # 当前正在删除数据
                if is_deleting:
                    log.info(f"TimedDeleteTask > study delete is processing, auto delete task finish")
                    time.sleep(3600)
                    continue
                while used_percent > disk_threshold and study_exists:
                    # 上述条件都不满足，开始删除数据
                    try:
                        self.start_delete(mysql_client)
                    except:
                        log.error(f"TimedDeleteTask > auto delete task error: {traceback.format_exc()}")
                    disk_precent = self.__get_disk_percent()
                    percent = int(disk_precent.get("used_percent", 0))
                    study = self.__get_study_exists(mysql_client)
                    if percent <= disk_threshold or not study:
                        break
                    time.sleep(5)
                # try:
                #     self.start_delete(mysql_client)
                # except:
                #     log.error(f"TimedDeleteTask > error: {traceback.format_exc()}")
                #
                # mysql_client.close()
                mysql_client.close()
            except:
                log.error(f"TimedDeleteTask > error: {traceback.format_exc()}")
            log.info("TimedDeleteTask > auto delete task accomplish. ")
            time.sleep(3600)


    def start_delete(self, mysql_client):

        study_uid_list = mysql_client.select(Study, fields=["study_instance_uid", "orthanc_id"],
                                             sorts={"gmt_modified": True})
        delete_num = self.__get_systemconfig_by_code(mysql_client, code="DeleteStudyNumber")
        if not delete_num:
            delete_num = 10
        study_uid_list = study_uid_list[-delete_num:]

        study_orthanc_uids = [(study_dict.get("study_instance_uid"), study_dict.get("orthanc_id")) for study_dict in
                              study_uid_list]

        study_slice = DeleteTools.list_slice(study_orthanc_uids, 10)
        study_instance_uids = [study_dict.get("study_instance_uid") for study_dict in study_uid_list]
        delete_study_id = mysql_client.insert_custom(DeleteStudy,
                                                     data={"count": len(study_uid_list), "deleted_count": 0,
                                                           "study_list": ",".join(study_instance_uids),
                                                           "is_finished": 0, "gmt_create": datetime.datetime.now(),
                                                           "gmt_modified": datetime.datetime.now()})
        log.info(f"TimedDeleteTask > start delete study_instance_uids: {study_instance_uids}")
        DeleteTools().start_delete_study(delete_study_id, study_slice, len(study_uid_list), None, None)

    def __get_disk_percent(self):
        """
        获取磁盘信息
        """
        result_cpu = os.popen("df -h")
        text_cpu = result_cpu.readlines()
        re_str = ".+?([0-9,.]+[K,G,M,T]).*?([0-9,.]+[K,G,M,T]).*?([0-9,.]+[K,G,M,T]).*?([0-9,.]+)% */\n$"
        data = dict()
        for text in text_cpu:
            result = re.findall(re_str, text)
            if result:
                try:
                    (data["total"], data["used"], data["remain"], data["used_percent"]) = result[0]
                    data["used_percent"] = int(data["used_percent"])
                except:
                    log.error("TimedDeleteTask >  get disk error")
                break
        return data

    def __get_systemconfig_by_code(self, mysql_client, code):
        try:
            config = mysql_client.select(SystemConfig, conditions={"code": code}, fields=["value"], )
            if config:
                value = config[0].get("value")
                log.info("TimedDeleteTask > code: {}, value: {}".format(code, value))
                result = literal_eval(value)
                return result
        except Exception:
            log.error("TimedDeleteTask > 查询({})发生异常".format(code))

    def __get_study_exists(self, mysql_client):
        study_exists = mysql_client.select(Study, fields=["id"], )
        return study_exists

    def __get_is_working(self, mysql_client):

        algorithm_task_qs = mysql_client.select(
            AlgorithmTask,
            fields=["start_dated", "finish_percent"],
            sorts={"start_dated": True}
        )
        if algorithm_task_qs:
            start_dated = algorithm_task_qs[0].get("start_dated")
            percent = algorithm_task_qs[0].get("finish_percent")
            total_seconds = (datetime.datetime.now() - start_dated).total_seconds()
            total_mins = total_seconds / 60
            if 0 <= percent < 100 and total_mins < 60:
                return True

    def __get_is_handle_delete(self, mysql_client):

        delete_study = mysql_client.select(DeleteStudy, sorts={"gmt_modified": True})
        if delete_study:
            is_finished = delete_study[0].get("is_finished")
            gmt_create = delete_study[0].get("gmt_create")

            total_seconds = (datetime.datetime.now() - gmt_create).total_seconds()
            total_mins = total_seconds / 60
            if not is_finished and total_mins < 60:
                return True


def main():
    threads = [DeleteStudyTool(), TimedDeleteTask()]
    for i in threads:
        i.start()


if __name__ == '__main__':
    main()
