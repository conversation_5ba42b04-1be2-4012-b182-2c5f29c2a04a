#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/30 15:07
"""

from user import curd
from utils.auth import base64_decode, check_password, create_token
from utils.base import BaseService
from utils.code import Code


class UserService(BaseService):

    def login(self, username, password):
        user = curd.get_by_username(self.db, username)
        if user is None:
            self.log.info("User > user not found, username:{}".format(username))
            return Code.USER_NOT_FOUND, None
        plain_password = base64_decode(password)
        if not plain_password:
            self.log.error("User > failed to decrypt password[{}]".format(password))
            return Code.WRONG_PASSWORD, None
        if not check_password(plain_password, user.password):
            self.log.info("User > wrong password, input:{}, plain:{}, hashed:{}".format(
                password, plain_password, user.password))
            return Code.WRONG_PASSWORD, None
        data = dict(username=username)
        token = create_token(data)
        self.log.info("User > login success, username:{}, token:{}".format(username, token))
        return Code.OK, dict(token=token)


