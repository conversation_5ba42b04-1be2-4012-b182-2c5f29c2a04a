#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : curd
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/30 15:03
"""
from sqlalchemy.orm import Session
from user.model import User


def get(db: Session, user_id: str):
    return db.query(User).filter(User.id == user_id).first()


def get_by_username(db: Session, username: str):
    return db.query(User).filter(User.username == username).first()


def find(db: Session, skip: int = 0, limit: int = 10):
    return db.query(User).offset(skip).limit(limit).all()

