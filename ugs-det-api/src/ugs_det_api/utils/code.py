#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : code
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/29 15:30
"""
from datetime import datetime, date
from enum import Enum

from starlette import status

JSON_ENCODER = {datetime: lambda dt: dt.strftime("%Y-%m-%d %H:%M:%S"),
                date: lambda d: d.strftime("%Y-%m-%d")}


class Code(Enum):
    """接口响应码"""
    OK = (0, "success", status.HTTP_200_OK)
    INTERNAL_SERVER_ERROR = (50000, "internal server error", status.HTTP_500_INTERNAL_SERVER_ERROR)

    # common（40000~40999）
    INCOMPLETE_PARAMETERS = (40000, "incomplete parameters", status.HTTP_400_BAD_REQUEST)
    UNAUTHORIZED = (40001, "unauthorized", status.HTTP_401_UNAUTHORIZED)
    TOKEN_EXPIRED = (40002, "token expired", status.HTTP_401_UNAUTHORIZED)

    # User（41000~41999）
    USER_NOT_FOUND = (41001, "user not found", status.HTTP_400_BAD_REQUEST)
    WRONG_PASSWORD = (41002, "wrong password", status.HTTP_400_BAD_REQUEST)

    # Study（42000~42999）
    STUDY_NOT_FOUND = (42001, "study not found", status.HTTP_400_BAD_REQUEST)
    STUDY_IMAGE_RECEIVING = (42002, "image receiving", status.HTTP_400_BAD_REQUEST)
    STUDY_ALGORITHM_FAILED = (42003, "algorithm failed or calculating", status.HTTP_400_BAD_REQUEST)

    # Task（43000~43999）
    TASK_NOT_FOUND = (43001, "task not found", status.HTTP_400_BAD_REQUEST)
    TASK_INVALID_SUBMISSION = (43002, "invalid submission", status.HTTP_400_BAD_REQUEST)

    @property
    def code(self):
        """
        获取返回码
        """
        return self.value[0]

    @property
    def msg(self):
        """
        获取提示信息
        """
        return self.value[1]

    @property
    def status(self):
        """
        获取状态码
        """
        return self.value[2]




