#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : base
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/30 16:08
"""
from datetime import datetime, date

from pydantic import BaseModel
from sqlalchemy import Column, String, DateTime
from database import Base, SessionLocal
from utils.code import Code
from utils.logger import log

from fastapi.encoders import jsonable_encoder
from starlette.responses import JSONResponse

JSON_ENCODER = {datetime: lambda dt: dt.strftime("%Y-%m-%d %H:%M:%S"),
                date: lambda d: d.strftime("%Y-%m-%d")}


class BaseEntity(Base):
    __abstract__ = True

    id = Column(String(36), primary_key=True, index=True)
    gmtCreate = Column("gmt_create", DateTime, default=datetime.utcnow())
    gmtModified = Column("gmt_modified", DateTime, default=datetime.utcnow())

    def to_dict(self):
        return {c.name: getattr(self, c.name, None) for c in self.__table__.columns}


class BaseService:
    def __init__(self):
        self.db = SessionLocal()
        self.log = log

    def __del__(self):
        self.db.close()


class Result(BaseModel):
    code: int
    message: str
    data: dict


class PageData(BaseModel):
    content: list
    pageNo: int
    pageSize: int
    totalElements: int
    totalPages: int


class ResponseHelper:
    @staticmethod
    def get_result(code: Code, data=None):
        result = Result(code=code.code, message=code.msg,
                        data=(jsonable_encoder(data, custom_encoder=JSON_ENCODER) if data else dict()))
        return result.dict()

    @staticmethod
    def of(code: Code, data=None):
        """
        响应组装
        :param code: 响应码
        :param data: 返回数据
        :return:
        """
        return JSONResponse(ResponseHelper.get_result(code, data), status_code=code.status)


class TaskStatus:
    failed = -1  # 下载失败
    created = 0  # 已创建
    packing = 1  # 打包中
    packed = 2  # 待下载
