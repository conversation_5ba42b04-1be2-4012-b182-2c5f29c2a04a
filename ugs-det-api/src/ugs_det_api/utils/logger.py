#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : logger
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/29 15:12
"""
import logging
import os
from concurrent_log_handler import ConcurrentRotatingFileHandler

from config import Setting


class MyLogger:
    """单例日志类"""
    _logger = None

    def __new__(cls, *args, **kwargs):
        if cls._logger is None:
            cls._logger = super().__new__(cls, *args, **kwargs)
            cls._logger = logging.getLogger(Setting.service_name)
            cls._logger.setLevel(logging.INFO if Setting.is_production() else logging.DEBUG)
            formatter = logging.Formatter(
                "%(asctime)s[%(name)s][%(levelname)s][%(process)d-%(thread)d][%(filename)s-%(lineno)s]: %(message)s",
                "%Y-%m-%d %H:%M:%S")
            # console
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            cls._logger.addHandler(console_handler)
            # rolling file
            log_dir = "/data/ctpdata/log/{}".format(Setting.service_name)
            if not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            log_path = os.path.join(log_dir, "{}.log".format(Setting.service_name))
            rotating_file_handler = ConcurrentRotatingFileHandler(
                filename=log_path, maxBytes=1024 * 1024 * 10, backupCount=3, encoding="utf-8"
            )
            rotating_file_handler.setFormatter(formatter)
            cls._logger.addHandler(rotating_file_handler)
        return cls._logger


log = MyLogger()
