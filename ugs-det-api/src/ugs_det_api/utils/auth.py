#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : auth
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/30 15:13
"""
import base64
import binascii
import traceback
from datetime import datetime, timed<PERSON><PERSON>

from fastapi import Header
from passlib.context import Crypt<PERSON>ontext
from jose import jwt
from starlette.requests import Request

from config import Setting, Const
from utils.code import Code
from utils.logger import log


class AuthException(Exception):
    def __init__(self, code: Code):
        self.code = code


class _Auth:
    PWD_CONTEXT = CryptContext(schemes=["bcrypt"], deprecated="auto")
    SECRET_KEY = "d1b353d265eac146f4587f149e8b58d689a04ef692d4a28b8626deefc57dd691"
    ALGORITHM = "HS256"
    TOKEN_NAME = "token"
    TOKEN_TYPE = "Bearer "
    TOKEN_EXPIRE_MINUTES = 360


def base64_decode(encode_str):
    try:
        return base64.b64decode(encode_str.encode(Const.charset)).decode(Const.charset)
    except binascii.Error as e:
        log.error("login > failed to decrypt [{}], error:{}".format(encode_str, e))
        return None


def check_password(password, hashed_password):
    """
    校验密码是否与哈希密码匹配
    :param password: 原密码
    :param hashed_password: 哈希后的密码
    :return:
    """
    return _Auth.PWD_CONTEXT.verify(password, hashed_password)


def create_token(data: dict, expire_time: int = _Auth.TOKEN_EXPIRE_MINUTES):
    payload = data.copy()
    # 添加失效时间
    expire = datetime.utcnow() + timedelta(minutes=expire_time)
    payload.update({"expire": expire.timestamp()})
    credentials = jwt.encode(payload, _Auth.SECRET_KEY, algorithm=_Auth.ALGORITHM)
    token = "{}{}".format(_Auth.TOKEN_TYPE, credentials)
    return token


def verify_token(request: Request):
    try:
        token = request.headers[_Auth.TOKEN_NAME]
        min_length = len(_Auth.TOKEN_TYPE)
        if not token or len(token) <= min_length:
            return Code.UNAUTHORIZED
        credentials = token[min_length:]
        payload = jwt.decode(credentials, _Auth.SECRET_KEY, algorithms=_Auth.ALGORITHM)
        log.debug(payload)
        username = payload.get("username")
        expire = payload.get("expire")
        expire_time = datetime.fromtimestamp(expire)
        if expire_time < datetime.utcnow():
            log.info("API > token:{}, username:{}, expire:{}, token expired".format(token, username, expire_time))
            return Code.TOKEN_EXPIRED
        return None
    except:
        log.error("API > headers:{}, error:{}".format(request.headers, traceback.format_exc()))
        return Code.UNAUTHORIZED
