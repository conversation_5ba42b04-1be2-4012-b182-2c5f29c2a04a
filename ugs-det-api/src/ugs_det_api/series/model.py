#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : model.py
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/9/1 13:45
"""
from sqlalchemy import Column, String, Boolean

from utils.base import BaseEntity


class Series(BaseEntity):
    __tablename__ = "t_series"

    # hospitalUid = Column("hospital_uid", String(64))
    seriesInstanceUID = Column("series_instance_uid", String(128))
    seriesNumber = Column("series_number", String(16))
    seriesDescription = Column("series_description", String(255))
    modality = Column(String(64))
    type = Column(String(32))
    downloaded = Column(Boolean)
    studyId = Column("study_id", String(255))
