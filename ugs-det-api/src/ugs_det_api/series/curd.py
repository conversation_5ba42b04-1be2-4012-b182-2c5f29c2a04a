#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : curd.py
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/9/1 13:50
"""
from sqlalchemy.orm import Session
from series.model import Series


def get(db: Session, series_id: str):
    return db.query(Series).filter(Series.id == series_id).first()


def find_by_study_and_type(db: Session, study_id: str, type: str):
    return db.query(Series).filter(Series.studyId == study_id, Series.type == type).all()


def find_by_study_and_types(db: Session, study_id: str, types: list):
    return db.query(Series).filter(Series.studyId == study_id, Series.type.in_(types)).all()

def find_by_study_type_and_downlaod(db: Session, study_id: str, types: list, downlaod: bool):
    return db.query(Series).filter(Series.studyId == study_id,
                                   Series.type.in_(types), Series.downloaded == downlaod).first()
