#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : middleware
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/29 15:29
"""
import traceback

from starlette.middleware.cors import CORSMiddleware
from starlette.requests import Request

from utils.auth import AuthException
from utils.base import ResponseHelper
from utils.logger import log
from utils.code import Code


origins = ["*"]


async def catch_exceptions_middleware(request: Request, call_next):
    """全局异常捕获"""
    try:
        return await call_next(request)
    except AuthException as e:
        return ResponseHelper.of(e.code)
    except Exception as e:
        log.error(traceback.format_exc())
        return ResponseHelper.of(Code.INTERNAL_SERVER_ERROR)


def register_middleware(app):
    """注册中间件"""
    app.add_middleware(CORSMiddleware, allow_origins=origins, allow_credentials=True, allow_methods=["*"],
                       allow_headers=["*"])
    app.middleware("http")(catch_exceptions_middleware)


