#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : worker
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/9/1 11:30
"""
import os
import shutil
import threading
import time
import traceback
import zipfile

import rsa
import rsa.randnum
from Crypto.Cipher import AES
from Crypto.Util import Counter
from Crypto.Protocol.KDF import PBKDF2

from config import Const
from task import curd as task_curd
from study import curd as study_curd
from series import curd as series_curd
from utils.base import TaskStatus
from utils.logger import log
from database import SessionLocal


class SubType:
    aspects = ["3D_ASPECTS_SUMMARY_USC", "3D_PC-ASPECTS_SUMMARY_USC", "3D_ASPECTS_MASK_USC", "HEMORRHAGE_SUMMARY_USC",
               "HEMORRHAGE_MASK_USC"]
    ctp = ["USC_UGuard_CTP_AIF_VOF_CSF_Location", "USC_UGuard_CTP_MIP", "USC_UGuard_CTP_Parameter_Colored_Map_CBF",
           "USC_UGuard_CTP_Parameter_Colored_Map_CBV", "USC_UGuard_CTP_Parameter_Colored_Map_MTT",
           "USC_UGuard_CTP_Parameter_Colored_Map_PS", "USC_UGuard_CTP_Parameter_Colored_Map_Tmax",
           "USC_UGuard_CTP_Parameter_Colored_Map_TTP", "USC_UGuard_CTP_Summary", "USC_UGuard_CTP_Total_Summary"]
    cta_vr = {"VR_AP": "USC-UGuard_CTA_Volume_Rendering_AP", "VR_LR": "USC-UGuard_CTA_Volume_Rendering_LR",
              "MIP_AP": "USC-UGuard_CTA_Volume_MIP_AP", "MIP_LR": "USC-UGuard_CTA_Volume_MIP_LR",
              "VR_Head_AP": "USC-UGuard_CTA_Volume_Rendering_Head_AP",
              "VR_Head_LR": "USC-UGuard_CTA_Volume_Rendering_Head_LR",
              "MIP_Head_AP": "USC-UGuard_CTA_Volume_MIP_Head_AP", "MIP_Head_LR": "USC-UGuard_CTA_Volume_MIP_Head_LR"}
    cta_cs = "COLLATERAL_CIRCULATION_USC"

    @classmethod
    def get_cta_vr_path(cls, root_dir, file_name):
        dot_split_list = file_name.split(".")
        split_list = dot_split_list[0].split("_")
        file_type = "_".join(split_list[:-1])
        angle_num = int(split_list[-1]) if str(split_list[-1]).isdigit() else 0
        newname = "{}.{}".format(int(angle_num/15)+1, dot_split_list[-1])
        return os.path.join(root_dir, cls.cta_vr.get(file_type), newname)


class Encryptor:
    def __init__(self, pem_path):
        with open(pem_path) as f:
            self.public_key = rsa.PublicKey.load_pkcs1(f.read().encode(Const.charset))
        self.salt = b'ugs-testugs-test'

    def encrypt(self, infile_path, outfile_path):
        with open(infile_path, "rb") as infile, open(outfile_path, "wb") as outfile:
            # 生成随机且固定长度的key
            key = rsa.randnum.read_random_bits(256)
            # 生成加过盐值的aes_key长度与key长度一直
            aes_key = PBKDF2(key, self.salt, dkLen=32)
            # AES.MODE_CTR加密模块
            aes_engine = AES.new(aes_key, AES.MODE_CTR, counter=Counter.new(128))
            # 对源文件数据做AES加密
            out_data = aes_engine.encrypt(infile.read())
            # 使用公钥加密aes_key，生成加密后的encrypted_aes_key
            encrypted_aes_key = rsa.encrypt(aes_key, self.public_key)
            # 将encrypted_aes_key写入加密文件
            outfile.write(encrypted_aes_key)
            # 将加密后的文件数据写入加密文件
            outfile.write(out_data)


class PackHandler:

    def __init__(self, resource):
        self.patient_name = resource.get("patientName")
        self.study_instance_uid = resource.get("studyInstanceUID")
        self.toshiba = resource.get("toshiba")
        self.original = resource.get("original")
        self.aspects = resource.get(Const.aspects, [])
        self.cta = resource.get(Const.cta, [])
        self.ctp = resource.get(Const.ctp, [])
        # 创建加密工具
        self.encryptor = Encryptor(Const.pemPath)
        # 加密输出目录
        encrypt_dir = os.path.join(Const.encryptionDir, self.study_instance_uid)
        if os.path.exists(encrypt_dir):
            shutil.rmtree(encrypt_dir)
        os.makedirs(encrypt_dir)
        self.temp_dir = encrypt_dir

    def start(self):
        start_time = time.time()
        #
        if self.aspects:
            log.info("Pack > handle aspects:{}".format(self.aspects))
            for series_instance_uid in self.aspects:
                self.handle_aspects(series_instance_uid)
        if self.cta:
            log.info("Pack > handle cta:{}".format(self.cta))
            for series_instance_uid in self.cta:
                self.handle_cta(series_instance_uid)
        if self.ctp:
            if not self.toshiba:
                log.info("Pack > handle ctp:{}".format(self.ctp))
                for series_instance_uid in self.ctp:
                    self.handle_ctp(series_instance_uid)
            else:
                log.info("Pack > handle toshiba ctp:{}".format(self.study_instance_uid))
                self.handle_toshiba_ctp()
        encrypt_end_time = time.time()
        zip_path = self.create_zip()
        end_time = time.time()
        log.info(F"<Study:{self.study_instance_uid}>".center(100, "*"))
        log.info("* Encrypt : {:.2f}s".format(encrypt_end_time - start_time))
        log.info("* Compress: {:.2f}s".format(end_time - encrypt_end_time))
        log.info("* Total   : {:.2f}s".format(end_time - start_time))
        log.info(F"".center(100, "*"))
        return zip_path

    def handle_aspects(self, series_instance_uid):
        aspects_dir = os.path.join(self.temp_dir, series_instance_uid)
        dcm_dir = os.path.join(Const.dcmDir, series_instance_uid)
        if os.path.exists(dcm_dir) and self.original:
            output_dir = os.path.join(aspects_dir, Const.dicom)
            os.makedirs(output_dir, exist_ok=True)
            self.encrypt_batch(dcm_dir, output_dir)
        result_dir = os.path.join(Const.staticDir, self.study_instance_uid, series_instance_uid)
        if os.path.exists(result_dir):
            for subtype in SubType.aspects:
                summary_dir = os.path.join(result_dir, subtype, "pic")
                if os.path.exists(summary_dir):
                    output_dir = os.path.join(aspects_dir, subtype)
                    os.makedirs(output_dir, exist_ok=True)
                    self.encrypt_batch(summary_dir, output_dir)
        log.info("Pack > aspects result encrypted, series:{}".format(series_instance_uid))

    def handle_cta(self, series_instance_uid):
        cta_dir = os.path.join(self.temp_dir, series_instance_uid)
        dcm_dir = os.path.join(Const.dcmDir, series_instance_uid)
        if os.path.exists(dcm_dir) and self.original:
            output_dir = os.path.join(cta_dir, Const.dicom)
            os.makedirs(output_dir, exist_ok=True)
            self.encrypt_batch(dcm_dir, output_dir)
        vr_dir = os.path.join(Const.vrDir, self.study_instance_uid, series_instance_uid)
        if os.path.exists(vr_dir):
            file_list = os.listdir(vr_dir)
            for file_name in file_list:
                file_path = os.path.join(vr_dir, file_name)
                output_path = SubType.get_cta_vr_path(cta_dir, file_name)
                if os.path.exists(output_path):
                    continue
                output_dir = os.path.dirname(os.path.abspath(output_path))
                os.makedirs(output_dir, exist_ok=True)
                self.encryptor.encrypt(file_path, output_path)
            log.info("Pack > input:{}, encrypt {} files".format(vr_dir, len(file_list)))
        collateral_dir = os.path.join(Const.staticDir, self.study_instance_uid,
                                      series_instance_uid, SubType.cta_cs, "pic")
        if os.path.exists(collateral_dir):
            output_dir = os.path.join(cta_dir, SubType.cta_cs)
            os.makedirs(output_dir, exist_ok=True)
            self.encrypt_batch(collateral_dir, output_dir)
        # 1.7.0 单图
        # else:
        #     collateral_path = os.path.join(Const.ctaDir, self.study_instance_uid,
        #                                    series_instance_uid, "cs_mip.jpg")
        #     if os.path.exists(collateral_path):
        #         output_dir = os.path.join(cta_dir, SubType.cta_cs)
        #         os.makedirs(output_dir, exist_ok=True)
        #         output_path = os.path.join(output_dir, "1.jpg")
        #         self.encryptor.encrypt(collateral_path, output_path)
        #         log.info("Encryptor > input:{}, output:{}, encrypt {} files".format(
        #             collateral_path, output_path, 1))
        log.info("Pack > cta result encrypted, series:{}".format(series_instance_uid))

    def handle_ctp(self, series_instance_uid):
        ctp_dir = os.path.join(self.temp_dir, series_instance_uid)
        dcm_dir = os.path.join(Const.dcmDir, series_instance_uid)
        if os.path.exists(dcm_dir) and self.original:
            output_dir = os.path.join(ctp_dir, Const.dicom)
            os.makedirs(output_dir, exist_ok=True)
            self.encrypt_batch(dcm_dir, output_dir)
        result_dir = os.path.join(Const.ctpDir, self.study_instance_uid, series_instance_uid, Const.png)
        if os.path.exists(result_dir):
            for subtype in SubType.ctp:
                sub_dir = os.path.join(result_dir, subtype)
                if os.path.exists(sub_dir):
                    output_dir = os.path.join(ctp_dir, subtype)
                    os.makedirs(output_dir, exist_ok=True)
                    self.encrypt_batch(sub_dir, output_dir)
        log.info("Pack > ctp result encrypted, series:{}".format(series_instance_uid))

    def handle_toshiba_ctp(self):
        dcm_dir = os.path.join(Const.dcmDir, self.study_instance_uid)
        if os.path.exists(dcm_dir) and self.original:
            output_dir = os.path.join(self.temp_dir, Const.dicom)
            os.makedirs(output_dir, exist_ok=True)
            self.encrypt_batch(dcm_dir, output_dir)
        result_dir = os.path.join(Const.ctpDir, self.study_instance_uid, Const.png)
        if os.path.exists(result_dir):
            for subtype in SubType.ctp:
                sub_dir = os.path.join(result_dir, subtype)
                if os.path.exists(sub_dir):
                    output_dir = os.path.join(self.temp_dir, subtype)
                    os.makedirs(output_dir, exist_ok=True)
                    self.encrypt_batch(sub_dir, output_dir)
        log.info("Pack > toshiba ctp result encrypted, study:{}".format(self.study_instance_uid))

    def encrypt_batch(self, input_dir, output_dir):
        file_list = os.listdir(input_dir)
        for filename in file_list:
            file_path = os.path.join(input_dir, filename)
            output_path = os.path.join(output_dir, filename.replace("img_", ""))
            self.encryptor.encrypt(file_path, output_path)
        log.info("Encryptor > input:{}, output:{}, encrypt {} files".format(input_dir, output_dir, len(file_list)))

    def create_zip(self):
        """
        创建压缩文件
        :return:
        """
        abs_source_path = os.path.abspath(self.temp_dir)
        if not os.path.exists(abs_source_path):
            log.info("file not found: {}".format(abs_source_path))
            return
        zip_name = "{}_{}.zip".format(self.patient_name, self.study_instance_uid)
        zip_path = os.path.join(Const.detDir, zip_name)
        if os.path.exists(zip_path):
            os.remove(zip_path)
            log.info("remove {}".format(zip_path))
        zip_dir = os.path.split(os.path.abspath(zip_path))[0]
        if not os.path.exists(zip_dir):
            os.makedirs(zip_dir)
            log.info("create dir: {}".format(zip_dir))
        # 压缩文件
        if os.path.isfile(abs_source_path):
            f = zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED)
            f.write(self.temp_dir)
            f.close()
            log.debug("compress {} in {}".format(f, zip_path))
            return zip_path
        # 遍历目录
        file_list = []
        for root_dir, sub_dirs, files in os.walk(self.temp_dir):
            for fileItem in files:
                file_list.append(os.path.join(root_dir, fileItem))
            for dirItem in sub_dirs:
                file_list.append(os.path.join(root_dir, dirItem))
        if not file_list:
            return None
        f = zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED)
        # 读取列表压缩目录和文件
        for i in file_list:
            # 减少压缩文件的一层目录，即压缩文件不包括source_path目录
            f.write(i, i.replace(self.temp_dir, ""))
            log.debug("compress {} in {}".format(i, zip_path))
        f.close()
        log.info("Pack > compress {} success, zip:{}".format(self.temp_dir, zip_path))
        return zip_path


class StudyWorker(threading.Thread):
    def __init__(self, task_id, study_id, original):
        super().__init__()
        self.task_id = task_id
        self.study_id = study_id
        self.original = original
        self.db = SessionLocal()
        log.info("Worker > task:{}, study:{}, original:{}".format(task_id, study_id, original))

    def run(self):
        try:
            # 获取检查和序列信息
            resource = self.get_resource()
            # 更新任务状态：打包中
            task_curd.update_status(self.db, self.task_id, TaskStatus.packing)
            # 打包
            handler = PackHandler(resource)
            zip_path = handler.start()
            # 更新任务状态：失败
            if not zip_path or not os.path.exists(zip_path):
                log.info("Worker > failed to pack, task:{}, study:{}".format(self.task_id, self.study_id))
                task_curd.update_status(self.db, self.task_id, TaskStatus.failed)
                return
            # 更新任务状态：成功
            url = zip_path.replace(Const.dataDir, "")
            task_curd.update(self.db, self.task_id, TaskStatus.packed, url)
            log.info("Worker > success, task:{}, study:{}, zip:{}".format(
                self.task_id, self.study_id, zip_path))
        except:
            task_curd.update_status(self.db, self.task_id, TaskStatus.failed)
            log.info("Worker > task:{}, study:{}, error:{}".format(
                self.task_id, self.study_id, traceback.format_exc()))
        finally:
            self.db.close()

    def get_resource(self):
        study = study_curd.get(self.db, self.study_id)
        resource = dict(studyInstanceUID=study.studyInstanceUID, toshiba=study.toshiba, original=self.original)
        resource["patientName"] = study.patientName.replace(" ", "") if study.patientName else "unknown"
        series_list = series_curd.find_by_study_and_types(self.db, self.study_id, Const.algorithms)
        for series in series_list:
            if series.type not in resource:
                resource[series.type] = []
            resource[series.type].append(series.seriesInstanceUID)
        log.info("Worker > resource:{}".format(resource))
        return resource
