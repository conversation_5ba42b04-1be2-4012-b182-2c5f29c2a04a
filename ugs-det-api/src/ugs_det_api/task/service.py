#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/31 16:01
"""
import uuid

from config import Setting, Const
from series import curd as series_curd
from task import curd
from task.worker import <PERSON><PERSON>or<PERSON>
from task.model import Task
from utils.base import BaseService, TaskStatus
from utils.code import Code


class TaskService(BaseService):

    def get_task(self, task_id):
        task = curd.get(self.db, task_id)
        if not task:
            self.log.info("Task > invalid task id:[{}]".format(task_id))
            return Code.TASK_NOT_FOUND, None
        return Code.OK, task

    def exist(self, study_id):
        return True if curd.get_by_study(self.db, study_id) else False

    def create_task(self, study_id, original):
        # 检测是否有任务正在下载
        count = curd.count_by_status(self.db, [TaskStatus.created, TaskStatus.packing])
        if count > 0:
            self.log.info("Task > invalid submission, study_id:{}".format(study_id))
            return Code.TASK_INVALID_SUBMISSION, None
        series = series_curd.find_by_study_type_and_downlaod(self.db, study_id, Const.algorithms, False)
        if series:
            self.log.info("Task > image receiving, study_id:{}".format(study_id))
            return Code.STUDY_IMAGE_RECEIVING, None
        task = curd.get_by_study(self.db, study_id)
        self.log.info("Task > info:{}".format(task.__dict__ if task else None))
        if task:
            # 重新下载
            curd.update(self.db, task.id, TaskStatus.created, "")
            task.status = TaskStatus.created
            task.url = ""
        else:
            # 新增下载任务
            task = Task(id=str(uuid.uuid1()), studyId=study_id)
            curd.create(self.db, task)
            self.log.info("Task > create:{}".format(task))
        StudyWorker(task.id, study_id, original).start()
        return Code.OK, task
