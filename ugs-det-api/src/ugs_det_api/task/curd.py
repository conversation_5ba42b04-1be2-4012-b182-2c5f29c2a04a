#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : curd
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/31 16:05
"""
from sqlalchemy import func
from sqlalchemy.orm import Session
from task.model import Task


def get(db: Session, task_id: str):
    return db.query(Task).filter(Task.id == task_id).first()


def get_by_study(db: Session, study_id: str):
    return db.query(Task).filter(Task.studyId == study_id).first()


def count_by_status(db: Session, status: list):
    return db.query(func.count(Task.id)).filter(Task.status.in_(status)).scalar()


def create(db: Session, task: Task):
    db.add(task)
    db.commit()
    db.refresh(task)
    return task


def update(db: Session, task_id: str, status: int, url: str):
    db.query(Task).filter(Task.id == task_id).update({Task.status: status, Task.url: url})
    db.commit()


def update_status(db: Session, task_id: str, status: int):
    db.query(Task).filter(Task.id == task_id).update({Task.status: status})
    db.commit()
