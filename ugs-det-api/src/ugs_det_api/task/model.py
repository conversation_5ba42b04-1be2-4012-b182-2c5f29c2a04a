#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : model
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/31 16:03
"""
from sqlalchemy import Column, String, Integer

from utils.base import BaseEntity


class Task(BaseEntity):
    __tablename__ = "det_task"

    status = Column(Integer, default=0)
    url = Column(String(255), default="")
    studyId = Column("study_id", String(36))
