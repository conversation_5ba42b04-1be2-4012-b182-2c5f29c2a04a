#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : router
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/31 15:56
"""
from fastapi import APIRouter
from starlette.requests import Request

from task.service import TaskService
from utils.auth import verify_token
from utils.base import ResponseHelper
from utils.logger import log

router = APIRouter(prefix="/tasks", tags=["任务管理"])


@router.get("/{task_id}", name="任务查询")
async def get(request: Request, task_id: str):
    log.info("Task(get) > taskId:{},".format(task_id))
    code = verify_token(request)
    if code:
        return ResponseHelper.of(code)
    service = TaskService()
    code, data = service.get_task(task_id)
    return ResponseHelper.of(code, data)
