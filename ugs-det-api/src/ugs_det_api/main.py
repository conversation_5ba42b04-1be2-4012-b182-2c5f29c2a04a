#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : main
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/29 15:11
"""
import logging
import os
import sys

import uvicorn
from fastapi import FastAPI

sys.path.append(os.path.dirname(__file__))

from config import Setting
from utils.logger import log
from router import register_router
from middleware import register_middleware


def create_app():
    _app = FastAPI(title="det-api", description="date export tool api", version="1.0")
    # 生产环境
    if Setting.is_production():
        _app.docs_url = None
        _app.redoc_url = None
        _app.debug = False

    # 初始化路由
    register_router(_app)
    # 初始化中间件
    register_middleware(_app)
    return _app


app = create_app()


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    log.info("DetAPI startup")
    logger = logging.getLogger("uvicorn.access")
    formatter = logging.Formatter(
        "%(asctime)s[%(name)s][%(levelname)s][%(process)d-%(thread)d][%(filename)s-%(lineno)s]: %(message)s",
        "%Y-%m-%d %H:%M:%S")
    logger.handlers[0].setFormatter(formatter)
    logger.handlers[0].setLevel(logging.INFO if Setting.is_production() else logging.DEBUG)


@app.on_event("shutdown")
async def shutdown_event():
    """停止事件"""
    log.info("DetAPI shutdown")


def run():
    uvicorn.run(app="main:app", host="0.0.0.0", workers=Setting.serviceWorkers, port=Setting.servicePort,
                reload=(False if Setting.is_production() else True))


if __name__ == "__main__":
    run()
