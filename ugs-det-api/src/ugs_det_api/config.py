#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : config
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/29 15:13
"""
import os

_env = os.environ


class Setting:
    service_name = "ugs-det-api"
    environment = _env.get("ENVIRONMENT", "dev")

    # MySQL
    dbHost = _env.get("DB_HOST", "************")
    dbPort = int(_env.get("DB_PORT", "3310"))
    dbName = _env.get("DB_NAME", "cloud")
    dbUser = _env.get("DB_USER", "root")
    dbPassword = _env.get("DB_PASSWORD", "UnionStrong@2020")

    # Service
    servicePort = int(_env.get("SERVICE_PORT", "8099"))
    serviceWorkers = int(_env.get("SERVICE_WORKERS", "5"))

    @classmethod
    def is_development(cls):
        return cls.environment != "prod"

    @classmethod
    def is_production(cls):
        return cls.environment == "prod"


class Const:
    charset = "utf-8"
    dicom = "DICOM"
    png = "png"
    vrDir = "/code/vr"
    dataDir = "/data/ctpdata"
    dcmDir = F"{dataDir}/dcm"
    staticDir = F"{dataDir}/static"
    detDir = F"{staticDir}/det"
    ctaDir = F"{dataDir}/cta"
    ctpDir = F"{dataDir}/ctp"
    encryptionDir = F"{dataDir}/encryption"
    pemPath = "/ugs_det_api/public.pem"

    # Algorithms
    ctp = "ctp"
    cta = "cta"
    aspects = "aspects"
    algorithms = [ctp, cta, aspects]

    toshibaCtp = "toshibaCtp"
