#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : router.py
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/29 15:51
"""
from fastapi import APIRouter

from user.service import UserService
from utils.logger import log
from utils.base import ResponseHelper
from pub.schema import LoginRequest

router = APIRouter(tags=["免签接口"])


@router.post("/login", name="用户登录")
async def login(body: LoginRequest):
    log.info("Login > body:{}".format(body))
    service = UserService()
    code, data = service.login(body.username, body.password)
    return ResponseHelper.of(code, data)
