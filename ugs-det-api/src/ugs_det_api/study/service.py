#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/31 13:13
"""
from sqlalchemy import text

from config import Const
from database import engine
from study import curd
from series import curd as series_curd
from task.service import TaskService
from utils.base import BaseService, PageData
from utils.code import Code


class StudyService(BaseService):

    def list_study(self, patient_id, patient_name, page, size):
        if page <= 0 or size <= 0:
            self.log.info("Study > invalid page parameters, page:{}, size:{}".format(page, size))
            return Code.INCOMPLETE_PARAMETERS, None
        element_count = curd.count(self.db, patient_id, patient_name)
        page_count = int((element_count - 1) / size) + 1
        self.log.info("Study > count:{}, pages:{}".format(element_count, page_count))
        if page > page_count:
            self.log.info("invalid page:{}, pages:{}".format(page, page_count))
            return Code.INCOMPLETE_PARAMETERS, None
        if element_count == 0:
            data = PageData(content=[], pageNo=page, pageSize=size, totalElements=element_count, totalPages=page_count)
            return Code.OK, data.dict()
        start_index = (page - 1) * size
        elements = curd.page(self.db, patient_id, patient_name, start_index, size)
        for element in elements:
            if not element.toshiba:
                element.__setattr__(Const.toshibaCtp, False)
                continue
            series_list = series_curd.find_by_study_and_type(self.db, element.id, Const.ctp)
            element.__setattr__(Const.toshibaCtp, len(series_list) > 0)
        data = PageData(content=elements, pageNo=page, pageSize=size, totalElements=element_count,
                        totalPages=page_count)
        return Code.OK, data.dict()

    def download_study(self, study_id, original):
        study = curd.get(self.db, study_id)
        if not study:
            self.log.info("Study[{}] > study not found".format(study_id))
            return Code.STUDY_NOT_FOUND, None
        with engine.connect() as con:
            sql = ("select series_uid, algorithm_type from algorithm.algorithm_task "
                   "where series_uid in (select s.series_instance_uid from cloud.t_series s "
                   "inner join cloud.t_study t on t.id = s.study_id "
                   F"where t.study_instance_uid='{study.studyInstanceUID}') and finish_percent = 100")
            results = con.execute(text(sql))
            if results.rowcount == 0:
                self.log.info("Study[{}] > algorithm failed or calculating".format(study_id))
                return Code.STUDY_ALGORITHM_FAILED, None
        task_service = TaskService()
        return task_service.create_task(study_id, original)
