#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : router
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/31 9:53
"""
from fastapi import APIRouter
from starlette.requests import Request

from study.service import StudyService
from utils.auth import verify_token
from utils.base import ResponseHelper
from utils.logger import log

router = APIRouter(prefix="/studies", tags=["检查管理"])


@router.get("/", name="列表查询")
async def find(request: Request, patientId: str = None, patientName: str = None, page: int = 0, size: int = 10):
    log.info("Study(list) > patientId:{}, patientName:{}, page:{}, size:{}".format(
        patientId, patientName, page, size))
    code = verify_token(request)
    if code:
        return ResponseHelper.of(code)
    service = StudyService()
    code, data = service.list_study(patientId, patientName, page, size)
    return ResponseHelper.of(code, data)


@router.get("/{study_id}/download", name="列表查询")
async def download(request: Request, study_id: str, original: int = 0):
    log.info("Study(download) > studyId:{}, original:{}".format(study_id, original))
    code = verify_token(request)
    if code:
        return ResponseHelper.of(code)
    service = StudyService()
    code, data = service.download_study(study_id, original)
    return ResponseHelper.of(code, data)

