#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : curd
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/31 11:34
"""
from sqlalchemy import func
from sqlalchemy.orm import Session
from study.model import Study


def get(db: Session, study_id: str):
    return db.query(Study).filter(Study.id == study_id).first()


def count(db: Session, patient_id: str, patient_name: str):
    query = db.query(func.count(Study.id))
    if patient_id:
        query = query.filter(Study.patientID.like("%{}%".format(patient_id)))
    if patient_name:
        query = query.filter(Study.patientName.like("%{}%".format(patient_name)))
    return query.scalar()


def page(db: Session, patient_id: str, patient_name: str, skip: int = 0, limit: int = 10):
    query = db.query(Study)
    if patient_id:
        query = query.filter(Study.patientID.like("%{}%".format(patient_id)))
    if patient_name:
        query = query.filter(Study.patientName.like("%{}%".format(patient_name)))
    return query.order_by(Study.studyDatetime.desc()).offset(skip).limit(limit).all()
