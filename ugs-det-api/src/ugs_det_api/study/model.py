#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : model
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/31 9:55
"""
from sqlalchemy import Column, String, DateTime, Boolean

from utils.base import BaseEntity


class Study(BaseEntity):
    __tablename__ = "t_study"

    # hospitalUid = Column("hospital_uid", String(64))
    studyInstanceUID = Column("study_instance_uid", String(128))
    studyID = Column("study_id", String(32))
    studyDatetime = Column("study_datetime", DateTime)
    studyDescription = Column("study_description", String(255))
    patientID = Column("patient_id", String(64))
    patientName = Column("patient_name", String(64))
    patientSex = Column("patient_sex", String(32))
    patientAge = Column("patient_age", String(32))
    patientWeight = Column("patient_weight", String(32))
    patientBirthdate = Column("patient_birthdate", String(64))
    # apiVersion = Column("api_version", String(32))
    toshiba = Column("toshiba", Boolean)
    orthancId = Column("orthanc_id", String(255))
    # isConfirmed = Column("is_confirmed", Boolean)
