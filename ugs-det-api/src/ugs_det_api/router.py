#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : router
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/29 15:27
"""
from fastapi import APIRouter
from pub.router import router as pub
from study.router import router as study
from task.router import router as task


def register_router(app):
    """注册路由"""
    apps_router = APIRouter()
    apps_router.include_router(pub)
    apps_router.include_router(study)
    apps_router.include_router(task)
    app.include_router(apps_router, prefix="/api/v1")
