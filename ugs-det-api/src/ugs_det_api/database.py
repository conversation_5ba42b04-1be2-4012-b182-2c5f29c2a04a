#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : database
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/29 16:43
"""
from urllib.parse import quote_plus

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from config import Setting

# 义数据库 URL地址
# SQLALCHEMY_DATABASE_URL = "sqlite:///./sql_app.db"
# SQLALCHEMY_DATABASE_URL = "********************************************"
SQLALCHEMY_DATABASE_URL = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8mb4".format(
    Setting.dbUser, quote_plus(Setting.dbPassword), Setting.dbHost, Setting.dbPort, Setting.dbName)

# 创建 SQLAlchemy 引擎
engine = create_engine(SQLALCHEMY_DATABASE_URL, pool_size=Setting.serviceWorkers, echo=Setting.is_development(),
                       pool_recycle=3600, pool_pre_ping=True)

# 创建一个 SessionLocal数据库会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建一个Base类
Base = declarative_base()


# def get_db():
#     """Dependency"""
#     db = SessionLocal()
#     try:
#         yield db
#     finally:
#         db.close()
