version: "3"
services:
  ugs-det-api:
    container_name: "ugs-det-api"
    image: harbor.unionstrongtech.com/ugs/det:1.0.0
    ports:
      - ${SERVICE_PORT}:${SERVICE_PORT}
    volumes:
      - ./dist:/ugs_det_api/dist
      - ./public.pem:/ugs_det_api/public.pem
      - /data/ctpdata:/data/ctpdata
      - /home/<USER>/upixel-station-backend/server/static/jpg:/code/vr
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    working_dir: /ugs_det_api/
    env_file:
      - .env
    command: bash -c "pip install --force-reinstall dist/*.whl && ugsdetapi"
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true