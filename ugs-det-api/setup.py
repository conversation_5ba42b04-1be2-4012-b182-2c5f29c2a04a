#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : setup.py
@Project : ugs-det-api
<AUTHOR> mingxing
@Date    : 2023/8/30 11:38
"""

from setuptools import setup, Extension
from setuptools.command.build_py import build_py as build_py_orig
from Cython.Build import cythonize
import src.ugs_det_api.package_info as pi


ext_modules = [
    Extension("ugs_det_api.*", ["src/ugs_det_api/*.py"]),
    Extension("ugs_det_api.pub.*", ["src/ugs_det_api/pub/*.py"]),
    Extension("ugs_det_api.series.*", ["src/ugs_det_api/series/*.py"]),
    Extension("ugs_det_api.study.*", ["src/ugs_det_api/study/*.py"]),
    Extension("ugs_det_api.task.*", ["src/ugs_det_api/task/*.py"]),
    Extension("ugs_det_api.user.*", ["src/ugs_det_api/user/*.py"]),
    Extension("ugs_det_api.utils.*", ["src/ugs_det_api/utils/*.py"])
]


class BuildPy(build_py_orig):
    def build_packages(self):
        pass


setup(
    name=pi.name,
    author=pi.author,
    url=pi.url,
    cmdclass={"build_py": BuildPy},
    ext_modules=cythonize(ext_modules, language_level="3"),
    version=pi.version,
    packages=[pi.name],
    package_dir={"": "src"},
    platforms=pi.platforms,
    description=pi.description,
    long_description=pi.long_description,
    license="MIT",
    entry_points={
        "console_scripts": [
            "ugsdetapi = ugs_det_api.main:run"
        ]
    }
)
