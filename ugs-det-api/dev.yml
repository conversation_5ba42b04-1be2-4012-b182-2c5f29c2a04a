version: "3"
services:
  ugs-det-api:
    container_name: "ugs-det-api"
    image: harbor.unionstrongtech.com/ugs/det:1.0.0
    ports:
      - ${SERVICE_PORT}:${SERVICE_PORT}
    volumes:
      - ./src/ugs_det_api:/ugs_det_api
      - ./dev.pem:/ugs_det_api/public.pem
      - /data/ctpdata:/data/ctpdata
      - /home/<USER>/upixel-station-backend/server/static/jpg:/code/vr
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    working_dir: /ugs_det_api/
    env_file:
      - .env
    command: python3 -u main.py
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true