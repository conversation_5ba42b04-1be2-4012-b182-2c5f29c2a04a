version: "3"
services:
  ugs-center-line:
    container_name: "ugs-center-line"
    image: harbor.unionstrongtech.com/ugs/ctp_a4000:1.3.3b0
    volumes:
      - ./dist:/ugs_ctp/dist
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    working_dir: /ugs_ctp/
    env_file:
      - .env
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    command: bash -c "pip install --force-reinstall dist/*.whl && ugscenterline"
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true
