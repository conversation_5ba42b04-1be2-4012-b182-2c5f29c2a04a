#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : setup.py
@Project : ugs-center-line
<AUTHOR> mingxing
@Date    : 2023/3/28 11:04
"""
from setuptools import setup, Extension
from setuptools.command.build_py import build_py as build_py_orig
from Cython.Build import cythonize
import src.ugs_center_line.package_info as pi


ext_modules = [
    Extension("ugs_center_line.*", ["src/ugs_center_line/*.py"]),
    Extension("ugs_center_line.lib.*", ["src/ugs_center_line/lib/*.py"])
]


class BuildPy(build_py_orig):
    def build_packages(self):
        pass


setup(
    name=pi.name,
    author=pi.author,
    url=pi.url,
    cmdclass={"build_py": BuildPy},
    ext_modules=cythonize(ext_modules, language_level="3"),
    version=pi.version,
    packages=[pi.name],
    package_dir={"": "src"},
    package_data={pi.name: ["static/font/*.ttf", "static/py/*.py"]},
    platforms=pi.platforms,
    description=pi.description,
    long_description=pi.long_description,
    license="MIT",
    entry_points={
        "console_scripts": [
            "ugscenterline = ugs_center_line.consumer:main"
        ]
    }
)
