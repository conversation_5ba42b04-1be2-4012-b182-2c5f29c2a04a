version: "3"
services:
  ugs-center-line:
    container_name: "ugs-center-line"
    image: harbor.unionstrongtech.com/ugs/ctp_a4000:1.3.3b0
    volumes:
      - ./src/ugs_center_line:/ugs_center_line
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    working_dir: /ugs_center_line/
    env_file:
      - .env
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    command: python3 -u consumer.py
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true
