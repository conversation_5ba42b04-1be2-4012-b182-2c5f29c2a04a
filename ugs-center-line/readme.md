### dev2  A100上给GE演示版本

```text

 CTP 
 版本：
 增加了 对薄层ctp处理cta
  
判断厚度小于1 处理cta

 需要把/mipf-env/bin 目录下 拷贝到部署机器的 /mipf-env/bin 下

 
 演示环境使用A100环境
 ctp 需要编译cuda11版本 **********/library/algorithm_cuda11_1:ctp_v1
 需要把A100 /mipf-env/bin 目录下 拷贝到部署机器的 /mipf-env/bin 下
 
 
 算法启动使用 docker-compose-algorithm-ctp.yml 启动
 算法启动使用 A100  docker-compose-algorithm-ctp-a100.yml 启动
 
 A100  需要拷贝 3.6 /home/<USER>/mipf-env-cuda11/mipf  到 A100 的 /mipf-env/bin
 其他服务器部署 需要拷贝 3.6 /mipf-env/bin  到 /mipf-env/bin
 
 # A100上使用
 
 修改ctp_script 219行
 
 #ctpTool.ExtractCTA(extract_cta,-2)   #A100使用
            
   tpTool.ExtractCTA(extract_cta)

```

#### dev2_v1.1  ctp使用python包

```text
CTP通过wheel部署
安装第三方依赖包
*********** 服务器/home/<USER>/mipfthird/目录下，下载mipfthird-XXX-linux-amd64.deb包，通过
dpkg -i  mipfthird-XXX-linux-amd64.deb安装
这个包一经安装不需要经常更新，长期有效。
安装算法包
*********** 服务器/home/<USER>/mipf/目录下，下载最新版本的mipf-XXX.whl包，通过pip install 安装，安装时会自动覆盖旧版本。下载罪行的mipf-XXX-linux-amd64.deb包，通过dpkg -i mipf-XXX-linux-amd64.deb安装。
Demo
运行前执行shell
export LD_LIBRARY_PATH=/usr/lib/mipfthird:/usr/lib/mipfthird/ep:/usr/lib/mipfthird/Qt5.7:/usr/lib/mipfthird/opencv3.4.6:/usr/lib/mipfthird/MitkCore:/usr/lib/mipf:$LD_LIBRARY_PATH
export DCM_DATA_DIR=/usr/lib/mipfthird/ep/dcmtk  
export DCMDICTPATH=$DCM_DATA_DIR/dicom.dic:$DCM_DATA_DIR/private.dic:$DCM_DATA_DIR/acrnema.dic:$DCM_DATA_DIR/diconde.dic  
export QT_QPA_PLATFORM_PLUGIN_PATH=/usr/lib/mipfthird/Qt5.7/plugins
export QT_QPA_PLATFORM='offscreen'


镜像使用 **********/uguard/algorithm_cuda11_1:ctp_v2
启动 docker-compose-algorithm-ctp-a100.yml

```

### dev2_v1.2

```text

python  版本，增加tf版本
```

### dev2_v1.3 支持送检版本

```text
开启送检版本
在 .env_algorithem配置 SONGJIAN=True 


1. 增加动静脉点得获取,
2. 增加传入动静脉点后重新计算
3. 支持2060

ctp版本及存放目录
ctp python版本 0.6.0
1，16下
home/songling/Projects/Build/mipf-cuda11-install/python/dist
GetAIFIndex 获取动脉点
GetVOFIndex 获取静脉点

镜像使用：**********/uguard/ctp_test:v6.0.2

```

#### dev2_v1.4 ctp适配东芝数据

```text
.env_algorithem 新增东芝数据开关： TOSHIBA=1
数据设置  SeriesDescription  Head 0.5 CE   ctp

.env_algorithem 新增自定义配置：CTP_ALGORITHM_CONFIG=/algorithm_server/PyMain/config.ini（/algorithm_server对应的宿主机uguard_ctp目录）

镜像使用：************:808/uguard/ctp_cuda11.4:0.8.4_new
```

#### dev2_v1.5

```
CTP支持A4000

镜像使用：************:808/uguard/algorithm_ctp_cuda11.3_a4000:0.9.0b0
```