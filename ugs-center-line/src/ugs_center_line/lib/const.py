#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : const
@Project : uguard_ctp
<AUTHOR> mingxing
@Date    : 2022/9/15 16:33
"""
import os
from enum import Enum

_env = os.environ


class Consts:
    RESULT_CATEGORY = {
        "summary": ["USC-UGuard CTP Summary", "USC-UGuard CTP Total Summary"],
        "colorMap": ["USC-UGuard Perfusion Parameter Maps Colored CBV",
                     "USC-UGuard Perfusion Parameter Maps Colored CBF",
                     "USC-UGuard Perfusion Parameter Maps Colored MTT",
                     "USC-UGuard Perfusion Parameter Maps Colored Tmax",
                     "USC-UGuard Perfusion Parameter Maps Colored TTP",
                     "USC-UGuard Perfusion Parameter Maps Colored PS"],
        "mip": ["USC-UGuard AIF-VOF Location", "USC-UGuard CTP MIP"]
    }
    DIR_CATEGORY = {
        "summary": ["USC_UGuard_CTP_Summary", "USC_UGuard_CTP_Total_Summary"],
        "colorMap": ["USC_UGuard_CTP_Parameter_Colored_Map_CBV",
                     "USC_UGuard_CTP_Parameter_Colored_Map_CBF",
                     "USC_UGuard_CTP_Parameter_Colored_Map_MTT",
                     "USC_UGuard_CTP_Parameter_Colored_Map_Tmax",
                     "USC_UGuard_CTP_Parameter_Colored_Map_TTP",
                     "USC_UGuard_CTP_Parameter_Colored_Map_PS"],
        "mip": ["USC_UGuard_CTP_AIF_VOF_CSF_Location", "USC_UGuard_CTP_MIP"]
    }
    PACS_TARGET = "target_pacs"
    CODE_REPORT_BACK_METHOD = "reportBackMethod"
    CODE_REPORT_CTP_MERGE = "ctpReportMerge"
    CODE_REPORT_CTP_SUMMARY = "reportCtpSummary"
    CODE_REPORT_CTP_COLORMAP = "reportCtpColorMap"
    CODE_REPORT_CTP_MIP = "reportCtpMip"
    BACK_METHOD_DEFAULT = "orthanc"
    CODE_ALGORITHM_RESULT_MODALITY = "algorithmResultModality"
    DEFAULT_ALGORITHM_RESULT_MODALITY = "OT"


class Config:
    # # Common
    SERVICE_NAME = _env.get("SERVICE_NAME", "ugs-center-line")
    SERVICE_VERSION = _env.get("SERVICE_VERSION", "1.0.0")
    # RabbitMQ Configuration
    MQ_HOST = _env.get("MQ_HOST", "ugs-rabbitmq")
    MQ_PORT = _env.get("MQ_PORT", 5673)
    MQ_USERNAME = _env.get("MQ_USERNAME", "unionstrong")
    MQ_PASSWORD = _env.get("MQ_PASSWORD", "UnionStrong@2020")
    # PACS Configuration`
    PACS_HOST = _env.get("PACS_HOST", "ugs-pacs")
    PACS_AET = _env.get("PACS_AET", "DockerOrthanc")
    PACS_PORT = int(_env.get("PACS_PORT", 4242))
    PACS_DOCKER_PORT = int(_env.get("PACS_DOCKER_PORT", 8042))
    PACS_USERNAME = _env.get("PACS_USERNAME", "unionstrong")
    PACS_PASSWORD = _env.get("PACS_PASSWORD", "UnionStrong@2020")
    # WebApi Configuration
    WEBAPI_HOST = _env.get("WEBAPI_HOST", "ugs-api")
    WEBAPI_PORT = _env.get("WEBAPI_PORT", "4201")
    # Path Configuration
    DIR_ROOT = _env.get("ROOT_DIR", "/data/ctpdata")
    DIR_CTP = os.path.join(DIR_ROOT, "ctp")
    DIR_DCM = os.path.join(DIR_ROOT, "dcm")
    DIR_STATIC = os.path.join(DIR_ROOT, "static")


class RetCode(Enum):
    """返回码枚举类"""

    # 平台（40000~49999）
    PLATFORM_SERIES_NOT_FOUND = (45000, "Series Not Found")
    PLATFORM_UPLOAD_REPORT_ERROR = (45001, "Upload Report Error")
    # CTP算法（50000~59999）
    CTP_OK = (200, "Process successfully!")
    CTP_ERROR = (50000, "CTP error!")
    CTP_INITIALIZE_FAILED = (50001, "Initialize environment failed!")
    CTP_PARSE_FAILED = (50002, "Parse ctp series failed!")
    CTP_LOAD_FAILED = (50003, "Load ctp series failed!")
    CTP_REGISTER_FAILED = (50004, "Motion correction failed!")
    CTP_EXTRACT_BRAIN_FAILED = (50005, "Extract brain failed!")
    CTP_PREPROCESS_FAILED = (50006, "Preprocess failed!")
    CTP_GAMMA_FIT_FAILED = (50007, "Gamma fit failed!")
    CTP_EXTRACT_CSF_FAILED = (50008, "Extract csf failed!")
    CTP_DETECTED_AVP_FAILED = (50009, "Detect aif and vof point failed!")
    CTP_GENERATE_FEATURE_MAP_FAILED = (50010, "Generate feature map failed!")
    CTP_FEATURE_COMPARE_FAILED = (50011, "Compare feature map failed!")
    CTP_GENERATE_RESULTS_FAILED = (50012, "Generate results failed!")
    SYMMETRY_FAILED = (50013, "Extract symmetry failed!")
    CENTER_LINE_ERROR = (50014, "CenterLine error!")
    @property
    def code(self):
        """
        获取状态码
        """
        return self.value[0]

    @property
    def msg(self):
        """
        获取状态码信息
        """
        return self.value[1]

