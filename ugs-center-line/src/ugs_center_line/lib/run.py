#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : run
@Project : uguard_center_line
<AUTHOR> mingxing
@Date    : 2022/9/15 16:31
"""
import json
import os
import time
from threading import Thread, Event

from lib.common import RabbitProducer
from lib.const import Config, RetCode
from lib.logger import MyLogger
from lib.center_line import get_center_line

log = MyLogger()


def handle_center_line(channel, method, study_instance_uid, series, algorithm_type, **kwargs):
    """
    CenterLine处理

    :param channel:
    :param method:
    :param study_instance_uid:
    :param series:
    :param algorithm_type:
    :param kwargs:
    :return:
    """
    start_time = time.time()
    # 东芝灌注
    toshiba = kwargs.get("toshiba", False)
    percent_message = dict(studyInstanceUID=study_instance_uid, algorithmType=algorithm_type, toshiba=toshiba)
    percent_message["seriesInstanceUIDs" if toshiba else "seriesInstanceUID"] = series
    # 检查图像
    _dicom_dir = os.path.join(Config.DIR_DCM, study_instance_uid if toshiba else series)
    if not os.path.exists(_dicom_dir):
        log.info("CenterLine > series dcm not found")
        percent_message.update({"percent": 500, "errorCode": RetCode.CENTER_LINE_ERROR.code, "errorMsg": "dicom图像路径不存在"})
        RabbitProducer.send(percent_message)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return False
    
    start_time = time.time()
    
    # 创建进度报告线程
    stop_event = Event()  # 用于控制进度报告的停止

    def report_progress():
        progress = 5
        while progress <= 90 and not stop_event.is_set():  # 增加停止条件
            RabbitProducer.send(dict(percent=progress, **percent_message))
            progress += 5
            time.sleep(3)
    # 使用线程发送进度
    progress_thread = Thread(target=report_progress)
    progress_thread.daemon = True
    progress_thread.start()
    
    # 获取center line结果
    lines, success = get_center_line(_dicom_dir)
    stop_event.set()  # 设置停止标志，结束进度报告
    end_time = time.time()
    log.info("CenterLine[study:{}, series:{}] > result: {}".format(study_instance_uid, series, success))
    # CenterLine计算失败
    if not success:
        log.error("CenterLine[study:{}, series:{}] > failed to get center_line result".format(study_instance_uid, series))
        percent_message.update({"percent": 500, "errorCode": RetCode.CENTER_LINE_ERROR.code, "errorMsg": "左脑或右脑点数量太少"})
        RabbitProducer.send(percent_message)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return False
    # 发送算法处理时间
    run_time = end_time - start_time
    time_message = dict(studyInstanceUID=study_instance_uid, algorithmType=algorithm_type, toshiba=toshiba,
                        consumerTime=run_time, percent=95)
    time_message["seriesInstanceUIDs" if toshiba else "seriesInstanceUID"] = series
    RabbitProducer.send(time_message)
    
    # center_line算法成功
    RabbitProducer.send(dict(percent=100, result=json.dumps(lines), **percent_message))
    channel.basic_ack(delivery_tag=method.delivery_tag)
    end_time = time.time()
    log.info("CenterLine[study:{}, series:{}] > CenterLine: {:.2f}s, Platform: {:.2f}s, Total: {:.2f}s".format(
        study_instance_uid, series, run_time, (end_time - start_time - run_time), (end_time - start_time)))
    log.info("********************** success **********************")
    return True
