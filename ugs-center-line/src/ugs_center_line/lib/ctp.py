#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : ctp
@Project : uguard_ctp
<AUTHOR> mingxing
@Date    : 2022/9/15 16:58
"""
import datetime
import os
import shutil
import sys
import re
import time
import traceback
from multiprocessing import Manager, Process
import subprocess
import tempfile

import SimpleITK as sitk
import pandas as pd
import numpy as np
import usia.pyctp as pyctp
import usia.pymipf as pymipf
import usia.pyitk as pyitk
import imgkit
from ctp_vessel_segmentation import *


from lib.common import DicomUtils, FileUtils, StringUtils, WebApi
from lib.const import RetCode, Config, Consts
from lib.logger import MyLogger, CtpLogger

log = MyLogger()


class CtpUtils:

    @staticmethod
    def load_config(session):
        config_dir = session.GetConfigPath()
        conf_list = WebApi.get_config([], category="ctp", tag="init")
        if not conf_list:
            log.warning("ctp config not found")
            return
        conf_size = len(conf_list)
        log.info("find {} configurations".format(conf_size))
        for conf in conf_list:
            _code = conf.get("code", "")
            _value = conf.get("value", "")
            _format = conf.get("format", "")
            if len(_code) == 0 or len(_value) == 0 or len(_format) == 0:
                log.info("invalid config: {}".format(conf))
                continue
            if _code == "ctpFalseColor":
                lut_time = ""
                lut_value = ""
                if _value == "siemens":
                    lut_time = os.path.join(config_dir, "siemens-time.lut")
                    lut_value = os.path.join(config_dir, "siemens-value.lut")
                session.SetStringConfig("MTT-Color-LookupTable-File", lut_time)
                session.SetStringConfig("Tmax-Color-LookupTable-File", lut_time)
                session.SetStringConfig("TTP-Color-LookupTable-File", lut_time)
                session.SetStringConfig("CBF-Color-LookupTable-File", lut_value)
                session.SetStringConfig("CBV-Color-LookupTable-File", lut_value)
                session.SetStringConfig("PS-Color-LookupTable-File", lut_value)
                continue
            if _code == "Threshold-Segmentation-Range":
                value_list = _value.split(",")
                CtpUtils.__set_config(session, "Csf-Threshold", value_list[0], _format)
                CtpUtils.__set_config(session, "Vessel-Threshold", value_list[1], _format)
                continue
            CtpUtils.__set_config(session, _code, _value, _format)
        log.info("{} configurations imported successfully".format(conf_size))

    @staticmethod
    def __set_config(session, _key, _value, _format):
        if _format == "text":
            session.SetStringConfig(_key, str(_value))
            return
        if _format == "bool":
            session.SetBoolConfig(_key, StringUtils.to_bool(_value))
            return
        if _format == "int":
            session.SetIntConfig(_key, int(_value))
            return
        if _format == "float":
            session.SetDoubleConfig(_key, float(_value))
            return
        log.info("invalid config format, key:{}, value:{}, format:{}".format(_key, _value, _format))

    @staticmethod
    def process_ctp_series(ctp_dir, session, auto=True, ventricle_model=True, partition_model=True,
                           manual_inputs=None, result_dir=None, ctp_vessel_model=True, gpu_id=0):
        ctp_tools = pyctp.CTPTools()
        start = time.perf_counter()
        try:
            if auto:
                ctp_tools.ProcessCTPImages(ctp_dir)
            else:
                if not ctp_tools.Initialize(pymipf.GetSession()):
                    log.error("pyctp[CTPTools] > Initialize environment failed")
                    return ctp_tools, RetCode.CTP_INITIALIZE_FAILED, CtpUtils.get_processed_time(start)

                if result_dir:
                    ctp_tools.SetInternelImageSaveDir(result_dir)
                    # True保存调试结果
                    ctp_tools.SetSaveInternelImage(True)

                if not ctp_tools.ParseCTPImages(ctp_dir):
                    log.error("pyctp[CTPTools] > Parse ctp series failed")
                    return ctp_tools, RetCode.CTP_PARSE_FAILED, CtpUtils.get_processed_time(start)

                if not ctp_tools.LoadCTPImages():
                    log.error("pyctp[CTPTools] > Load ctp series failed")
                    return ctp_tools, RetCode.CTP_LOAD_FAILED, CtpUtils.get_processed_time(start)

                if not ctp_tools.RegisterCTPImages():
                    pass

                if not ctp_tools.ExtractCTPBrain():
                    log.error("pyctp[CTPTools] > Extract brain failed")
                    return ctp_tools, RetCode.CTP_EXTRACT_BRAIN_FAILED, CtpUtils.get_processed_time(start)

                if not ctp_tools.PrepareCTPImages():
                    log.error("pyctp[CTPTools] > Preprocess failed")
                    return ctp_tools, RetCode.CTP_PREPROCESS_FAILED, CtpUtils.get_processed_time(start)

                symmetry_extracted = False
                if ventricle_model:
                    log.info("Partition brain using model!")
                    pyItkImage = ctp_tools.GetImage(0)
                    sitkImage = CtpUtils.itk_to_sitk_image(pyItkImage)
                    partition_label = CtpUtils.partition_brain(sitkImage)
                    ctp_tools.SetMaskImage(CtpUtils.sitk_to_itk_image(partition_label, np.uint8), "brain-partition")

                    # prepare_dir(result_dir,False)
                    # sitk.WriteImage(partition_label, os.path.join(result_dir, "brain_seg.nii.gz"))
                    if session.GetIntConfig("Symmetry-Method", 2) == 2:
                        log.info("Extract Symmetry Using SVM")
                        center, normal, success = CtpUtils.extract_brain_symmetry_plane_with_svm(partition_label)
                        if success:
                            log.info("partition symmetry:{}, {}".format(center, normal))
                            symmetry_extracted = ctp_tools.UpdateSymmetryPlane(center, normal)
                        else:
                            log.info("Extract Symmetry Using SVM Failed!")

                    array_label = sitk.GetArrayFromImage(partition_label)
                    array_label[array_label < 35] = 0
                    array_label[array_label > 34] = 1
                    venticle_label = sitk.GetImageFromArray(array_label)
                    venticle_label.SetOrigin(partition_label.GetOrigin())
                    venticle_label.SetSpacing(partition_label.GetSpacing())
                    venticle_label.SetDirection(partition_label.GetDirection())
                    ctp_tools.SetMaskImage(CtpUtils.sitk_to_itk_image(venticle_label, np.uint8), "csf-origin")

                    # print("csf stats:", pyctp.GetImageStatistics(ctp_tools.GetInternelImage("base_smoothed"),
                    #                                              sitk_to_itk_image(venticle_label, np.uint16),1),
                    #       result_dir)

                    venticle_label = sitk.BinaryMorphologicalClosing(venticle_label, [4, 2, 1])
                    venticle_label = sitk.BinaryDilate(venticle_label, [1, 1, 1])
                    ctp_tools.SetMaskImage(CtpUtils.sitk_to_itk_image(venticle_label, np.uint8), "csf")
                    log.info("Extract csf using threshold!")
                    ctp_tools.ExtractCSF()
                else:
                    log.info("Extract csf using threshold!")
                    ctp_tools.ExtractCSF()

                if not ((session.GetIntConfig("Symmetry-Method", 2) == 2) and ventricle_model and symmetry_extracted):
                    log.info("Extract Symmetry Using Registration!")
                    if not ctp_tools.ExtractSymmetryPlane():
                        return ctp_tools, RetCode.SYMMETRY_FAILED, CtpUtils.get_processed_time(start)

                if ctp_tools.ComputeImageStatistics():
                    statics = ctp_tools.GetImageStatistics()
                    log.info("pyctp[CTPTools] > Image Statistics: {}".format(statics))

                if not ctp_tools.GammaFit():
                    log.error("pyctp[CTPTools] > Gamma fit failed")
                    return ctp_tools, RetCode.CTP_GAMMA_FIT_FAILED, CtpUtils.get_processed_time(start)

                if session.GetBoolConfig("Remove-ICH", True):
                    log.info("Extract ICH!")
                    pyItkImage = ctp_tools.GetImage(0)
                    sitkImage = CtpUtils.itk_to_sitk_image(pyItkImage)
                    ich_label = CtpUtils.extract_ich(sitkImage)
                    if ich_label:
                        ctp_tools.SetMaskImage(CtpUtils.sitk_to_itk_image(ich_label, np.uint8), "ich")
                    else:
                        empty_img = CtpUtils.itk_to_sitk_image(ctp_tools.GetMaskImage("mask"))
                        empty_img = sitk.Multiply(empty_img, 0)
                        ctp_tools.SetMaskImage(CtpUtils.sitk_to_itk_image(empty_img, np.uint8), "ich")

                if ctp_vessel_model:
                    log.info("Extract ctp vessel using model!")
                    pyItkImage = ctp_tools.GetInternelImage("mip")
                    sitkImage = CtpUtils.itk_to_sitk_image(pyItkImage)
                    vessel_label = mip_vessel_seg(sitkImage)
                    itk_image = pyitk.UChar3DImage()
                    itk_image.FromPyArray(sitk.GetArrayFromImage(vessel_label).astype(np.int8),
                                          pyItkImage.GetOrigin(),
                                          pyItkImage.GetSpacing(),
                                          pyItkImage.GetDirection())
                    ctp_tools.SetMaskImage(itk_image, "vessel")

                if not manual_inputs:
                    if not ctp_tools.DetectAifVofPoints():
                        log.error("pyctp[CTPTools] > Detect aif and vof point failed")
                        return ctp_tools, RetCode.CTP_DETECTED_AVP_FAILED, CtpUtils.get_processed_time(start)
                else:
                    log.info("pyctp[CTPTools] > Using manual inputs: {}".format(manual_inputs))
                    if "aif" in manual_inputs.keys():
                        ctp_tools.SetAIFIndex(manual_inputs["aif"])
                    if "vof" in manual_inputs.keys():
                        ctp_tools.SetVOFIndex(manual_inputs["vof"])
                    if "aif_data" in manual_inputs.keys():
                        ctp_tools.SetAIFData(manual_inputs["aif_data"])
                    if "vof_data" in manual_inputs.keys():
                        ctp_tools.SetVOFData(manual_inputs["vof_data"])

                if not ctp_tools.GenerateFeatureMaps():
                    log.error("pyctp[CTPTools] > Generate feature map failed")
                    return ctp_tools, RetCode.CTP_GENERATE_FEATURE_MAP_FAILED, CtpUtils.get_processed_time(start)

                if not ctp_tools.FeatureCompare():
                    log.error("pyctp[CTPTools] > Generate feature map failed")
                    return ctp_tools, RetCode.CTP_FEATURE_COMPARE_FAILED, CtpUtils.get_processed_time(start)

                if not ctp_tools.GenerateResults():
                    log.error("pyctp[CTPTools] > Generate results failed")
                    return ctp_tools, RetCode.CTP_GENERATE_RESULTS_FAILED, CtpUtils.get_processed_time(start)

                log.info("pyctp[CTPTools] > Multi Threshold Summary: {}".format(ctp_tools.GetMultiThresholdSummary()))

                if partition_model:
                    log.info("pyctp[CTPTools] > Partition brain using model!")
                    mean_res = ctp_tools.GetBrainPartition()
                    if bool(mean_res):
                        with tempfile.TemporaryDirectory() as temp_dir:
                            table_png_path = CtpUtils.get_brain_partition_table(
                                mean_res, session.GetConfigPath(), temp_dir)
                            ctp_tools.SetBrainPartitionTable(table_png_path)
                            log.info("pyctp[CTPTools] > Partition brain finish!")
                    else:
                        log.info("pyctp[CTPTools] > Partition brain Failed!")

                ctp_tools.ExtractResults()
                ctp_tools.PaintReport()

            return ctp_tools, RetCode.CTP_OK, CtpUtils.get_processed_time(start)
        except Exception:
            log.error(traceback.format_exc())
            return ctp_tools, RetCode.CTP_ERROR, CtpUtils.get_processed_time(start)

    @staticmethod
    def get_processed_time(start):
        return time.perf_counter() - start

    @staticmethod
    def prepare_dir(dir, overwrite=True):
        if os.path.exists(dir):
            if overwrite:
                try:
                    shutil.rmtree(dir)
                except OSError as Argument:
                    print(Argument)
                os.makedirs(dir)
        else:
            os.makedirs(dir)

    @staticmethod
    def itk_to_sitk_image(itk_image):
        sitk_image = sitk.GetImageFromArray(itk_image.ToPyArray())
        sitk_image.SetSpacing(itk_image.GetSpacing())
        sitk_image.SetOrigin(itk_image.GetOrigin())
        sitk_image.SetDirection(itk_image.GetDirection())
        return sitk_image

    @staticmethod
    def sitk_to_itk_image(sitk_image, dtype=np.float32, contiguous="F"):
        itk_image = None
        if dtype == np.float32:
            itk_image = pyitk.Float3DImage()
        elif dtype == np.uint8:
            itk_image = pyitk.UChar3DImage()
        elif dtype == np.int8:
            itk_image = pyitk.Char3DImage()
        elif dtype == np.int16:
            itk_image = pyitk.Short3DImage()
        elif dtype == np.uint16:
            itk_image = pyitk.UShort3DImage()
        elif dtype == np.int32:
            itk_image = pyitk.Int3DImage()
        elif dtype == np.uint32:
            itk_image = pyitk.UInt3DImage()
        else:
            raise TypeError("Invalid image pixel type {}".format(dtype))

        itk_image.FromPyArray(sitk.GetArrayFromImage(sitk_image).astype(dtype),
                              sitk_image.GetOrigin(),
                              sitk_image.GetSpacing(),
                              sitk_image.GetDirection(),
                              contiguous="F")
        return itk_image

    @staticmethod
    def get_result_dir_name(in_dir, out_dir, parents=1, skip=False):
        out_names = re.split("['\\\\/']+", in_dir)
        out_names = list(filter(lambda x: x != '', out_names))
        if parents > len(out_names):
            print("Wrong parents {} for input dir {}!".format(parents, in_dir))
            return None
        out_names.reverse()
        filter(None, out_names)
        out_name = []
        if skip:
            out_name.append(out_names[parents - 1])
        else:
            for i in range(parents):
                out_name.insert(0, out_names[i])
        result_dir = os.path.join(out_dir, os.sep.join(out_name))
        return result_dir

    @staticmethod
    def extract_brain_symmetry_plane_with_svm(brain_segment: sitk.Image, sample_rate=0.1, left_labels=[33, 34, 31],
                                              right_labels=[26, 27, 24]):
        start = time.perf_counter()

        from sklearn.svm import SVC
        from random import sample

        ppis = sitk.PhysicalPointImageSource()
        ppis.SetSpacing(brain_segment.GetSpacing())
        ppis.SetOrigin(brain_segment.GetOrigin())
        ppis.SetSize(brain_segment.GetSize())
        ppis.SetDirection(brain_segment.GetDirection())
        ppis_image = ppis.Execute()

        image_array = sitk.GetArrayFromImage(brain_segment)
        image_array = np.expand_dims(image_array, 3)
        ppis_array = sitk.GetArrayFromImage(ppis_image)

        left_points = []
        right_points = []
        for x, y in np.nditer([image_array, ppis_array], flags=["external_loop"]):
            if x[0] in left_labels:
                left_points.append(y)
            elif x[0] in right_labels:
                right_points.append(y)

        if len(left_points) < 10 or len(right_points) < 10:
            return None, None, False

        sample_num = int(min(len(left_points) * sample_rate, len(right_points) * sample_rate))
        print("Sample number:", sample_num)

        x_left = sample(left_points, sample_num)
        x_right = sample(right_points, sample_num)

        y_left = [1] * len(x_left)
        y_right = [0] * len(x_right)

        x_train = x_left + x_right
        y_train = y_left + y_right

        model = SVC(kernel="linear")
        model.fit(x_train, y_train)

        weight = model.coef_[0]  # 取出权重矩阵
        bias = model.intercept_[0]

        k0 = -weight[0] / weight[2]
        k1 = -weight[1] / weight[2]
        b = -bias / weight[2]

        arr = np.array([k0, k1, -1])
        normal = arr / np.linalg.norm(arr)
        center = model.support_vectors_.mean(axis=0)

        end = time.perf_counter()
        log.info("Extract Symmetry takes: {:.2f}s".format(end - start))
        return center, normal, True

    @staticmethod
    def GenerateMultiThresholdResultFile(results, filename, session):
        dfs = {}
        for k, v in results.items():
            items_str = session.GetStringConfig(k, '')
            item_list = items_str.split(',')
            value_list = v
            assert len(item_list) == len(value_list), "Uncomparable list for results {}".format(k)
            df = pd.DataFrame({'parameter': item_list, "volume/ml": value_list})
            dfs[k] = df
        with pd.ExcelWriter(filename) as writer:
            for k, v in dfs.items():
                v.to_excel(writer, sheet_name=k, index=False)

    @staticmethod
    def get_statistics(results, others=None):
        CBF_Volumes = results.GetCBFVolumes()
        Tmax_Volumes = results.GetTMaxVolumes()
        CBV_Volumes = results.GetCBVVolumes()

        statistics = {}
        statistics["CBF<30%"] = CBF_Volumes[0]
        statistics["CBF<34%"] = CBF_Volumes[1]
        statistics["CBF<38%"] = CBF_Volumes[2]
        statistics["CBF<50%"] = CBF_Volumes[3]
        statistics["CBF<70%"] = CBF_Volumes[4]

        statistics["CBV<34%"] = CBV_Volumes[0]
        statistics["CBV<38%"] = CBV_Volumes[1]
        statistics["CBV<42%"] = CBV_Volumes[2]
        statistics["CBV<50%"] = CBV_Volumes[3]
        statistics["CBV<70%"] = CBV_Volumes[4]

        statistics["Tmax>10s"] = Tmax_Volumes[0]
        statistics["Tmax>8s"] = Tmax_Volumes[1]
        statistics["Tmax>6s"] = Tmax_Volumes[2]
        statistics["Tmax>4s"] = Tmax_Volumes[3]

        if others:
            statistics.update(others)

        return statistics

    @staticmethod
    def extract_index(filename):
        with open(filename, encoding="utf-8") as file_obj:
            contents = file_obj.read()
            return [int(x) for x in contents.split(",")]

    @staticmethod
    def extract_ventricle(input_image: sitk.Image, gpu_id=0) -> sitk.Image:
        result_image = None
        with tempfile.TemporaryDirectory() as temp_dir:
            input_filename = os.path.join(temp_dir, "input.nii")
            ventricle_filename = os.path.join(temp_dir, "ventricle.nii")
            sitk.WriteImage(input_image, input_filename)
            # command = "{} extract_ventricle.py".format(sys.executable) + " -i " + '\"' + input_filename + '\"' + " -o " \
            #           + '\"' + ventricle_filename + '\"' + " -g {}".format(gpu_id)
            py_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/py/extract_ventricle.py")
            command = '{} {} -i "{}" -o "{}" -g {}'.format(
                sys.executable, py_path, input_filename, ventricle_filename, gpu_id)
            log.info(command)
            p = subprocess.Popen(command, shell=True)
            p.wait()
            result_image = sitk.ReadImage(ventricle_filename)
            result_image.CopyInformation(input_image)
        return result_image

    @staticmethod
    def extract_ich(input_image: sitk.Image, gpu_id=0) -> sitk.Image:
        result_image = None
        with tempfile.TemporaryDirectory() as temp_dir:
            input_filename = os.path.join(temp_dir, "input.nii")
            ich_filename = os.path.join(temp_dir, "ich.nii")
            sitk.WriteImage(input_image, input_filename)
            # command = "{} extract_ich.py".format(sys.executable) + " -i " + '\"' + input_filename + '\"' + " -o " \
            #           + '\"' + ich_filename + '\"' + " -g {}".format(gpu_id)
            py_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/py/extract_ich.py")
            command = '{} {} -i "{}" -o "{}" -g {}'.format(
                sys.executable, py_path, input_filename, ich_filename, gpu_id)
            log.info(command)
            p = subprocess.Popen(command, shell=True)
            p.wait()
            result_image = sitk.ReadImage(ich_filename)
            result_image.CopyInformation(input_image)
        return result_image

    @staticmethod
    def partition_brain(input_image: sitk.Image) -> sitk.Image:
        result_image = None
        with tempfile.TemporaryDirectory() as temp_dir:
            input_filename = os.path.join(temp_dir, "input.nii")
            partition_filename = os.path.join(temp_dir, "partition_label.nii")
            sitk.WriteImage(input_image, input_filename)
            # command = "{} partition_brain.py".format(sys.executable) + " -i " + '\"' + input_filename + '\"' + " -o " \
            #           + '\"' + partition_filename + '\"'
            py_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/py/partition_brain.py")
            command = "{} {} -i '{}' -o '{}'".format(sys.executable, py_path, input_filename, partition_filename)
            log.info(command)
            p = subprocess.Popen(command, shell=True)
            p.wait()
            result_image = sitk.ReadImage(partition_filename)
            result_image.CopyInformation(input_image)
        return result_image

    @staticmethod
    def is_warning(vs):
        left_to_right = vs[0] / (vs[1] + 1e-5)
        right_to_left = vs[1] / (vs[0] + 1e-5)
        reduce = min(left_to_right, right_to_left)
        return abs(reduce) < 0.7

    @staticmethod
    def get_brain_partition_table(mean_res, table_template_dir, temp_dir):
        table_template = [os.path.join(table_template_dir, "ctp-before.html"),
                          os.path.join(table_template_dir, "ctp-after.html")]
        out_before_path = ""
        out_after_path = ""
        with open(table_template[0], "r", encoding='utf-8') as f:
            htmlfile_before = f.read()
            for k, values in mean_res.items():
                for index, value in enumerate(values):
                    if index < 10:
                        jump = 10
                    elif index == 25:
                        jump = 7
                    else:
                        continue
                    vs = [value, values[index + jump]]
                    olds = ["S" + str(index + 1) + k, "S" + str(index + 1 + jump) + k]
                    news = []
                    warning = CtpUtils.is_warning(vs)
                    for v in vs:
                        if not np.isnan(v) and v > 0.0:
                            if warning:
                                news.append('<b style="color: red">{}</b>'.format(round(v, 1)))
                            else:
                                news.append(str(round(v, 1)))
                    for old, new in zip(olds, news):
                        htmlfile_before = htmlfile_before.replace(old, new)
            out_before_path = os.path.join(temp_dir, "ctp-before.png")
            imgkit.from_string(htmlfile_before, out_before_path, options={"width": 890})

        with open(table_template[1], "r", encoding='utf-8') as f:
            htmlfile_after = f.read()
            for k, values in mean_res.items():
                for index, value in enumerate(values):
                    if index >= 20 and index != 20 or index != 21 or index != 22:
                        old = "S" + str(index + 1) + k
                        if not np.isnan(value) and value > 0.0:
                            new = str(round(value, 1))
                        else:
                            new = "N/A"
                        htmlfile_after = htmlfile_after.replace(old, new)
            out_after_path = os.path.join(temp_dir, "ctp-after.png")
            imgkit.from_string(htmlfile_after, os.path.join(temp_dir, out_after_path), options={"width": 890})
        return [out_before_path, out_after_path]
