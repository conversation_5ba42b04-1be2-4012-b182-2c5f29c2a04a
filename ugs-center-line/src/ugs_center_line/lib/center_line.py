from typing import Tuple, List
import SimpleITK as sitk
import argparse
import usia.pyitk as pyitk
import numpy as np
from lib.logger import MyLogger

from lib.ctp import CtpUtils

log = MyLogger()


######函数说明###########
def extract_symmetry(img: sitk.Image, mode=0):
    """
    Extract the symmetry of a CT Volume
    Args:
        img:  The CT volume import as a sitkImage
        mode: The centerline mode. 0 for index coordinate, 1 for physical coordinate ignore image origin, 2 for
    physical coordinate consider image origin
 
    Returns:
        lines: Centerlines of each slice. [[[x00,y00],[x01.y01]],[[x10,y10],[x11.y11]], ... ,[[xn0,yn0],[xn1.yn1]]]
        success: If extract centerlines successfully!
    """
    partition_label = CtpUtils.partition_brain(img)
    itk_img = CtpUtils.sitk_to_itk_image(partition_label, np.uint8)
    # points = pyitk.GetPointsFromLabelImage(itk_img, [33, 34, 31], [26, 27, 24])
    center, normal, success = CtpUtils.extract_brain_symmetry_plane_with_svm(partition_label, 0.1)
    if success:
        lines = pyitk.LinesFromPlaneOnImage(itk_img, center, normal, 0)
        return lines, True
    else:
        #raise Exception("Extract symmetry failed!")
        return None, False


def apply_window(image, window_width, window_level):
    min_window = window_level - window_width // 2
    max_window = window_level + window_width // 2
    windowed_image = np.clip(image, min_window, max_window) # 限制值的范围
    windowed_image = (windowed_image - min_window) / (max_window - min_window) # 归一化
    windowed_image = (windowed_image * 255).astype(np.uint8) # 转换为8位图像
    return windowed_image


def get_center_line(dicom_dir) -> Tuple[List[List[Tuple[float, float]]], bool]:
    #读取序列
    reader = sitk.ImageSeriesReader()
    dicom_series = reader.GetGDCMSeriesFileNames(dicom_dir)
    
    # 获取第一张和最后一张图片的位置信息
    first_image = sitk.ReadImage(dicom_series[0])
    last_image = sitk.ReadImage(dicom_series[-1])
    # first_z = float(first_image.GetMetaData('0020|1041'))  # Image Position (Patient) Z
    # last_z = float(last_image.GetMetaData('0020|1041'))
    first_instance_number = first_image.GetMetaData('0020|0013')
    last_instance_number = last_image.GetMetaData('0020|0013')
    
    reader.SetFileNames(dicom_series)
    img = reader.Execute()
    #提取对称轴
    lines, success = extract_symmetry(img, 1)
    
    if success and first_instance_number > last_instance_number:
        log.info("CenterLine > reverse lines")
        lines = lines[::-1]
        
    return lines, success
 
 
###########调用方法###########

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("-i", "--input_dir", help="Input image dir!", required=True)
    parser.add_argument("-o", "--output_dir", help="Output dir!")
    args = parser.parse_args()

    image_dir = args.input_dir
    output_dir = args.output_dir
    
    #读取序列
    reader = sitk.ImageSeriesReader()
    dicom_series = reader.GetGDCMSeriesFileNames(image_dir)
    reader.SetFileNames(dicom_series)
    img = reader.Execute()
 
    #提取对称轴
    lines,success = extract_symmetry(img, 1)
