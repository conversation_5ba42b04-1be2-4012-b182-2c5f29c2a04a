#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : consumer.py
@Project : uguard_center_line
<AUTHOR> mingxing
@Date    : 2022/9/15 16:26
"""

import json
import os
import sys
import traceback
import time
import pika
import socket

import usia.pyitk as pyitk
sys.path.append(os.path.dirname(__file__))

from lib.common import RabbitProducer
from lib.const import Config
from lib.logger import MyLogger
from lib.run import handle_center_line
import package_info as pi

log = MyLogger()


def callback(ch, method, properties, body):
    """
    消息监听回调

    :param ch: 回调参数channel
    :param method: 回调参数method
    :param properties: 回调参数properties
    :param body: 消息体
    :return:
    """
    log.info("CenterLine > receive message: {}".format(body.decode("utf-8")))
    body = json.loads(body)
    toshiba = body.get("toshiba", False)  # 东芝标记
    study_instance_uid = body.get("studyInstanceUID")
    series = body.get("seriesInstanceUIDs") if toshiba else body.get("seriesInstanceUID")
    algorithm_type = body.get("algorithmType", "")

    resourceData = body.get("resourceData")
    gpu_id = 0
    # 指定GPU
    if resourceData and isinstance(resourceData, dict):
        try:
            resourceId = resourceData.get("resourceId")
            if resourceId:
                gpu_id = int(resourceId)
        except:
            gpu_id = 0

    content = dict(image_series=series, studyInstanceUID=study_instance_uid, algorithmType=algorithm_type,
                   toshiba=toshiba, percent=5)
    content["seriesInstanceUIDs" if toshiba else "seriesInstanceUID"] = series
    time.sleep(1)       # 等待1秒，防止发送消息太快导致前端接收不到
    try:
        RabbitProducer.send(content)
        res = handle_center_line(ch, method, study_instance_uid, series, algorithm_type, toshiba=toshiba, gpu_id=gpu_id)
        log.info("CenterLine [study: {}, series: {}]> toshiba:{}, handle {}".format(
            study_instance_uid, series, toshiba, ("successfully" if res else "failed")))
    except:
        log.error(traceback.format_exc())
    finally:
        task_type = body.get("taskType", "1")
        log.debug("CenterLine > task type: {}".format(task_type))
        # 串行
        if task_type == "1":
            # 串行任务，结束后需要告知转发任务着任务已结束
            confirmation = {"is_working": False}
            RabbitProducer.send(confirmation, queue_name=RabbitProducer.ALGORITHM_STATUS)
            log.info("CenterLine > {}[{}] done".format(("Study" if toshiba else "Series"),
                                                (study_instance_uid if toshiba else series)))


def main():
    queue_name = "algorithm_center_line_task"
    try:
        version_info = dict(version=Config.SERVICE_VERSION, serverType=Config.SERVICE_NAME)
        RabbitProducer.send(version_info)
    except:
        log.error(f"update version error: {traceback.format_exc()}")
    while True:
        try:
            connection = pika.BlockingConnection(
                pika.ConnectionParameters(Config.MQ_HOST, Config.MQ_PORT, "/",
                                          pika.PlainCredentials(Config.MQ_USERNAME, Config.MQ_PASSWORD),
                                          heartbeat=0
                                          ))
            conn_socket = connection._impl.socket
            conn_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            channel = connection.channel()
            channel.queue_declare(queue=queue_name, durable=True)
            channel.basic_consume(callback, queue=queue_name, no_ack=False)
            log.info("waiting for message to exit press CTRL+C, {}:{}({})".format(
                Config.SERVICE_NAME, Config.SERVICE_VERSION, pi.version))
            channel.start_consuming()
        except:
            log.error(f"center line connection error: {traceback.format_exc()}")
        log.info(f"center line connection retry, please check connection")
        time.sleep(10)


if __name__ == "__main__":
    main()
