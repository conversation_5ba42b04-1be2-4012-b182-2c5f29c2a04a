import argparse
import SimpleIT<PERSON> as sitk

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("-i", "--input_file", help="File of image to extract ventricle!", required=True)
    parser.add_argument("-o", "--output_file", help="Output file!", required=True)
    parser.add_argument("-g", "--gpu", type=int ,help="GPU id", default=0)
    args = parser.parse_args()

    from ich_seg.run import get_ich_model, generate_report_from_dir
    model, spacing_nn, num_pool_per_axis = get_ich_model()
    params = {
        "confidences_thre": [
            0.9946,
            0.9981,
            0.9956,
            0.8867,
            0.90
        ],
        "full_conv": 0,
        "keep_largest_component": 1,
        "m1_m6_fixed": 1,
        "max_brain_length": 180,
        "reverse": 0,
        "thre": 0.96,
        "thre_pc": 0.96,
        "volume_nidus_thre": 0.4,
        "volume_thre": 1
    }
    result = generate_report_from_dir(args.input_file, model, spacing_nn, num_pool_per_axis, params, verbose=False)
    if result['flag']:
        label = result['result']['seg_array']
        label = sitk.GetImageFromArray(label)
        label.CopyInformation(sitk.ReadImage(args.input_file))
        sitk.WriteImage(label,args.output_file)
    else:
        print("Extract ICH failed!")
