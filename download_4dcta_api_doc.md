# 下载4D CTA接口文档

## 接口概述

新增的下载4D CTA接口用于检查和下载4D_CTA目录的压缩文件。该接口参照"获取算法信息"接口的设计，提供类似的响应结构。

## 接口信息

- **URL**: `/api/v1/studies/<str:study_instance_uid>/download_4dcta/`
- **方法**: `GET`
- **认证**: 需要认证（使用现有的认证机制）

## 请求参数

### URL参数
- `study_instance_uid` (string, 必需): 检查实例UID

### 查询参数
- `series_instance_uid` (string, 必需): 序列实例UID

## 响应格式

### 成功响应 (HTTP 200)

#### 当4D_CTA目录存在时：
```json
{
    "status": true,
    "code": 200,
    "message": "成功获取4D CTA下载信息",
    "data": {
        "ctp": {
            "4d_ctp": true,
            "download_url": "/ctp/<study_instance_uid>/<series_instance_uid>/4D_CTA/<zip_filename>"
        }
    }
}
```

#### 当4D_CTA目录不存在时：
```json
{
    "status": true,
    "code": 200,
    "message": "4D_CTA目录不存在",
    "data": {
        "ctp": {
            "4d_ctp": false
        }
    }
}
```

### 错误响应

#### 缺少必需参数 (HTTP 200)
```json
{
    "status": false,
    "code": 200,
    "message": "缺少必需参数: series_instance_uid",
    "data": null
}
```

#### 认证失败 (HTTP 200)
```json
{
    "status": false,
    "code": 413,
    "message": "操作验证失败"
}
```

#### 服务器错误 (HTTP 200)
```json
{
    "status": false,
    "code": 200,
    "message": "下载4D CTA时发生错误: <错误详情>"
}
```

## 接口逻辑

1. **验证用户权限**: 使用现有的认证机制验证用户权限
2. **参数验证**: 检查必需的`series_instance_uid`参数
3. **目录检查**: 检查4D_CTA目录是否存在
   - 目录路径: `{DOCKER_DATA_BASE_DIR}/ctp/{study_instance_uid}/{series_instance_uid}/4D_CTA`
4. **文件处理**:
   - 如果目录不存在，返回`4d_ctp: false`
   - 如果目录存在：
     - 检查是否已有对应的zip文件
     - 如果没有zip文件，创建新的zip文件
     - 返回下载URL
5. **响应构建**: 构建符合规范的响应数据

## 文件命名规则

- **zip文件名**: `{study_instance_uid}_{series_instance_uid}_4D_CTA.zip`
- **存储路径**: `{DOCKER_DATA_BASE_DIR}/ctp/zip/`
- **下载URL**: `/ctp/{study_instance_uid}/{series_instance_uid}/4D_CTA/{zip_filename}`

## 与"获取算法信息"接口的对比

| 字段 | 获取算法信息接口 | 下载4D CTA接口 |
|------|------------------|----------------|
| `4d_ctp` | ✓ | ✓ |
| `restart_info` | ✓ | ✗ |
| `download_url` | ✗ | ✓ |

## 使用示例

### Python示例
```python
import requests

# 请求参数
study_uid = "*******.*******.9"
series_uid = "*******.*******.10"
url = f"http://your-server:4224/api/v1/studies/{study_uid}/download_4dcta/"
params = {"series_instance_uid": series_uid}

# 发送请求
response = requests.get(url, params=params)
data = response.json()

# 处理响应
if data["status"] and data["data"]["ctp"]["4d_ctp"]:
    download_url = data["data"]["ctp"]["download_url"]
    print(f"下载URL: {download_url}")
else:
    print("4D_CTA目录不存在")
```

### JavaScript示例
```javascript
const studyUid = "*******.*******.9";
const seriesUid = "*******.*******.10";
const url = `http://your-server:4224/api/v1/studies/${studyUid}/download_4dcta/`;
const params = new URLSearchParams({series_instance_uid: seriesUid});

fetch(`${url}?${params}`)
    .then(response => response.json())
    .then(data => {
        if (data.status && data.data.ctp["4d_ctp"]) {
            const downloadUrl = data.data.ctp.download_url;
            console.log("下载URL:", downloadUrl);
        } else {
            console.log("4D_CTA目录不存在");
        }
    })
    .catch(error => console.error("请求失败:", error));
```

## 注意事项

1. **权限控制**: 接口使用与其他接口相同的认证机制
2. **文件管理**: zip文件会被缓存，重复请求会直接返回已存在的zip文件
3. **错误处理**: 所有错误都会被捕获并返回适当的错误信息
4. **日志记录**: 关键操作都会记录到日志中，便于调试和监控
5. **目录结构**: zip文件内保持原始的目录结构
6. **性能考虑**: 大文件的压缩可能需要一些时间，建议前端显示加载状态

## 部署说明

1. 确保`DOCKER_DATA_BASE_DIR`和`ALGORITHM_TYPE_CTP`配置正确
2. 确保zip存储目录有足够的磁盘空间
3. 确保Web服务器能够提供静态文件下载服务
4. 建议配置适当的文件清理策略，定期清理旧的zip文件

## 测试

使用提供的测试脚本`test_download_4dcta_api.py`进行接口测试：

```bash
python test_download_4dcta_api.py
```

测试脚本会验证：
- zip文件创建功能
- API响应结构
- 实际接口调用（需要服务运行）
