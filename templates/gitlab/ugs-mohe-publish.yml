variables:
  RELEASE_BRANCH:
    value: ""
    description: "请输入当前要发布的分支名，必须以release开头，例：release-1.8.1"
  VERSION_B:
    value: ""
    description: "请输入当前发布日期，例：230524_1"
  GE_REPOSITORY:
    value: "************:808"
    description: "GE公共仓库地址，例：************:808"
default:
  image: *************:808/ugs-ci/docker:230718
  tags:
    - k8s-runner
publish-mohe:
  stage: deploy
  timeout: 3 hours
  environment:
    name: staging
  rules:
    - if: $CI_PIPELINE_SOURCE == "web"
  script:
    - |
      ls -lart
      echo "releaseBranch:${RELEASE_BRANCH}, versionB:${VERSION_B}, GeRepository:${GE_REPOSITORY}"
      package_version=$(echo ${RELEASE_BRANCH} | awk -F '-' '{print $2}')
      mkdir ../temp && cd ../temp
      work_dir=$(pwd)  
      echo "----------------------=clone=----------------------"  
      git clone -b ${RELEASE_BRANCH} http://ugs-api-token:${UGS_API_TOKEN}@************/uguard-stroke/ugs-mohe-release.git     
      git clone -b ${RELEASE_BRANCH} http://ugs-api-token:${UGS_API_TOKEN}@************/uguard-stroke/ugs-mohe.git 
      echo "ugs-mohe clone successfully"
      cd ugs-mohe-release
      ls -lh 
      echo "----------------------=package=----------------------"  
      cat package.properties
      echo "----------------------=image=----------------------"  
      services=('api' 'transport' 'result' 'delete' 'frontend' 'aspects' 'ctp' 'cta')  
      for service in ${services[@]}; do
        service_image=$(grep $service package.properties | cut -d'=' -f2)
        echo "source image:${service_image}"
        docker pull $service_image
        # PROD
        prod_service_image=$(echo "${service_image}" | sed "s/${DEV_HARBOR_HOST}/${PROD_HARBOR_HOST}/")
        docker tag $service_image $prod_service_image
        docker login -u $DOCKER_REGISTRY_USERNAME -p $DOCKER_REGISTRY_PASSWORD ${PROD_HARBOR_HOST}
        docker push $prod_service_image
        echo "${prod_service_image} pushed ✅"
        # GE
        ge_service_image=$(echo "${service_image}" | sed "s/${DEV_HARBOR_HOST}/${GE_REPOSITORY}/")
        docker tag $service_image $ge_service_image
        docker login -u $DOCKER_REGISTRY_USERNAME -p $DOCKER_REGISTRY_PASSWORD ${GE_REPOSITORY}
        docker push $ge_service_image
        echo "${ge_service_image} pushed ✅"
        sleep 10
        sed -r -i "s#(${service}=).*#\1${ge_service_image}#" package.properties
        # Update YML
        yaml_file="${work_dir}/ugs-mohe/templates/ugs-${service}.yaml"
        old_image=$(grep "ge-k8s/ugs-${service}" $yaml_file | awk -F ' ' '{print $2}')
        echo "old image: ${old_image}, new image:${ge_service_image}"
        sed -r -i "s#${old_image}#${ge_service_image}#" $yaml_file
        echo "****${yaml_file}****"
        cat $yaml_file
        echo "************************************************"
        docker rmi $service_image
        docker rmi $prod_service_image
        docker rmi $ge_service_image
      done;
      cat package.properties
      echo "----------------------=helm=----------------------"
      cd $work_dir/ugs-mohe
      ls -lh 
      zip_name=ugs-mohe-${package_version}.${VERSION_B}.zip
      zip -rq $zip_name templates Chart.yaml README.md
      PACKAGE_REGISTRY_URL="${CI_BASE_URL}/api/v4/projects/411/packages/generic/ugs-mohe/${package_version}"
      curl -H "PRIVATE-TOKEN: ${UGS_API_TOKEN}" -T $zip_name ${PACKAGE_REGISTRY_URL}/$zip_name
      echo -e "\n${zip_name} archive successfully!"
      echo "${zip_name} pushed ✅"
      echo "----------------------=info=----------------------"    
      cd $work_dir/ugs-mohe-release
      sed -r -i "s/(UGuard:).*/\1${RELEASE_BRANCH}_${VERSION_B}/" version.txt      
      cat version.txt
      sed -r -i "s/(ugs-version=).*/\1${package_version}/" package.properties
      sed -r -i "s/(ugs-mohe=).*/\1${zip_name}/" package.properties
      cat package.properties
      echo "----------------------=commit=----------------------"    
      git config --global user.email "ugs-api-token"
      git config --global user.name "${UGS_API_TOKEN}"
      git add .
      git commit -m "publish ${RELEASE_BRANCH}"
      git push -u origin ${RELEASE_BRANCH}
      echo "publish ${RELEASE_BRANCH} successfully"
