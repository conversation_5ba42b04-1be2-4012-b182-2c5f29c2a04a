variables:
  RELEASE_BRANCH:
    value: ""
    description: "请输入当前要发布的分支名，必须以release开头，例：release-1.8.1"
  VERSION_B:
    value: ""
    description: "请输入当前发布日期，例：230524_1"
default:
  image: *************:808/ugs-ci/ubuntu18_py36:230509
  tags:
    - k8s-runner
publish-upgrade:
  stage: deploy
  environment:
    name: staging
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $VERSION_B
  script:
    - |
      ls -lart
      echo "releaseBranch:${RELEASE_BRANCH}, versionB:${VERSION_B}"
      package_version=$(echo ${RELEASE_BRANCH} | awk -F '-' '{print $4}')
      mkdir ../temp && cd ../temp
      work_dir=$(pwd)  
      echo "----------------------=clone=----------------------"  
      git clone -b ${RELEASE_BRANCH} http://ugs-api-token:${UGS_API_TOKEN}@************/uguard-stroke/ugs-upgrade-release.git     
      git clone -b ${RELEASE_BRANCH} http://ugs-api-token:${UGS_API_TOKEN}@************/uguard-stroke/ugs-upgrade.git 
      echo "ugs-upgrade clone successfully"
      cd ugs-upgrade-release
      ls -alrt
      cp -rf $work_dir/ugs-upgrade/* .
      ls -alrt
      echo "----------------------=info=----------------------"    
      cd $work_dir/ugs-upgrade-release
      sed -r -i "s/(UGuard:).*/\1${RELEASE_BRANCH}_${VERSION_B}/" version.txt      
      cat version.txt
      sed -r -i "s/(ugs-version=).*/\1${package_version}/" package.properties
      cat package.properties      
      echo "----------------------=commit=----------------------"    
      git config --global user.email "ugs-api-token"
      git config --global user.name "${UGS_API_TOKEN}"
      git add .
      git commit -m "publish ${RELEASE_BRANCH}"
      git push -u origin ${RELEASE_BRANCH}
      echo "publish ${RELEASE_BRANCH} successfully"
