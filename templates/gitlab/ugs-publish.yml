variables:
  RELEASE_BRANCH:
    value: ""
    description: "请输入当前要发布的分支名，必须以release开头，例：release-1.7.3"
  VERSION_B:
    value: ""
    description: "请输入当前发布日期，例：230524_1"
default:
  image: *************:808/ugs-ci/ubuntu18_py36:230509
  tags:
    - k8s-runner
publish:
  stage: deploy
  environment:
    name: staging
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_BRANCH == "publish" && $RELEASE_BRANCH =~ /^release.*/ && $VERSION_B
  script:
    - |
      declare -A SERVICES
      declare -A VersionDict
      SERVICES=( [ugs-frontend]='113' [ugs-api]='36' [ugs-transport]='37' [ugs-result]='38' [ugs-delete]='41' [ugs-aspects]='32' [ugs-ctp]='30' [ugs-cta]='31' [ctp4d]='564' [ugs-center-line]='512'  )
      PACKAGE_VERSION=$(echo ${RELEASE_BRANCH} | awk -F '-' '{print $2}')
      for serviceName in ${!SERVICES[@]}; do
        projectId=${SERVICES[${serviceName}]}
        echo "serviceName: ${serviceName}, projectId: ${SERVICES[${serviceName}]}. "
        # 根据分支遍历仓库下的项目列表，拿到package_id
        packageUrl="${CI_BASE_URL}/api/v4/projects/${projectId}/packages?per_page=100&page=1"
        packageResult=$(curl -H "PRIVATE-TOKEN: ${UGS_API_TOKEN}" "${packageUrl}")
        packageId=$(echo $packageResult | python3 -c "import sys,json;package_id_list=[data.get('id') for data in (json.load(sys.stdin)) if data.get('name') == '"${serviceName}"' and data.get('version') == '"${PACKAGE_VERSION}"'];print(package_id_list[0] if package_id_list else -1)")
        echo "packageUrl: ${packageUrl}; packageResult: ${packageResult}; packageId: ${packageId}"
        if [ "${packageId}" == "-1" ]; then
          echo "service:${serviceName}, version:${PACKAGE_VERSION}, package not found"
          continue  				
        fi
        # 根据package_id拿到所有的包列表，找到时间最大的包名即可
        packageListUrl="${CI_BASE_URL}/api/v4/projects/${projectId}/packages/${packageId}/package_files?per_page=100&page=1"
        packageListResult=$(curl -H "PRIVATE-TOKEN: ${UGS_API_TOKEN}" "${packageListUrl}")
        package_file_name=$(echo $packageListResult | python3 -c "import sys,json; time_file_map={data.get('created_at'): data.get('file_name') for data in (json.load(sys.stdin))} ;print(time_file_map.get(max(time_file_map), -1) if time_file_map else -1)")
        echo "packageListUrl: ${packageListUrl}; packageListResult:${packageListResult}; package_file_name: ${package_file_name}"
        if [ "${package_file_name}" == "-1" ]; then
          echo "service:${serviceName}, version:${PACKAGE_VERSION}, filename not found"
          continue  				
        fi        
        # 下载包
        downloadUrl="${CI_BASE_URL}/api/v4/projects/${projectId}/packages/generic/${serviceName}/${PACKAGE_VERSION}/${package_file_name}"
        echo "downloadUrl: ${downloadUrl}, package_file_name: ${package_file_name}"
        wget --no-check-certificate --quiet --method GET --header "PRIVATE-TOKEN: ${UGS_API_TOKEN}" "${downloadUrl}"
        # 上传包
        uploadUrl="${CI_BASE_URL}/api/v4/projects/${UGS_RELEASE_ID}/packages/generic/ugs/${PACKAGE_VERSION}/${package_file_name}"
        echo "uploadUrl: ${uploadUrl}"
        curl -H "PRIVATE-TOKEN: ${UGS_API_TOKEN}" -T ${package_file_name} "${uploadUrl}"
        echo "${RELEASE_BRANCH} upload package to release successfully!"
        VersionDict["${serviceName}"]=${package_file_name}
      done

      # 查看分支是否存在
      CODE=`curl -s -o /dev/null -w "%{http_code}"  --header "PRIVATE-TOKEN: ${UGS_API_TOKEN}" "${CI_BASE_URL}/api/v4/projects/${UGS_RELEASE_ID}/repository/branches/${RELEASE_BRANCH}"`
      # if  [ "$CODE" == "404" ]; then
      #   echo ""
      # fi
      cd ..
      BASE_DIR=$(pwd)   
      git clone -b ${RELEASE_BRANCH} http://ugs-api-token:${UGS_API_TOKEN}@172.16.1.238/uguard-stroke/ugs-release.git
      
      cd ugs-release
      ls -alrt 

      # 打印服务及包名
      for service in ${!VersionDict[@]}; do
        sed -r -i "s/(${service}=).*/\1${VersionDict[${service}]}/" package.properties        
      done

      sed -r -i "s/(ugs-version=).*/\1${PACKAGE_VERSION}/" package.properties
      sed -r -i "s/(UGuard:).*/\1${RELEASE_BRANCH}_${VERSION_B}/" version.txt
      cat package.properties
      cat version.txt
      echo "satrt commit and push ."
      # 提交并push分支
      git config --global user.email "ugs-api-token"
      git config --global user.name "${UGS_API_TOKEN}"
      echo "config"
      git add .
      echo "add"
      git commit -m "modify branch ${PACKAGE_VERSION} in package.properties version.txt"
      echo "commit"
      git push origin ${RELEASE_BRANCH}:${RELEASE_BRANCH} -f 
    - echo "post finish ."
