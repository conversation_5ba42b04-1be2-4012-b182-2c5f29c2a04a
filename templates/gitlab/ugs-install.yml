variables:
  DEVICE_HOST:
    value: ""
    description: "请输入服务器的主机地址，例如：************"
  DEVICE_USER:
    value: ""
    description: "请输入服务器的登录用户名，例如：unionstrong"
  DEVICE_PASS:
    value: ""
    description: "请输入服务器的登录密码"
default:
  image: *************:808/uguard/ugs-ubuntu:18.04
  tags:
    - k8s-runner
deploy-job:
  stage: deploy
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $DEVICE_HOST && $DEVICE_USER && $DEVICE_PASS
  script:
    - ls -alrt
    - bash install.sh
    - echo "${DEVICE_HOST} deploy ${CI_COMMIT_BRANCH} successfully!"
