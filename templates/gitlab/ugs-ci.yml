default:
  image: *************:808/ugs-ci/ubuntu18_py36:230509
  tags:
    - k8s-runner
  before_script:
    - DEST_DIR="${APP_DIR}/${CI_PROJECT_NAME}"
    - echo "host:${UGS_HOST_IP}, dir:${DEST_DIR}"
    - CUR_DIR=$(pwd)
    - ls -alrt
    - PACKAGE_VERSION=$(echo ${CI_COMMIT_BRANCH} | awk -F '-' '{print $2}')
    - echo "package version:${PACKAGE_VERSION}"
variables:
  APP_DIR: "/home/<USER>"
  UGS_HOST_IP:
    value: "************"
    description: "请选择部署的目标服务器， 默认：************"
    options:
      - "************"
      - "*************"
      - "************"
      - "************"
      - "************"
  VERSION_B:
    value: ""
    description: "请输入构建日期，例：230525_1"
build:
  stage: build
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_BRANCH =~ /^release.*/ && $VERSION_B
  script:
    - |
      python3 setup.py bdist_wheel
      echo "${CI_COMMIT_BRANCH} compiled successfully!"
      mv prod.yml docker-compose.yml
      if [ -f extension.yml ]; then mv extension.yml docker-compose.override.yml; fi
      ZIP_NAME=${CI_PROJECT_NAME}-${PACKAGE_VERSION}.${VERSION_B}
      echo "${ZIP_NAME}" > version
      zip -r ${ZIP_NAME}.zip dist/ volume/ .env docker-compose*.yml version
      echo "${CI_COMMIT_BRANCH} packaged successfully!"
      ls -lrt
      PACKAGE_REGISTRY_URL="${CI_BASE_URL}/api/v4/projects/${CI_PROJECT_ID}/packages/generic/${CI_PROJECT_NAME}/${PACKAGE_VERSION}"
      curl -H "PRIVATE-TOKEN: ${UGS_API_TOKEN}" -T ${ZIP_NAME}.zip ${PACKAGE_REGISTRY_URL}/${ZIP_NAME}.zip
      echo -e "\n${CI_COMMIT_BRANCH} archive successfully!"
  artifacts:
    name: "${CI_PROJECT_NAME}_${CI_COMMIT_REF_SLUG}"
    expire_in: 1 hrs
    paths:
      - "*.zip"
deploy-dev:
  stage: deploy
  environment:
    name: dev
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_BRANCH =~ /^(feature|fix|develop).*/
  script:
    - |
      rm -rf .git .gitignore .gitlab-ci.yml
      mv dev.yml docker-compose.yml      
      DO_SSHPASS="sshpass -p${UGS_HOST_PASS}"
      DO_SSH="${DO_SSHPASS} ssh -o StrictHostKeyChecking=no ${UGS_HOST_USER}@${UGS_HOST_IP}"
      ${DO_SSH} sudo rm -rf ${DEST_DIR}
      ${DO_SSHPASS} scp -r ${CUR_DIR} ${UGS_HOST_USER}@${UGS_HOST_IP}:${DEST_DIR}
      ${DO_SSH} "cd ${DEST_DIR};sudo docker-compose rm -fs;sudo docker-compose up --build  -d"
      echo "${CI_COMMIT_BRANCH} deployed successfully!"
deploy-test:
  stage: deploy
  environment:
    name: test
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_BRANCH =~ /^test.*/
  script:
    - echo "host:${UGS_HOST_IP}, dir:${DEST_DIR}"
    - ls -alrt
    - |
      rm -rf .git .gitignore .gitlab-ci.yml
      mv dev.yml docker-compose.yml
      DO_SSHPASS="sshpass -p${UGS_HOST_PASS}"
      DO_SSH="${DO_SSHPASS} ssh -o StrictHostKeyChecking=no ${UGS_HOST_USER}@${UGS_HOST_IP}"
      ${DO_SSH} sudo rm -rf ${DEST_DIR}
      ${DO_SSHPASS} scp -r ${CUR_DIR} ${UGS_HOST_USER}@${UGS_HOST_IP}:${DEST_DIR}
      ${DO_SSH} "cd ${DEST_DIR};sudo docker-compose rm -fs;sudo docker-compose up --build  -d"
      echo "${CI_COMMIT_BRANCH} deployed successfully!"
deploy-release:
  stage: deploy
  environment:
    name: staging
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_BRANCH =~ /^release.*/ && $VERSION_B
  script:
    - |
      echo "host:${UGS_HOST_IP}, dir:${DEST_DIR}"
      ZIP_NAME=${CI_PROJECT_NAME}-${PACKAGE_VERSION}.${VERSION_B}
      DO_SSHPASS="sshpass -p${UGS_HOST_PASS}"
      DO_SSH="${DO_SSHPASS} ssh -o StrictHostKeyChecking=no ${UGS_HOST_USER}@${UGS_HOST_IP}"
      ${DO_SSH} "sudo rm -rf ${DEST_DIR};mkdir -p ${DEST_DIR}"
      ${DO_SSHPASS} scp ${ZIP_NAME}.zip ${UGS_HOST_USER}@${UGS_HOST_IP}:${DEST_DIR}
      ${DO_SSH} unzip -qo -d ${DEST_DIR} ${DEST_DIR}/${ZIP_NAME}.zip
      ${DO_SSH} "cd ${DEST_DIR};sudo docker-compose rm -fs;sudo docker-compose up -d"
      echo "${CI_COMMIT_BRANCH} deployed successfully!"
rebuild-db:
  stage: .post
  environment:
    name: staging
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_BRANCH =~ /^release.*/ && $VERSION_B && $CI_PROJECT_NAME== "ugs-api"
  script:
    - |      
      SQL_FILE=${APP_DIR}/ugs.sql      
      LOCAL_SQL=${CUR_DIR}/db/${PACKAGE_VERSION}.sql
      DO_SSHPASS="sshpass -p${UGS_HOST_PASS}"
      DO_SSH="${DO_SSHPASS} ssh -o StrictHostKeyChecking=no ${UGS_HOST_USER}@${UGS_HOST_IP}"
      ${DO_SSH} "sudo rm -rf ${SQL_FILE}"
      ${DO_SSHPASS} scp ${LOCAL_SQL} ${UGS_HOST_USER}@${UGS_HOST_IP}:${SQL_FILE}
      ${DO_SSH} "sudo docker exec -i ugs-mysql mysql -uroot -pUnionStrong@2020 <" ${SQL_FILE}
      echo "database rebuilt successfully!"
