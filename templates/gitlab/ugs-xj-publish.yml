variables:
  RELEASE_BRANCH:
    value: ""
    description: "请输入当前要发布的分支名，必须以release开头，例：release-1.1.3"
  VERSION_B:
    value: ""
    description: "请输入当前发布日期，例：230524_1"
default:
  image: *************:808/ugs-ci/ubuntu18_py36:230509
  tags:
    - k8s-runner
publish-xj:
  stage: deploy
  environment:
    name: staging
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $VERSION_B
  script:
    - |
      ls -lat
      echo "releaseBranch:${RELEASE_BRANCH}, versionB:${VERSION_B}"      
      branch_version=$(echo ${RELEASE_BRANCH} | awk -F '-' '{print $2}')
      mkdir ../temp && cd ../temp
      echo "----------------------=clone=----------------------"  
      git clone -b UGS_XJ_Release_$branch_version http://ugs-api-token:${UGS_API_TOKEN}@************/uguard-stroke/ugs-api.git     
      git clone -b ${RELEASE_BRANCH} http://ugs-api-token:${UGS_API_TOKEN}@************/uguard-stroke/ugs-xj-release.git     
      echo "ugs-xj-release clone successfully"
      cp ugs-api/db/UGS_XJ_Release_$branch_version.sql ugs-xj-release/ugs.sql
      cd ugs-xj-release
      ls -alt
      echo "----------------------=info=----------------------"    
      cat package.properties
      echo "----------------------=package=----------------------"    
      package_version=$(grep ugs-version package.properties | cut -d'=' -f2)
      services=('ugs-frontend' 'ugs-api' 'ugs-transport' 'ugs-result' 'ugs-delete')  
      for service in ${services[@]}; do
        package_name=$(grep $service package.properties | cut -d'=' -f2)
        download_url="${CI_BASE_URL}/api/v4/projects/${UGS_RELEASE_ID}/packages/generic/ugs/${package_version}/${package_name}"
        wget --quiet --method GET --header "PRIVATE-TOKEN: ${UGS_API_TOKEN}" "${download_url}"
        unzip -qo -d $service $package_name
        rm $package_name
        if [ -f "${service}/version" ]; then rm $service/version; fi
        echo "${service} installed ✅"
      done;
      ls -alt
      echo "----------------------=commit=----------------------"    
      git rm package.properties
      git config --global user.email "ugs-api-token"
      git config --global user.name "${UGS_API_TOKEN}"
      git add .
      git commit -m "version ${branch_version} released"
      git push -u origin ${RELEASE_BRANCH}
      echo "publish ${RELEASE_BRANCH} successfully"
