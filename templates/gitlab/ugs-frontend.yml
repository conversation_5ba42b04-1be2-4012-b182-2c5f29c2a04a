default:
  image: *************:808/ugs-ci/ubuntu18_nodejs16:230720
  tags:
    - k8s-runner
  before_script:
    - |
      ls -alt
      CUR_DIR=$(pwd)
      cd local-fronted
      ls -alt
      rm -rf package-lock.json dist*
      package_name=node_modules_1.8.1.tar.gz
      wget --no-check-certificate --quiet --method GET --header "PRIVATE-TOKEN: ${UGS_API_TOKEN}" "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/node_modules/1.8.1/${package_name}"
      tar -zxf $package_name
      install_start=`date +%s`
      npm config set registry https://registry.npmmirror.com/
      npm i --legacy-peer-deps
      build_start=`date +%s`
      npm run build
      build_end=`date +%s`
      echo "----------------------=npm=----------------------"
      echo - npm install in `expr $build_start - $install_start` seconds
      echo - npm build in `expr $build_end - $build_start` seconds
      echo "-------------------------------------------------"
      cd dist/local-frontend
      echo ${CI_COMMIT_BRANCH} > ${CI_COMMIT_BRANCH}.Version
      zip -rq $CUR_DIR/dist.zip .
      cd $CUR_DIR
      echo 'build successfully!'
variables:
  APP_DIR: "/home/<USER>"
  UGS_HOST_IP:
    value: "************"
    description: "development host. Set to '************' by default."
    options:
      - "************"
      - "************"
      - "*************"
      - "************"
      - "************"
  VERSION_B:
    value: ""
    description: "version B. Set to '' by default."
deploy-dev:
  stage: deploy
  environment:
    name: dev
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_BRANCH =~ /^(feature|fix|develop|future).*/
  script:
    - |
      rm -rf .git .gitignore .gitlab-ci.yml
      DEST_DIR="${APP_DIR}/${CI_PROJECT_NAME}"
      DO_SSHPASS="sshpass -p${UGS_HOST_PASS}"
      echo "host:${UGS_HOST_IP}, dir:${DEST_DIR}"
      DO_SSH="${DO_SSHPASS} ssh -o StrictHostKeyChecking=no ${UGS_HOST_USER}@${UGS_HOST_IP}"
      ${DO_SSH} "sudo rm -rf ${DEST_DIR};mkdir -p ${DEST_DIR}"
      ${DO_SSHPASS} scp dist.zip ${UGS_HOST_USER}@${UGS_HOST_IP}:${DEST_DIR}
      ${DO_SSH} sudo unzip -qo -d ${DEST_DIR} ${DEST_DIR}/dist.zip
      ${DO_SSH} "sudo docker restart ugs-nginx"
    - echo "${CI_COMMIT_BRANCH} deployed successfully!"
deploy-test:
  stage: deploy
  environment:
    name: test
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_BRANCH =~ /^test.*/
  script:
    - |
      rm -rf .git .gitignore .gitlab-ci.yml
      DEST_DIR="${APP_DIR}/${CI_PROJECT_NAME}"
      DO_SSHPASS="sshpass -p${UGS_HOST_PASS}"
      echo "host:${UGS_HOST_IP}, dir:${DEST_DIR}"
      DO_SSH="${DO_SSHPASS} ssh -o StrictHostKeyChecking=no ${UGS_HOST_USER}@${UGS_HOST_IP}"
      ${DO_SSH} "sudo rm -rf ${DEST_DIR};mkdir -p ${DEST_DIR}"
      ${DO_SSHPASS} scp dist.zip ${UGS_HOST_USER}@${UGS_HOST_IP}:${DEST_DIR}
      ${DO_SSH} sudo unzip -qo -d ${DEST_DIR} ${DEST_DIR}/dist.zip
      ${DO_SSH} "sudo docker restart ugs-nginx"
    - echo "${CI_COMMIT_BRANCH} deployed successfully!"
deploy-release:
  stage: deploy
  environment:
    name: staging
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_BRANCH =~ /^release.*/ && $VERSION_B
  script:
    - |
      PACKAGE_VERSION=$(echo ${CI_COMMIT_BRANCH} | awk -F '-' '{print $2}')
      PACKAGE_REGISTRY_URL="${CI_BASE_URL}/api/v4/projects/${CI_PROJECT_ID}/packages/generic/${CI_PROJECT_NAME}/${PACKAGE_VERSION}"
      ZIP_NAME=${CI_PROJECT_NAME}-${PACKAGE_VERSION}.${VERSION_B}
      curl -H "PRIVATE-TOKEN: ${UGS_API_TOKEN}" -T dist.zip ${PACKAGE_REGISTRY_URL}/${ZIP_NAME}.zip
      CUR_DIR=$(pwd)
      DEST_DIR="${APP_DIR}/${CI_PROJECT_NAME}"
      echo "host:${UGS_HOST_IP}, dir:${DEST_DIR}"
      DO_SSHPASS="sshpass -p${UGS_HOST_PASS}"
      DO_SSH="${DO_SSHPASS} ssh -o StrictHostKeyChecking=no ${UGS_HOST_USER}@${UGS_HOST_IP}"
      ${DO_SSH} "sudo rm -rf ${DEST_DIR};mkdir -p ${DEST_DIR}"
      ${DO_SSHPASS} scp dist.zip ${UGS_HOST_USER}@${UGS_HOST_IP}:${DEST_DIR}/${ZIP_NAME}.zip
      ${DO_SSH} sudo unzip -qo -d ${DEST_DIR} ${DEST_DIR}/${ZIP_NAME}.zip
      ${DO_SSH} "sudo docker restart ugs-nginx"
    - echo "${CI_COMMIT_BRANCH} deployed successfully!"
