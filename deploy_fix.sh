#!/bin/bash
# 部署修复脚本

set -e

echo "=== UGS-Transport 连接修复部署脚本 ==="

# 配置
CONTAINER_NAME="ugs-transport"
BACKUP_DIR="/tmp/ugs-transport-backup-$(date +%Y%m%d_%H%M%S)"
SOURCE_DIR="./ugs-transport/src/ugs_transport"

echo "1. 检查容器状态..."
if ! sudo docker ps | grep -q $CONTAINER_NAME; then
    echo "错误: $CONTAINER_NAME 容器未运行"
    exit 1
fi

echo "2. 创建备份目录..."
sudo mkdir -p $BACKUP_DIR

echo "3. 备份原始代码..."
sudo docker cp $CONTAINER_NAME:/code/src/ugs_transport/ $BACKUP_DIR/
echo "备份已保存到: $BACKUP_DIR"

echo "4. 停止容器..."
sudo docker stop $CONTAINER_NAME

echo "5. 复制修改后的代码到容器..."
sudo docker cp $SOURCE_DIR/utils/connection_manager.py $CONTAINER_NAME:/code/src/ugs_transport/utils/
sudo docker cp $SOURCE_DIR/utils/health_monitor.py $CONTAINER_NAME:/code/src/ugs_transport/utils/
sudo docker cp $SOURCE_DIR/consumer.py $CONTAINER_NAME:/code/src/ugs_transport/

echo "6. 启动容器..."
sudo docker start $CONTAINER_NAME

echo "7. 等待容器启动..."
sleep 10

echo "8. 检查容器状态..."
if sudo docker ps | grep -q $CONTAINER_NAME; then
    echo "✓ 容器启动成功"
else
    echo "✗ 容器启动失败，正在恢复备份..."
    
    # 恢复备份
    sudo docker stop $CONTAINER_NAME 2>/dev/null || true
    sudo docker cp $BACKUP_DIR/ugs_transport/ $CONTAINER_NAME:/code/src/
    sudo docker start $CONTAINER_NAME
    
    echo "已恢复原始代码"
    exit 1
fi

echo "9. 检查服务日志..."
echo "最近的日志:"
sudo docker logs $CONTAINER_NAME --tail 20

echo ""
echo "=== 部署完成 ==="
echo "备份位置: $BACKUP_DIR"
echo "请监控日志以确认服务正常运行:"
echo "sudo docker logs -f $CONTAINER_NAME"
echo ""
echo "如需回滚，请运行:"
echo "sudo docker stop $CONTAINER_NAME"
echo "sudo docker cp $BACKUP_DIR/ugs_transport/ $CONTAINER_NAME:/code/src/"
echo "sudo docker start $CONTAINER_NAME"
