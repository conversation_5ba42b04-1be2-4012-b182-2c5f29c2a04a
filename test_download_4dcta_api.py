#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试下载4D CTA接口的脚本
"""

import requests
import json
import os
import tempfile
import zipfile
from unittest.mock import patch, MagicMock

def test_download_4dcta_api():
    """测试下载4D CTA接口"""
    
    # 测试参数
    base_url = "http://localhost:4224"  # 根据实际部署调整
    study_instance_uid = "test_study_123"
    series_instance_uid = "test_series_456"
    
    # 构建请求URL
    url = f"{base_url}/api/v1/studies/{study_instance_uid}/download_4dcta/"
    params = {
        "series_instance_uid": series_instance_uid
    }
    
    # 添加认证头（根据实际认证方式调整）
    headers = {
        "Content-Type": "application/json",
        # "Authorization": "Bearer your_token_here"  # 如果需要认证
    }
    
    print(f"测试URL: {url}")
    print(f"参数: {params}")
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 检查响应结构
                if "data" in data and "ctp" in data["data"]:
                    ctp_data = data["data"]["ctp"]
                    
                    if ctp_data.get("4d_ctp") is True:
                        print("✓ 4D_CTA目录存在")
                        
                        if "download_url" in ctp_data:
                            download_url = ctp_data["download_url"]
                            print(f"✓ 下载URL: {download_url}")
                            
                            # 验证URL格式
                            expected_pattern = f"/ctp/{study_instance_uid}/{series_instance_uid}/4D_CTA/"
                            if expected_pattern in download_url:
                                print("✓ 下载URL格式正确")
                            else:
                                print(f"✗ 下载URL格式错误，期望包含: {expected_pattern}")
                        else:
                            print("✗ 响应中缺少download_url字段")
                    
                    elif ctp_data.get("4d_ctp") is False:
                        print("✓ 4D_CTA目录不存在（正常情况）")
                        
                        if "download_url" not in ctp_data:
                            print("✓ 目录不存在时正确地没有返回download_url")
                        else:
                            print("✗ 目录不存在时不应该返回download_url")
                    
                    else:
                        print("✗ 4d_ctp字段值异常")
                
                else:
                    print("✗ 响应数据结构异常")
                    
            except json.JSONDecodeError as e:
                print(f"✗ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        
        else:
            print(f"✗ 请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"✗ 请求异常: {e}")

def test_zip_creation():
    """测试zip文件创建功能"""
    print("\n=== 测试zip文件创建功能 ===")
    
    # 创建临时测试目录和文件
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试文件
        test_files = [
            "test1.txt",
            "subdir/test2.txt",
            "subdir/nested/test3.txt"
        ]
        
        for file_path in test_files:
            full_path = os.path.join(temp_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            with open(full_path, 'w') as f:
                f.write(f"Test content for {file_path}")
        
        # 创建zip文件
        zip_path = os.path.join(temp_dir, "test.zip")
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        if file.endswith('.zip'):
                            continue  # 跳过zip文件本身
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, temp_dir)
                        zipf.write(file_path, arcname)
                        print(f"添加文件: {file_path} -> {arcname}")
            
            # 验证zip文件
            if os.path.exists(zip_path):
                print(f"✓ zip文件创建成功: {zip_path}")
                
                # 检查zip文件内容
                with zipfile.ZipFile(zip_path, 'r') as zipf:
                    file_list = zipf.namelist()
                    print(f"✓ zip文件包含 {len(file_list)} 个文件:")
                    for file_name in file_list:
                        print(f"  - {file_name}")
                        
                    # 验证文件内容
                    for file_name in file_list:
                        content = zipf.read(file_name).decode('utf-8')
                        expected_content = f"Test content for {file_name}"
                        if content == expected_content:
                            print(f"✓ 文件内容正确: {file_name}")
                        else:
                            print(f"✗ 文件内容错误: {file_name}")
            else:
                print("✗ zip文件创建失败")
                
        except Exception as e:
            print(f"✗ zip创建过程中发生错误: {e}")

def test_api_structure():
    """测试API响应结构"""
    print("\n=== 测试API响应结构 ===")
    
    # 模拟不同的响应情况
    test_cases = [
        {
            "name": "4D_CTA目录存在",
            "4d_ctp_exists": True,
            "expected_fields": ["4d_ctp", "download_url"]
        },
        {
            "name": "4D_CTA目录不存在", 
            "4d_ctp_exists": False,
            "expected_fields": ["4d_ctp"]
        }
    ]
    
    for case in test_cases:
        print(f"\n测试用例: {case['name']}")
        
        # 构建模拟响应
        if case["4d_ctp_exists"]:
            ctp_data = {
                "4d_ctp": True,
                "download_url": "/ctp/test_study/test_series/4D_CTA/test.zip"
            }
        else:
            ctp_data = {
                "4d_ctp": False
            }
        
        response_data = {
            "status": True,
            "code": 200,
            "message": "成功",
            "data": {
                "ctp": ctp_data
            }
        }
        
        # 验证响应结构
        print(f"模拟响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        
        # 检查必需字段
        for field in case["expected_fields"]:
            if field in ctp_data:
                print(f"✓ 包含必需字段: {field}")
            else:
                print(f"✗ 缺少必需字段: {field}")
        
        # 检查不应该存在的字段
        if case["4d_ctp_exists"]:
            if "restart_info" not in ctp_data:
                print("✓ 正确地没有包含restart_info字段")
            else:
                print("✗ 不应该包含restart_info字段")

if __name__ == "__main__":
    print("=== 开始测试下载4D CTA接口 ===")
    
    # 测试zip文件创建功能
    test_zip_creation()
    
    # 测试API响应结构
    test_api_structure()
    
    # 测试实际API（需要服务运行）
    print("\n=== 测试实际API接口 ===")
    print("注意：此测试需要ugs-api服务正在运行")
    
    try:
        test_download_4dcta_api()
    except Exception as e:
        print(f"API测试跳过（服务可能未运行）: {e}")
    
    print("\n=== 测试完成 ===")
