
### aspects v1.3
```text
更新内容
aspects算法v1.3
env_algorithem  增加aspects参数

镜像增加vtk包
pip3 install vtk==9.0.1

aspects v1.3更新内容

run.py is the interface script
调用get_report()获取result
report = result.get('report')
n_layer_template、up_layer_template、template_path、thre_low、thre_high、margin、
roi_thre、roi这8个参数建议可以从配置文件里读取。
template_path在git上的路径为/template

实验验证 

多中心数据，共1264个患者的2132个序列，V1.3相对于V1.2，在665/2132例上有提升。
 
优化基于的假设

1.V1.0给出分割的confidence大于等于0.8的数据，分割可以接受。
2.V1.0给出分割的confidence小于0.3的数据，通过刚性配准可以选出相对合理的核团层与核团上层，且柔性配准的分区合理性高于神经网络做分割。
3.V1.0给出分割的confidence在0.3到0.8之间的数据，若刚性配准之后神经网络分割的confidence+bargin大于V1.0给出分割的confidence，则使用刚性配准之后的结果，否则采用原结果。

优化策略 

1.V1.0分割的confidence大于等于0.8的数据，出V1.0（神经网络对原图像做选层与分区）的报告。
2.V1.0分割的confidence小于0.3的数据，通过刚性配准确定核团层与核团上层，通过柔性配准确定分区，给出报告。
3.V1.0分割的confidence大于等于0.3小于0.8的数据，通过刚性配准确定核团层与核团上层，对核团层与核团上层使用神经网络进行分区，并计算confidence_after_rigid_


aspects  版本 intergrate_ich_report
在v1.3基础上 增加出血demo  只针对A008.P000009.D02.S003 , A008.P000053.D02.S004,A0008.P000408.D01.S002进行出血处理
 出血测试数据 A008.P000009.D02.S003 , A008.P000053.D02.S004,A0008.P000408.D01.S002
 

部署:
 使用镜像 172.16.1.6/library/algorithm_cuda10_1:ctp_aspects_v1 
 需要把模型考到models/  uguard_aspects目录model下
 
 A100部署:
  aspects  修改aspect_util 的71行os.environ["CUDA_VISIBLE_DEVICES"] = "" 使用cpu
 
 
```

### dev2_v1.4    送检版本

```text
在dev2_1.3基础上增加出血数据A008.P000009.D02.S003 返回结果和位置 

使用镜像：172.16.1.6/uguard/algorithm_cuda11_aspects:v1

启动 docker-compose-algorithm-aspect.yml

需要把模型考到models/  uguard_aspects目录model下

```

