#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : setup.py
@Project : ugs-aspects
<AUTHOR> mingxing
@Date    : 2023/3/28 13:50
"""
from setuptools import setup, Extension
from setuptools.command.build_py import build_py as build_py_orig
from Cython.Build import cythonize
import src.ugs_aspects.package_info as pi

ext_modules = [
    Extension("ugs_aspects.*", ["src/ugs_aspects/*.py"]),
    Extension("ugs_aspects.lib.*", ["src/ugs_aspects/lib/*.py"])
]


class BuildPy(build_py_orig):
    def build_packages(self):
        pass


setup(
    name=pi.name,
    author="unionstrong",
    url=pi.url,
    cmdclass={"build_py": BuildPy},
    ext_modules=cythonize(ext_modules, language_level="3"),
    version=pi.version,
    packages=[pi.name],
    package_dir={"": "src"},
    package_data={
        pi.name: ["static/image/*.png", "static/template/3d/*.html", "static/template/3d/icon/*.png",
                  "static/template/ich/*", "static/template/pc/*"]
    },
    platforms=pi.platforms,
    description=pi.description,
    long_description=pi.long_description,
    license="MIT",
    entry_points={
        "console_scripts": [
            "ugsaspects = ugs_aspects.consumer:main"
        ]
    }
)
