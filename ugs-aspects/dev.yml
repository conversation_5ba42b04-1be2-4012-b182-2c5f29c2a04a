version: "3"
services:
  ugs-aspects:
    container_name: "ugs-aspects"
    image: harbor.unionstrongtech.com/ugs/aspects:1.8.0rc0
    volumes:
      - ./src/ugs_aspects:/ugs_aspects
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    working_dir: /ugs_aspects/
    env_file:
      - .env
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    command: python3.8 -u consumer.py
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true
