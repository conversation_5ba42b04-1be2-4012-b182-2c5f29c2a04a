#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : aspects
@Project : uguard_aspects
<AUTHOR> mingxing
@Date    : 2022/8/4 18:06
"""
import os
import time
import traceback
from abc import ABC, abstractmethod
from multiprocessing import Manager, Process

from PIL import Image
import numpy as np

from lib.common import FileUtils, WebApi, StringUtils
from lib.const import RetCode, Config
from lib.logger import log

from brain_seg.utils.utils import save_img


class AspectsHandler(ABC):
    @abstractmethod
    def handle(self, params: dict):
        pass


class BaseHandler(AspectsHandler):
    _next_handler = None

    def __init__(self, handler: AspectsHandler = None):
        self._next_handler = handler

    def set_next(self, handler):
        self._next_handler = handler

    @abstractmethod
    def _check(self, params: dict):
        pass

    @abstractmethod
    def _calculate(self, params: dict):
        pass

    def handle(self, params: dict):
        if self._check(params):
            self._calculate(params)
        if self._next_handler:
            self._next_handler.handle(params)


class BrainHandler(BaseHandler):
    """
    核团模块处理
    """

    def _check(self, params: dict):
        return False

    def _calculate(self, params: dict):
        log.info("Aspects[brain] > start")
        try:
            from brain_seg.utils.utils import to_one_hot, save_img
            from brain_seg.utils.constant import ASPECTS_COLORS, ASPECTS_LABELS
            from brain_seg.utils.visulization import get_images_with_masks

            save_dir = os.path.join(params["reportDir"], "3D_ASPECTS_MASK_USC/pic")
            FileUtils.create_dirs(save_dir)
            warning_image = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/image/warning.png")
            warning_array = np.asarray(Image.open(warning_image))[..., :3] / 255
            seg_array_one_hot = to_one_hot(params["segArray"], num_classes=35)
            area_names_aspects = ['C', 'L', 'IC', 'I', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'pca', 'cerebellum',
                                  'thalamus',
                                  'midbrain', 'pon']
            left_labels = [ASPECTS_LABELS[i] for i in area_names_aspects]
            right_labels = list(np.array(left_labels[:10]) + 10) + list(np.array(left_labels[10:]) + 7)
            labels = left_labels + right_labels
            seg_array_to_show = [np.expand_dims(seg_array_one_hot[..., i], axis=-1) for i in labels]
            seg_array_to_show = np.concatenate(seg_array_to_show, axis=-1)
            colors = np.array([ASPECTS_COLORS[i] for i in area_names_aspects * 2]) / 255
            images_show = get_images_with_masks(params["images"], seg_array_to_show, color_list=colors)
            for i, image in enumerate(images_show):
                if image.ndim == 2:
                    image = np.expand_dims(image, axis=-1)
                    image = np.tile(image, [1, 1, 3])
                image = np.concatenate([image, warning_array], axis=0)
                save_img(image, os.path.join(save_dir, "{}.jpg".format(i)))
            params["report"].update({"3dMask": save_dir})
            params["retCode"] = RetCode.OK.code
        except Exception as e:
            log.error(traceback.format_exc())
        return True


class TDHandler(BaseHandler):
    """
    前循环模块处理
    """

    def _check(self, params: dict):
        # can_3d = params.get("can3D")
        # log.info("Aspects > check 3D:{}".format(can_3d))
        # return True if can_3d else False
        return False

    def _calculate(self, params: dict):
        log.info("Aspects[3d] > start")
        try:
            from aspects_3d.run import generate_report_from_segmentation, get_html

            config_thre = params["configs"]["thre"]
            result = generate_report_from_segmentation(params["images"], params["spacing"], params["segArray"],
                                                       config_thre, verbose=False)
            log.info("Aspects[3d] > result:{}".format(result))
            save_dir = os.path.join(params["outputDir"], "report_3d")
            FileUtils.create_dirs(save_dir)
            template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/template/3d")
            icon_dir = os.path.join(template_dir, "icon")
            target_icon_dir = os.path.join(save_dir, "icon")
            FileUtils.copy_dirs(icon_dir, target_icon_dir)
            report_img_path = os.path.join(save_dir, "icon", "img.png")
            save_img(result["report"][:1080, :1080, :], report_img_path)
            # 读取模板内容
            html_file_path = os.path.join(template_dir, "report.html")
            template_content = FileUtils.read_file(html_file_path)
            # 生成报告
            report_html_path = os.path.join(save_dir, "report.html")
            content = get_html(result, html_template=template_content)
            FileUtils.save_file(report_html_path, content)
            report_dir = os.path.join(params["reportDir"], "3D_ASPECTS_SUMMARY_USC/pic")
            os.makedirs(report_dir, exist_ok=True)
            report_3d_path = os.path.join(report_dir, "1.jpg")
            FileUtils.html2jpg(report_html_path, report_3d_path)
            log.info("Aspects[3d] > result:{}".format(report_dir))
            params["report"].update({"3d": report_dir})
            # 计算评分
            infarct_areas_left = result["infarct_areas_L"].tolist() \
                if isinstance(result["infarct_areas_L"], np.ndarray) else result["infarct_areas_L"]
            infarct_areas_right = result["infarct_areas_R"].tolist() \
                if isinstance(result["infarct_areas_R"], np.ndarray) else result["infarct_areas_R"]
            score_left = 10 - len(infarct_areas_left)
            score_right = 10 - len(infarct_areas_right)
            params["result"].update({"frontCycleInfarctAreasLeft": infarct_areas_left,
                                     "frontCycleInfarctAreasRight": infarct_areas_right,
                                     "frontCycleScoreLeft": score_left, "frontCycleScoreRight": score_right,
                                     "frontCycleInfarctSide": result["infarct_side"]})
            params["retCode"] = RetCode.OK.code
        except Exception:
            log.error("Aspects[3d] > failed to generate report:{}".format(traceback.format_exc()))
        return True


class PCHandler(BaseHandler):
    """
    后循环模块处理
    """

    def _check(self, params: dict):
        # can_pc = params.get("canPC")
        # log.info("Aspects > check PC:{}".format(can_pc))
        # return True if can_pc else False
        return False

    def _calculate(self, params: dict):
        log.info("Aspects[pc] > start")
        try:
            from pc_aspects.run import get_pc_aspects_report_from_segmentation, get_pc_aspects_html

            config_thre_pc = params["configs"]["thre_pc"]
            result = get_pc_aspects_report_from_segmentation(params["images"], params["spacing"], params["segArray"],
                                                             config_thre_pc, verbose=False)
            log.info("Aspects[pc] > result:{}".format(result))
            save_dir = os.path.join(params["outputDir"], "report_pc")
            image_dir = os.path.join(save_dir, 'image')
            FileUtils.create_dirs(image_dir)
            result_array = result["result_array"]
            for i, slice in enumerate(result_array):
                save_img(slice, os.path.join(image_dir, "{}.png".format(i + 1)))
            # 读取模板内容
            html_file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/template/pc/report.html")
            template_content = FileUtils.read_file(html_file_path)
            # 生成报告
            report_html_path = os.path.join(save_dir, "report.html")
            content = get_pc_aspects_html(result, html_template=template_content)
            FileUtils.save_file(report_html_path, content)
            report_dir = os.path.join(params["reportDir"], "3D_PC-ASPECTS_SUMMARY_USC/pic")
            os.makedirs(report_dir, exist_ok=True)
            report_pc_path = os.path.join(report_dir, "1.jpg")
            FileUtils.html2jpg(report_html_path, report_pc_path)
            log.info("Aspects[pc] > report:{}".format(report_pc_path))
            params["report"].update({"pc": report_dir})
            params["result"].update({"postCycleInfarctAreas": result["infarct_areas"],
                                     "postCycleScore": result["score"]})
            params["retCode"] = RetCode.OK.code
        except Exception:
            log.error("Aspects[pc] > failed to generate report:{}".format(traceback.format_exc()))


class ICHHandler(BaseHandler):
    """
    出血模块处理
    """

    def _check(self, params: dict):
        can_ich = params.get("canICH")
        log.info("Aspects > check ICH:{}".format(can_ich))
        return True if can_ich else False

    def _calculate(self, params: dict):
        log.info("Aspects[ich] > start")
        try:
            from ich_seg.run import get_ich_model, generate_report_from_dir
            from ich_seg.utils.report import get_html_report1, get_html_report2
            from skimage.transform import resize

            configs = params["configs"]
            ich_configs = dict(confidences_thre=configs["confidences_thre"], reverse=configs["reverse"],
                               volume_nidus_thre=configs["volume_nidus_thre"], volume_thre=configs["volume_thre"],
                               full_conv=configs["full_conv"], max_brain_length=configs["max_brain_length"])
            model, spacing_nn, num_pool_per_axis = get_ich_model()
            result = generate_report_from_dir(params["dcmDir"], model, spacing_nn, num_pool_per_axis, ich_configs,
                                              verbose=False)
            log.info("Aspects[ich] > result:{}".format(result))
            if not result["flag"]:
                log.info("Aspects[ich] > failed to generate report:{}".format(result.get("error_message", "")))
                return
            images = result["result"]
            # 出血体积
            report1 = images["report1"]
            template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/template/ich")
            html_file1_path = os.path.join(template_dir, "report1.html")
            content1 = FileUtils.read_file(html_file1_path)
            save_dir = os.path.join(params["outputDir"], "report_ich")
            FileUtils.create_dirs(save_dir)
            slices = report1["slices"]
            slices_size = len(slices)
            slice_dir = os.path.join(save_dir, "image")
            FileUtils.create_dirs(slice_dir)
            for i, _slice in enumerate(slices, 1):
                save_img(_slice, os.path.join(slice_dir, "{}.png".format(i)))
            if slices_size < 40:
                _slice = slices[slices_size-1]
                for i in range(slices_size+1, 41):
                    save_img(np.zeros_like(_slice), os.path.join(slice_dir, "{}.png".format(i)))
            html1_path = os.path.join(save_dir, "report1.html")
            get_html_report1(report1, html_template=content1, write_path=html1_path)
            report_dir = os.path.join(params["reportDir"], "HEMORRHAGE_SUMMARY_USC", "pic")
            FileUtils.create_dirs(report_dir)
            report1_ich_path = os.path.join(report_dir, "1.jpg")
            FileUtils.html2jpg(html1_path, report1_ich_path)
            log.info("Aspects[ich] > volume:{}".format(report1["total_volume"]))
            params["result"].update({"ichVolume": report1["total_volume"]})
            # 出血亚型
            html_subtype_path = os.path.join(template_dir, "ich_subtype.html")
            content2 = FileUtils.read_file(html_subtype_path)
            save_dir = os.path.join(params["outputDir"], "report_ich")
            FileUtils.create_dirs(save_dir)
            html2_path = os.path.join(save_dir, "report2.html")
            get_html_report2(images["report2"], html_template=content2, write_path=html2_path)
            report2_ich_path = os.path.join(report_dir, "2.jpg")
            FileUtils.html2jpg(html2_path, report2_ich_path)
            log.info("Aspects[ich] > report:{}".format(report_dir))
            params["report"].update({"ich": report_dir})
            # 出血分区
            report3_dir = os.path.join(params["reportDir"], "HEMORRHAGE_MASK_USC", "pic")
            FileUtils.create_dirs(report3_dir)
            legend_name = os.path.join(template_dir, "legend.jpg")
            legend = np.asarray(Image.open(legend_name))[::2, ::2, :]
            margin_width = int((720 - 512) / 2)
            margin_left = np.zeros([512, margin_width, 3])
            margin_left[512 - 180:512 - 30, 20:80, :] = legend / 255
            margin_right = np.zeros([512, margin_width, 3])
            slice_array = images["report3"]["slices"]
            for i, _slice in enumerate(slice_array):
                _slice = np.squeeze(_slice)
                if _slice.ndim == 2:
                    _slice = (_slice - np.min(_slice)) / (np.max(_slice) - np.min(_slice) + 1e-8)
                    _slice = np.expand_dims(_slice, axis=-1)
                    _slice = np.tile(_slice, [1, 1, 3])
                if _slice.shape != [512, 512, 3]:
                    _slice = resize(_slice, [512, 512, 3])
                out = np.concatenate([margin_left, _slice, margin_right], axis=1)
                save_img(out, os.path.join(report3_dir, "{}.jpg".format(i + 1)))
            log.info("Aspects[ich] > report:{}".format(report3_dir))
            params["report"].update({"ichMask": report3_dir})
            params["retCode"] = RetCode.OK.code
        except Exception:
            log.error("Aspects[ich] > failed to generate report:{}".format(traceback.format_exc()))


class AspectRunner:
    def __init__(self, study_instance_uid, series_instance_uid, images, spacing, output_dir, report_dir, gpu_id=0):
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.images = images
        self.spacing = spacing
        self.output_dir = output_dir
        self.report_dir = report_dir
        # os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
        # if not tf.test.is_gpu_available(cuda_only=True):
        #     log.warning("Warning: GPU[{}] is not available, using CPU instead".format(gpu_id))

    def get_result(self):
        manager = Manager()
        mgr_dict = manager.dict()
        p = Process(target=self.go, args=(mgr_dict,))
        p.start()
        p.join()
        return mgr_dict.copy()

    def go(self, results):
        from brain_seg.run import get_model, get_segmentation, get_nuclei_confidence, get_flags

        start_time = time.time()
        configs = self.get_configs()
        confidences = configs["confidences_thre"]
        configs["confidences_thre"] = [float(i) for i in confidences.split(",")]
        model, spacing_nn, num_pool_per_axis = get_model()
        images, spacing = self.images, self.spacing
        if configs["reverse"]:
            log.info("Aspects[study:{},series:{}] > reverse image:{}".format(
                self.study_instance_uid, self.series_instance_uid, configs["reverse"]))
            images = images[::-1, ...]
        seg_array, box = get_segmentation(images, spacing, model, spacing_nn, num_pool_per_axis,
                                          keep_largest_component=configs["keep_largest_component"],
                                          max_brain_length=180, crop_skull=True, m1_m6_fixed=configs["m1_m6_fixed"])
        flags = get_flags(seg_array, spacing, plot=False)
        log.info("Aspects[brain] > image_shape:{}, spacing:{}, flags:{}".format(images.shape, spacing, flags))
        if not flags["nuclei_exist"]:
            results["retCode"] = RetCode.ASPECTS_NUCLEI_DOES_NOT_EXIST.code
            results["runTime"] = time.time() - start_time
            log.info("Aspects[study:{},series:{}] > nuclei does not exist".format(
                self.study_instance_uid, self.series_instance_uid))
            return
        params = dict(dcmDir=os.path.join(Config.DIR_DCM, self.series_instance_uid), configs=configs,
                      outputDir=self.output_dir, reportDir=self.report_dir,
                      images=images, spacing=spacing, segArray=seg_array,
                      can3D=flags["run_aspects"], canPC=flags["run_pc_aspects"], canICH=flags["run_ich"],
                      report={}, result={})
        log.info("Aspects[study:{},series:{}] > params:{}".format(
            self.study_instance_uid, self.series_instance_uid, params))
        handler = BrainHandler(TDHandler(PCHandler(ICHHandler())))
        handler.handle(params)
        results["runTime"] = time.time() - start_time
        results["can3D"] = flags["run_aspects"]
        results["canPC"] = flags["run_pc_aspects"]
        results["canICH"] = flags["run_ich"]
        results["report"] = params.get("report")
        results["result"] = params.get("result")
        results["retCode"] = params.get("retCode", RetCode.ASPECTS_ERROR.code)

    @staticmethod
    def get_configs():
        conf_list = WebApi.find_config([], category="aspects", tag="init")
        if not conf_list:
            log.warning("aspects config not found")
            return
        conf_size = len(conf_list)
        log.info("find {} configurations".format(conf_size))
        config = {}
        for conf in conf_list:
            _code = conf.get("code", "")
            _value = conf.get("value", "")
            _format = conf.get("format", "")
            config[_code] = StringUtils.parse_value(_format, _value)
        return config


