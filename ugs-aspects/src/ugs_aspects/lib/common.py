#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : util
@Project : uguard_aspects
<AUTHOR> mingxing
@Date    : 2022/8/3 10:33
"""
import copy
import datetime
import json
import os
import random
import shutil
import traceback

import imgkit
import pika
import pydicom
import numpy as np
import SimpleIT<PERSON> as sitk
from PIL import Image
from pydicom.uid import (JPEG2000, DeflatedExplicitVRLittleEndian,
                         ExplicitVRBigEndian, ExplicitVRLittleEndian,
                         ImplicitVRLittleEndian, JPEG2000Lossless,
                         JPEG2000MultiComponent,
                         JPEG2000MultiComponentLossless, JPEGBaseline,
                         JPEGExtended, JPEGLosslessSV1, JPEGLosslessP14,
                         JPEG<PERSON>Lossless, JPEGLSLossy, RLELossless)
from pynetdicom import AE, StoragePresentationContexts

import requests
from requests.auth import HTTPBasicAuth

from lib.const import Config, Consts
from lib.logger import log


class StringUtils:
    __BOOL_VALID = {"true": True, "1": True, "false": False, "0": False}

    @staticmethod
    def to_bool(value):
        if isinstance(value, bool):
            return value
        if not isinstance(value, str):
            raise ValueError("invalid literal for boolean, Not a string.")
        lower_value = value.lower()
        if lower_value not in StringUtils.__BOOL_VALID:
            raise ValueError("invalid literal for boolean: {}".format(value))
        return StringUtils.__BOOL_VALID[lower_value]

    @staticmethod
    def parse_value(_format, _value):
        if _format == "text":
            return str(_value)
        if _format == "bool":
            return StringUtils.to_bool(_value)
        if _format == "int":
            return int(_value)
        if _format == "float":
            return float(_value)
        log.info("invalid config format, value:{}, format:{}".format(_value, _format))
        return _value


class NpEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            # 👇️ alternatively use str()
            return float(obj)
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        return json.JSONEncoder.default(self, obj)


class FileUtils:
    @staticmethod
    def copy_dirs(source_dir, target_dir):
        if os.path.exists(target_dir):
            shutil.rmtree(target_dir)
        shutil.copytree(source_dir, target_dir)

    @staticmethod
    def remove_dir(dir_path):
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)

    @staticmethod
    def create_dirs(dir_path):
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)

    @staticmethod
    def rebuild_dirs(dir_path):
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
        os.makedirs(dir_path)

    @staticmethod
    def get_filename_without_suffix(path):
        return os.path.splitext(path)[0]

    @staticmethod
    def get_all(dir_path):
        if not os.path.exists(dir_path) or not os.path.isdir(dir_path):
            log.info("invalid directory:{}".format(dir_path))
            return []
        files = []
        for root, dirnames, filenames in os.walk(dir_path):
            for filename in filenames:
                file_path = os.path.join(root, filename)
                files.append(file_path)
        return files


    @staticmethod
    def get_first(dir_path):
        for root, dirs, files in os.walk(dir_path):
            if not files:
                return None
            for file in files:
                return os.path.join(dir_path, file)

    @staticmethod
    def save_json(content: dict, file_path: str, sort_keys: bool = True, indent: int = 4):
        with open(file_path, "w") as f:
            json.dump(content, f, sort_keys=sort_keys, indent=indent)

    @staticmethod
    def save_image(image_array, target_path):
        """
        保存图像

        :param image_array: 图像数组
        :param target_path: 保存路径
        :return:
        """
        img = np.squeeze(image_array)
        img = (img - np.min(img)) / (np.max(img) - np.min(img))
        result = Image.fromarray((img * 255).astype(np.uint8))
        result.save(target_path)

    @staticmethod
    def html2jpg(html_path, target_path):
        """
        网页转图片

        :param html_path: html路径
        :param target_path: 图像保存路径
        :return:
        """
        options = {"xvfb": "", "format": "jpg", "crop-w": "1188", "crop-h": "681", "enable-javascript": "",
                   "enable-local-file-access": ""}
        imgkit.from_file(html_path, target_path, options=options)

    @staticmethod
    def save_img(img, out_dir):
        """
        Save img in out_dir

        :param img:
        :param out_dir:
        :return:
        """

        img = np.squeeze(img)
        if img.ndim == 2:
            img = np.expand_dims(img, axis=-1)
            img = np.tile(img, [1, 1, 3])
        img = (img - np.min(img)) / (np.max(img) - np.min(img))
        np_array = (img * 255).astype(np.uint8)
        result = Image.fromarray(np_array)
        if len(np_array.shape) == 2:
            result.convert("RGB")
        result.save(out_dir)

    @staticmethod
    def read_file(file_path):
        with open(file_path, "rb") as f:
            return f.read()

    @staticmethod
    def save_file(file_path, content):
        with open(file_path, "wb") as f:
            f.write(content)


class RabbitProducer:
    ALGORITHM_RESULT = "algorithm_ctp_aspects_result"
    ALGORITHM_STATUS = "algorithm_working_status"

    @staticmethod
    def send(message: dict, queue_name: str = ALGORITHM_RESULT):
        """
        发送消息（提供统一方法）
        RabbitMQ消息生产者

        :param message: 消息
        :param queue_name: 队列名称（默认发送算法结果）
        :return:
        """

        connection = pika.BlockingConnection(
            pika.ConnectionParameters(Config.MQ_HOST, Config.MQ_PORT, "/",
                                      pika.PlainCredentials(Config.MQ_USERNAME, Config.MQ_PASSWORD),
                                      heartbeat=0))
        chanel = connection.channel()
        chanel.queue_declare(queue=queue_name, durable=True)
        body_str = json.dumps(message)
        chanel.basic_publish(exchange="", routing_key=queue_name,
                             properties=pika.BasicProperties(
                                 content_type="application/json",
                                 delivery_mode=2),
                             body=body_str)
        log.info("MQ send > queue:{}, message:{}".format(queue_name, body_str))
        connection.close()


class OrthancApi:

    _BASE_URL = F"http://{Config.PACS_HOST}:{Config.PACS_DOCKER_PORT}"
    _AUTH = HTTPBasicAuth(Config.PACS_USERNAME, Config.PACS_PASSWORD)

    @staticmethod
    def upload_image(file_path):
        url = F"{OrthancApi._BASE_URL}/instances"
        log.info("Orthanc[request] > url:{}, file:{}".format(url, file_path))
        with open(file_path, "rb") as f:
            files = {"files": (FileUtils.get_filename_without_suffix(file_path), f,
                               "application/octet-stream", {"Expires": "0"})}
            response = requests.post(url=url, files=files, auth=OrthancApi._AUTH, timeout=15)
        log.info("Orthanc[response] > code:{}".format(response.status_code))
        return response.status_code == 200

    @staticmethod
    def move(data):
        url = F"{OrthancApi._BASE_URL}/modalities/local/move"
        log.info("Orthanc[request] > url:{}, data:{}".format(url, data))
        response = requests.post(url=url, json=data, auth=OrthancApi._AUTH)
        log.info("Orthanc[response] > code:{}".format(response.status_code))
        return response.status_code == 200


class DicomUtils:

    @staticmethod
    def read(dcm_path):
        return pydicom.read_file(dcm_path, force=True)

    @staticmethod
    def generate_child_uid(instance_uid):
        tem_uid = instance_uid.split(".")[:-1]
        tem_uid.append(str(random.randint(10 ** 37, 10 ** 38 - 1)))
        return ".".join(tem_uid)

    @staticmethod
    def jpg2dcm(dataset_template, jpg_path, series_instance_uid, series_description, instance_number,
                original_series_number, modality=Consts.DEFAULT_ALGORITHM_RESULT_MODALITY):
        if not os.path.exists(jpg_path):
            log.error("{} not found".format(jpg_path))
            return None
        try:
            ds = copy.deepcopy(dataset_template)
            ds.file_meta.TransferSyntaxUID = ExplicitVRLittleEndian
            img = np.asarray(Image.open(jpg_path))
            ds.Rows, ds.Columns, ds.SamplesPerPixel = img.shape
            ds.PixelData = img.tostring()
            sop_instance_uid = DicomUtils.generate_child_uid(series_instance_uid)
            ds.SeriesInstanceUID = series_instance_uid
            ds.SOPInstanceUID = sop_instance_uid
            ds.InstanceNumber = instance_number
            ds.SeriesNumber = original_series_number
            now = datetime.datetime.now()
            create_date = now.strftime("%Y%m%d")
            create_time = now.strftime("%H%M%S")
            ds.SeriesDate = create_date
            ds.SeriesTime = create_time
            ds.ContentDate = create_date
            ds.ContentTime = create_time
            ds.Modality = modality
            ds.Manufacturer = "UnionStrong"
            ds.StationName = "USC-UGuard"
            ds.SeriesDescription = series_description
            ds.ManufacturerModelName = "UGuard"
            ds.PixelRepresentation = 0
            ds.BitsAllocated = 8
            ds.HighBit = 7
            ds.BitsStored = 8
            ds.PlanarConfiguration = 0
            ds.PhotometricInterpretation = "RGB"
            ds.is_implicit_VR = False
            ds.LossyImageCompression = "01"
            ds.LossyImageCompressionRatio = 10  # default jpeg
            ds.LossyImageCompressionMethod = "ISO_10918_1"
            ds.WindowCenter = "128"
            ds.WindowWidth = "256"
            ds.RescaleIntercept = "0"
            ds.RescaleSlope = "1"
            return ds
        except:
            log.error(traceback.format_exc())
            return None

    @staticmethod
    def get_images_by_sitk(file_list):
        is_success = False
        images = list()
        locations = list()
        series_uid = set()
        pixel_spacing = None
        try:
            for file_path in file_list:
                img = sitk.ReadImage(file_path)
                if not pixel_spacing:
                    pixel_spacing = img.GetSpacing()[:2]
                series_uid.add(img.GetMetaData("0x20, 0xe"))
                locations.append(float(img.GetMetaData("0x20, 0x0032")[-1]))
                image = np.squeeze(sitk.GetArrayFromImage(img))
                assert image.ndim == 2, "image.ndim = {}, expect 2".format(image.ndim)
                images.append(image)
            is_success = True
        except Exception as e:
            log.info("Aspects > failed to get image(sitk):{}".format(e))
        return is_success, len(series_uid), images, locations, pixel_spacing

    @staticmethod
    def get_images_by_pydicom(file_list):
        is_success = False
        images = list()
        locations = list()
        series_uid = set()
        pixel_spacing = None
        try:
            for file_path in file_list:
                ds = pydicom.read_file(file_path, force=True)
                if not pixel_spacing:
                    pixel_spacing = ds[0x28, 0x30].value
                image = np.array(ds.pixel_array, dtype="float")
                image = np.squeeze(image)
                assert image.ndim == 2, "image.ndim = {}, expect 2".format(image.ndim)
                series_uid.add(ds[0x20, 0xe].value)
                locations.append(float(ds[0x20, 0x0032].value[-1]))
                intercept = ds[0x28, 0x1052].value
                slope = ds[0x28, 0x1053].value
                image = image * slope + intercept
                images.append(image)
            is_success = True
        except Exception as e:
            log.info("Aspects > failed to get image(pydicom):{}".format(e))
        return is_success, len(series_uid), images, locations, pixel_spacing


class PacsUtils:
    ALL_TRANSFER_SYNTAX = [
        ImplicitVRLittleEndian,
        ExplicitVRLittleEndian,
        ExplicitVRBigEndian,
        DeflatedExplicitVRLittleEndian,
        JPEGBaseline,
        JPEGExtended,
        JPEGLosslessP14,
        JPEGLosslessSV1,
        JPEGLSLossless,
        JPEGLSLossy,
        JPEG2000Lossless,
        JPEG2000,
        JPEG2000MultiComponentLossless,
        JPEG2000MultiComponent,
        RLELossless,
    ]

    @staticmethod
    def get_associate(local_act, target_host, target_port, target_aet):
        ae = AE(ae_title=local_act)
        ae.dimse_timeout = 60
        for _context in StoragePresentationContexts:
            ae.add_requested_context(_context.abstract_syntax, PacsUtils.ALL_TRANSFER_SYNTAX)
        log.info("associate > host:{}, port:{}".format(target_host, target_port))
        return ae.associate(target_host, target_port, ae_title=target_aet)

    @staticmethod
    def send_dcm(file_path, local_act, target_host, target_port, target_aet):
        if not os.path.exists(file_path) and not os.path.isfile(file_path):
            log.warning("C-STORE > {} not found".format(file_path))
            return False
        dataset = pydicom.read_file(file_path)
        assoc = PacsUtils.get_associate(local_act, target_host, target_port, target_aet)
        if not assoc.is_established:
            log.warning("C-STORE > failed to associate")
            return False
        response = {}
        try:
            response = assoc.send_c_store(dataset)
        except Exception:
            log.info("C-STORE > store scu error: {}".format(traceback.format_exc()))
            assoc.release()
            transfer_syntax_uid = dataset.file_meta.TransferSyntaxUID
            sop_class_uid = dataset.SOPClassUID
            log.info("C-STORE > reassociate with SOPClassUID:{}, TransferSyntaxUID:{}".format(
                sop_class_uid, transfer_syntax_uid))
            ae = AE(ae_title=Config.LOCAL_AET)
            ae.add_requested_context(sop_class_uid, transfer_syntax_uid)
            ae.associate(Config.PACS_HOST, Config.PACS_PORT, ae_title=Config.PACS_AET)
            if assoc.is_established:
                response = assoc.send_c_store(dataset)
        result_status = response and response.get("Status") == 0
        log.info("{} send {}".format(os.path.basename(file_path), result_status))
        assoc.release()
        return result_status

    @staticmethod
    def send_batch_dcm(file_list, local_act, target_host, target_port, target_aet):
        if not file_list or len(file_list) == 0:
            log.warning("C-STORE > {} not found".format(file_list))
            return False
        assoc = PacsUtils.get_associate(local_act, target_host, target_port, target_aet)
        if not assoc.is_established:
            log.warning("C-STORE > failed to associate")
            return False
        response = {}
        for file_path in file_list:
            dataset = pydicom.read_file(file_path)
            try:
                response = assoc.send_c_store(dataset)
            except Exception:
                log.info("C-STORE > store scu error: {}".format(traceback.format_exc()))
                assoc.release()
                transfer_syntax_uid = dataset.file_meta.TransferSyntaxUID
                sop_class_uid = dataset.SOPClassUID
                log.info("C-STORE > reassociate with SOPClassUID:{}, TransferSyntaxUID:{}".format(
                    sop_class_uid, transfer_syntax_uid))
                ae = AE(ae_title=Config.LOCAL_AET)
                ae.add_requested_context(sop_class_uid, transfer_syntax_uid)
                ae.associate(Config.PACS_HOST, Config.PACS_PORT, ae_title=Config.PACS_AET)
                if assoc.is_established:
                    response = assoc.send_c_store(dataset)
            result_status = response and response.get("Status") == 0
            if not result_status:
                return False
            log.info("{} send {}".format(os.path.basename(file_path), result_status))
        assoc.release()
        return True


class WebApi:
    """平台接口服务"""
    _BASE_URL = F"http://{Config.WEBAPI_HOST}:{Config.WEBAPI_PORT}/api/v1"

    @staticmethod
    def get_config(code):
        """
        配置查询

        :param code: 配置编码
        :param category: 配置分类
        :param tag: 配置标签
        :return:
        """
        url = F"{WebApi._BASE_URL}/systemsettings?code={code}"
        try:
            log.info("WebApi[get] > url:{}".format(url))
            response = requests.get(url=url)
            response_body = response.json()
            log.info("WebApi[get] > response[{}]: {}".format(response.status_code, response_body))
            return WebApi.__get_data(response_body)
        except Exception:
            log.error("failed to call WebApi: {}".format(traceback.format_exc()))
            return []

    @staticmethod
    def find_config(code_list, category="", tag=""):
        """
        配置查询

        :param code_list: 配置编码列表
        :param category: 配置分类
        :param tag: 配置标签
        :return:
        """
        codes = ""
        if code_list and len(code_list) > 0:
            codes = "&codes=" + "&codes=".join(code_list)
        url = F"{WebApi._BASE_URL}/systemsettings?type={category}&tag={tag}{codes}"
        try:
            log.info("WebApi[get] > url:{}".format(url))
            response = requests.get(url=url)
            response_body = response.json()
            log.info("WebApi[get] > response[{}]: {}".format(response.status_code, response_body))
            return WebApi.__get_data(response_body)
        except Exception:
            log.error("failed to call WebApi: {}".format(traceback.format_exc()))
            return []

    @staticmethod
    def find_pacs_server():
        """
        PACS Server查询

        :return:
        """
        url = F"{WebApi._BASE_URL}/async/pacsserver/"
        try:
            log.info("WebApi[get] > url:{}".format(url))
            response = requests.get(url=url)
            response_body = response.json()
            log.info("WebApi[get] > response[{}]: {}".format(response.status_code, response_body))
            return WebApi.__get_data(response.json())
        except Exception:
            log.error("failed to call WebApi: {}".format(traceback.format_exc()))
            return []

    @staticmethod
    def __get_data(response_body):
        if not response_body or "data" not in response_body:
            return dict()
        return response_body.get("data")
