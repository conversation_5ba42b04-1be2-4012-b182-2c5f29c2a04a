#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : config
@Project : uguard_aspects
<AUTHOR> mingxing
@Date    : 2022/7/18 13:24
"""
import os
from enum import Enum

_env = os.environ


class Consts:
    REPORT_BACK_METHOD_ORTHANC = "orthanc"
    PACS_TARGET = "target_pacs"
    CODE_ALGORITHM_RESULT_MODALITY = "algorithmResultModality"
    DEFAULT_ALGORITHM_RESULT_MODALITY = "OT"


class Config:
    # # Common
    SERVICE_NAME = _env.get("SERVICE_NAME", "ugs-aspects")
    SERVICE_VERSION = _env.get("SERVICE_VERSION", "1.8.0rc0")
    # RabbitMQ Configuration
    MQ_HOST = _env.get("MQ_HOST", "cloud_platform_rabbitmq")
    MQ_PORT = _env.get("MQ_PORT", 5673)
    MQ_USERNAME = _env.get("MQ_USERNAME", "unionstrong")
    MQ_PASSWORD = _env.get("MQ_PASSWORD", "UnionStrong@2020")
    # PACS Configuration`
    PACS_HOST = _env.get("PACS_HOST", "cloud_platform_pacs")
    PACS_AET = _env.get("PACS_AET", "DockerOrthanc")
    PACS_PORT = int(_env.get("PACS_PORT", 4242))
    PACS_DOCKER_PORT = int(_env.get("PACS_DOCKER_PORT", 8042))
    PACS_USERNAME = _env.get("PACS_USERNAME", "unionstrong")
    PACS_PASSWORD = _env.get("PACS_PASSWORD", "UnionStrong@2020")
    LOCAL_AET = _env.get("LOCAL_AET", "UGUARD_ASPECTS")
    # WebApi Configuration
    WEBAPI_HOST = _env.get("WEBAPI_HOST", "webapi")
    WEBAPI_PORT = _env.get("WEBAPI_PORT", "4201")
    # Path Configuration
    DIR_ROOT = _env.get("ROOT_DIR", "")
    DIR_DCM = os.path.join(DIR_ROOT, "dcm")
    DIR_STATIC = os.path.join(DIR_ROOT, "static")


class RetCode(Enum):
    """返回码枚举类"""
    OK = (0, "Process successfully!")

    # 平台（40000~49999）
    PLATFORM_SERIES_NOT_FOUND = (46000, "Series Not Found")
    PLATFORM_UPLOAD_REPORT_ERROR = (46001, "Upload Report Error")
    PLATFORM_LOAD_IMAGES_FAILED = (46002, "Load images Failed")
    # Aspects算法（60000~69999）
    ASPECTS_ERROR = (60000, "ASPECTS error!")
    ASPECTS_REPORT_NOT_FOUND = (60001, "ASPECTS report not found!")
    ASPECTS_LOAD_IMAGES_FAILED = (60002, "ASPECTS LOAD IMAGES FAILED!")
    ASPECTS_NUCLEI_DOES_NOT_EXIST = (60003, "ASPECTS NUCLEI DOES NOT EXIST!")
    ASPECTS3D_ERROR = (61000, "ASPECTS 3D error!")

    @property
    def code(self):
        """
        获取状态码
        """
        return self.value[0]

    @property
    def msg(self):
        """
        获取状态码信息
        """
        return self.value[1]

