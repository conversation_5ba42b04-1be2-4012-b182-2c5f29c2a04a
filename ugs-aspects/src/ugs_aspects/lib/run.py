#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : run
@Project : uguard_aspects
<AUTHOR> mingxing
@Date    : 2022/7/18 10:16
"""
import json
import os
import time
import numpy as np

from lib.aspects import <PERSON>pectRunner
from lib.common import RabbitProducer, FileUtils, DicomUtils
from lib.const import RetCode, Config
from lib.logger import log
from lib.service import ReportHandler


def handle_aspect(channel, method, study_instance_uid, series_instance_uid, algorithm_type, **kwargs):
    """
    处理Aspects

    :param channel: callback channel
    :param method:  callback method
    :param study_instance_uid: 检查标识
    :param series_instance_uid: 序列标识
    :param algorithm_type: 算法类型
    :param kwargs:
    :return:
    """
    start_time = time.time()
    content = dict(studyInstanceUID=study_instance_uid, seriesInstanceUID=series_instance_uid,
                   algorithmType=algorithm_type)
    series_dcm_dir = os.path.join(Config.DIR_DCM, series_instance_uid)
    file_list = FileUtils.get_all(series_dcm_dir)
    if not file_list:
        content.update({"percent": 500, "errorCode": RetCode.PLATFORM_SERIES_NOT_FOUND.code})
        RabbitProducer.send(content)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return False
    series_result_dir = os.path.join(Config.DIR_ROOT, "aspects", study_instance_uid, series_instance_uid)
    FileUtils.rebuild_dirs(series_result_dir)
    static_output_dir = os.path.join(Config.DIR_STATIC, study_instance_uid, series_instance_uid)
    FileUtils.rebuild_dirs(static_output_dir)
    RabbitProducer.send(dict(percent=10, **content))
    # 算法输入转换
    images, spacing = get_images(file_list)
    if images is None or spacing is None:
        content.update({"percent": 500, "errorCode": RetCode.PLATFORM_LOAD_IMAGES_FAILED.code})
        RabbitProducer.send(content)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return False
    RabbitProducer.send(dict(percent=30, **content))
    # Aspects计算
    aspect_runner = AspectRunner(study_instance_uid, series_instance_uid, images, spacing, series_result_dir,
                                 static_output_dir)
    result = aspect_runner.get_result()
    log.info("Aspects[study: {}, series: {}] > get result:{}".format(study_instance_uid, series_instance_uid, result))
    # 检查算法计算
    ret_code = result.get("retCode", RetCode.ASPECTS_ERROR.code)
    dcm_path = FileUtils.get_first(series_dcm_dir)
    report_dict = result.get("report")
    is_report_back = kwargs.get("report_back", True)
    if ret_code != RetCode.OK.code:
        log.info("Aspects[study: {}, series: {}] > failed to get aspect result".format(study_instance_uid, series_instance_uid))
        content.update({"percent": 500, "errorCode": ret_code})
        RabbitProducer.send(content)
        channel.basic_ack(delivery_tag=method.delivery_tag)
        return False
    RabbitProducer.send(dict(percent=70, **content))
    ReportHandler(dcm_path, report_dict, is_report_back)
    # 发送Aspects耗时
    run_time = result.get("runTime")
    aspect_consumer_time = round(run_time, 2)
    RabbitProducer.send(dict(percent=80, consumerTime=aspect_consumer_time, **content))
    # 保存算法结果
    json_path = os.path.join(series_result_dir, "result.json")
    FileUtils.save_json(result.copy(), json_path)
    RabbitProducer.send(dict(percent=90, reportPath=static_output_dir, **content))
    # 保存算法结果
    text_info = result.get("result")
    text_path = os.path.join(series_result_dir, "text.json")
    FileUtils.save_json(text_info, text_path)
    # 回传通知
    RabbitProducer.send(dict(percent=95, BackReport=True, **content))
    # 算法任务完成
    RabbitProducer.send(dict(percent=100, result=json.dumps(text_info), errorCode=RetCode.OK.code, **content))
    channel.basic_ack(delivery_tag=method.delivery_tag)
    # 结束
    end_time = time.time()
    log.info("Aspects[study: {}, series: {}]  ASPECT: {:.5}s, Platform: {:.5}s, Total: {:.5}s".format(
        study_instance_uid, series_instance_uid, run_time, (end_time - start_time - run_time), (end_time - start_time)))
    log.info("********************** success **********************")
    return True


def get_images(file_list):
    is_success, series_count, images, locations, pixel_spacing = DicomUtils.get_images_by_sitk(file_list)
    if not is_success:
        is_success, series_count, images, locations, pixel_spacing = DicomUtils.get_images_by_pydicom(file_list)
        if not is_success:
            return None, None
    file_size = len(file_list)
    location_size = len(locations)
    log.info("Aspects > series count:{}, images:{}, locations:{}".format(series_count, file_size, location_size))
    if series_count != 1 or len(images) != file_size or location_size != file_size:
        return None, None
    ind = np.argsort(locations)
    locations = np.array(np.sort(locations))
    slice_thicknesses = locations[1:] - locations[:-1]
    if np.std(slice_thicknesses) >= np.mean(slice_thicknesses) / 5:
        log.info("slices not uniformly spaced, {}".format(slice_thicknesses))
        return None, None
    images = [images[i] for i in ind]
    images = np.array(images)
    slice_thickness = np.mean(slice_thicknesses)
    spacing = list(pixel_spacing) + [slice_thickness]
    return images, spacing
