#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : uguard_aspects
<AUTHOR> mingxing
@Date    : 2022/7/18 13:00
"""
import os

import pydicom.uid

from lib.common import DicomUtils, FileUtils, WebApi
from lib.const import Config, Consts
from lib.logger import log


class ReportHandler:

    def __init__(self, original_dcm, report_dict, return_back=True):
        self.dataset_template = DicomUtils.read(original_dcm)
        self.study_instance_uid = self.dataset_template.StudyInstanceUID
        self.series_instance_uid = self.dataset_template.SeriesInstanceUID
        self.output = F"{Config.DIR_STATIC}/{self.study_instance_uid}/{self.series_instance_uid}"
        self.series_uid_list = []
        self.dcm_list = []
        self.return_back = return_back
        original_series_number = 100
        config_data = WebApi.get_config(Consts.CODE_ALGORITHM_RESULT_MODALITY)
        result_modality = config_data[0]["value"] if config_data else Consts.DEFAULT_ALGORITHM_RESULT_MODALITY
        for report_type in report_dict:
            jpg_dir = report_dict.get(report_type)
            parent_dirname = os.path.basename(os.path.abspath(os.path.join(jpg_dir, "..")))
            series_desc = parent_dirname.replace("_", " ")
            series_uid = pydicom.uid.generate_uid()
            dcm_dir = os.path.join(self.output, parent_dirname, "dcm")
            log.info("report[study: {}, series:{}] > {}:{}, desc:{}, dcm output:{}".format(self.study_instance_uid, self.series_instance_uid, report_type, jpg_dir, series_desc, dcm_dir))
            FileUtils.create_dirs(dcm_dir)

            for filename in sorted(os.listdir(jpg_dir)):
                jpg_path = os.path.join(jpg_dir, filename)
                instance_number = int(FileUtils.get_filename_without_suffix(filename))
                dataset = DicomUtils.jpg2dcm(self.dataset_template, jpg_path, series_uid, series_desc,
                                             instance_number, original_series_number, result_modality)
                path = os.path.join(dcm_dir, "{}.dcm".format(instance_number))
                dataset.save_as(path)
                self.dcm_list.append(path)
            original_series_number += 1
            self.series_uid_list.append(series_uid)
        log.info("Aspects[study: {}, series:{}] > find {} series, {} dcm".format(self.study_instance_uid, self.series_instance_uid, len(self.series_uid_list), len(self.dcm_list)))

    # def upload(self):
    #     log.info("Aspects > upload {} dcm".format(len(self.dcm_list)))
    #     for dcm_path in self.dcm_list:
    #         is_succ = OrthancApi.upload_image(dcm_path)
    #         log.info("send {} : {}".format(os.path.basename(dcm_path), is_succ))
    #         if not is_succ:
    #             return False
    #     return True
    #
    # def postback(self):
    #     log.info("ASPECT > series:{}, report back: {}".format(self.series_instance_uid, self.return_back))
    #     if self.return_back:
    #         self.__get_config()
    #         threading.Thread(target=self.__send).start()

    # def __get_config(self):
    #     self.__target_aet = ""
    #     pacs_list = WebApi.find_pacs_server()
    #     if len(pacs_list) > 0:
    #         target_pacs = next((pacs for pacs in pacs_list if pacs.get("alie_name", "") == Consts.PACS_TARGET), None)
    #         if not target_pacs:
    #             return
    #         self.__target_host = target_pacs.get("ip_address")
    #         self.__target_port = int(target_pacs.get("port"))
    #         self.__target_aet = target_pacs.get("aet")
    #     self.__can_send_summary = True
    #     self.__back_method = Consts.REPORT_BACK_METHOD_ORTHANC
    #     config_list = WebApi.get_config(["reportAspectsSummary", "reportBackMethod"])
    #     if len(config_list) > 0:
    #         for conf in config_list:
    #             code = conf.get("code")
    #             value = conf.get("value")
    #             _format = conf.get("format", "")
    #             if code == "reportAspectsSummary":
    #                 self.__can_send_summary = StringUtils.to_bool(value)
    #                 continue
    #             if code == "reportBackMethod":
    #                 self.__back_method = value

    # def __send(self):
    #     if not self.__can_send_summary:
    #         log.warning("cancel report back, aspects summary:{}".format(self.__can_send_summary))
    #         return
    #     if not self.__target_aet:
    #         log.warning("cancel report back, target pacs not found")
    #         return
    #     log.info("back method: {}".format(self.__back_method))
    #     if self.__back_method == Consts.REPORT_BACK_METHOD_ORTHANC:
    #         resources = []
    #         for series_uid in self.series_uid_list:
    #             resources.append({"StudyInstanceUID": self.study_instance_uid, "SeriesInstanceUID": series_uid})
    #         data = {"Level": "SERIES", "TargetAet": self.__target_aet, "Timeout": 60, "Resources": resources}
    #         OrthancApi.move(data)
    #         return
    #     PacsUtils.send_batch_dcm(file_list=self.dcm_list, local_act=Config.LOCAL_AET, target_host=self.__target_host,
    #                              target_port=self.__target_port, target_aet=self.__target_aet)

    # def finish(self):
    #     if self.upload():
    #         self.postback()
