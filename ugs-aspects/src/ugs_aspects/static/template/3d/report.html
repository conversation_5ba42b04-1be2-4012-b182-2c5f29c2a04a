<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body{
            margin: 0;
            padding: 0;
        }
        p{
            margin: 0;
            padding: 0;
        }
        .box{
            width: 1189px;
            height: 681px;
            background-image: url('./icon/img.png');
            background-size: contain;
            background-repeat: no-repeat;
            position: relative;
            background-color: #000;
        }
        .wrap{
            position: absolute;
            top: 67px;
            right: 30px;
        }
        .databox{
            display: flex;
        }
        .explain{
            font-size: 16px;
            font-family: Noto Sans Mono CJK SC;
            font-weight: 500;
            color: #FAFD53;
            /* position:absolute;
            right: 234px;
            bottom: 46px; */
        }
        .right,.left{
            width: 244px;
            height: 535px;
            background-color: #000;
            opacity: 0.4;
        }
        .left{
            margin-left: 2px;
        }
        .header{
            height: 89px;
            background: #29313E;
            /* display: flex;
            flex-direction: column;
            align-items: center; */
            /* border: 2px solid #000;
            border-bottom: 0; */

        }
        .iconBox,.headerNum{
            flex: 50%;
            line-height: 44px;
            font-family: Noto Sans Mono CJK SC;
            font-weight: 500;
        }
        .iconBox{
            height: 38px;
            font-size: 21px;
            color: #FFFFFF;
            text-align: center;

        }
        .icon{
            display: inline-block;
            width: 19px;
            height: 19px;
        }
        .icon img{
            width: 100%;
            height: 100%;
        }
        .headerNum{
            font-size: 39px;
            color: #18C2CD;
            text-align: center;
        }
        table{
            width: 100%;
            border-collapse:collapse;
            /* text-align: center; */
            border: 0;
        }
        thead{
            width: 100%;
            height: 23px;
            background: #171F2C;
            font-size: 15px;
            font-family: Noto Sans Mono CJK SC;
            font-weight: 500;
            color: #748994;
        }
        tbody tr{
            height: 40px;
        }
        tbody tr :first-child{
            /* padding-left: 40px; */
            color: #fff;
        }
        tbody tr :last-child{
            text-align: center;
        }
        tr{
            background: #171F2C;
            border: 2px solid #000;
            border-left: 0;
            border-right:0;
        }
       thead td{
            text-align: center;
            font-size: 15px;
            font-family: Noto Sans Mono CJK SC;
            font-weight: 500;
            color: #748994;
        }
        .num{
            display: inline-block;
            width: 90px;
            height: 28px;
            line-height: 28px;
            /* background: #E02B33; */
            font-size: 18px;
            font-family: Noto Sans Mono CJK SC;
            font-weight: 500;
            color: #FFFFFF;
        }
        .block{
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-right: 7px;
            margin-left: 40px;
        }
    </style>
</head>
<body>
    <div class="box">
        <div class="wrap">
        <div class="databox">
            <div class="right" id="right">
                <div class="header">
                    <div class="iconBox">
                        <span class="icon">
                            <img src="icon/right.png" alt="">
                        </span>
                        <span class="headerText">右脑</span>
                    </div>
                    <div class="headerNum">
                        @rightNumber@
                    </div>
                </div>
                <table>
                    <thead>
                        <tr>
                            <td style="width: 108px;">部位</td>
                            <td>平均CT值(HU)</td>
                        </tr>

                    </thead>
                    <tbody id="rightDataBox">
                        <!-- <tr >
                            <td><span class="block" style="background-color: #E02B33;"></span>C</td>
                            <td><span class="num">34.5</span></td>
                        </tr> -->
                    </tbody>
                </table>
            </div>
            <div class="left" id="left">
                <div class="header">
                    <div class="iconBox">
                        <span class="icon">
                            <img src="icon/left.png" alt="">
                        </span>
                        <span class="headerText">左脑</span>
                    </div>
                    <div class="headerNum">
                        @leftNumber@
                    </div>
                </div>
                <table>
                    <thead>
                        <tr>
                            <td style="width: 108px;">部位</td>
                            <td>平均CT值(HU)</td>
                        </tr>

                    </thead>
                    <tbody id="leftDataBox">
                        <!-- <tr >
                            <td><span class="block" style="background-color: #E02B33;"></span>C</td>
                            <td><span class="num">34.5</span></td>
                        </tr> -->
                    </tbody>
                </table>
            </div>
        </div>
        <p class="explain">* 本结果采用3D分区方法，结果仅供参考。</p>
    </div>

    </div>
    <script>
        // 右脑数据格式
        // @rightData@
        var rightData =
        [
            {
                color:'#E02B33', // 固定不用管
                name:'C', // 固定不用管 部位
                data:@right_C_data@, // 平均CT值
                abnormal:@right_C_abnormal@, // 是否红色底色
            },
            {
                color:'#09941A',
                name:'L',
                data:@right_L_data@,
                abnormal:@right_L_abnormal@,
            },
            {
                color:'#7FC299',
                name:'IC',
                data:@right_IC_data@,
                abnormal:@right_IC_abnormal@,
            },
            {
                color:'#D07F35',
                name:'I',
                data:@right_I_data@,
                abnormal:@right_I_abnormal@,
            },
            {
                color:'#C9CB58',
                name:'M1',
                data:@right_M1_data@,
                abnormal:@right_M1_abnormal@,
            },
            {
                color:'#1ECC33',
                name:'M2',
                data:@right_M2_data@,
                abnormal:@right_M2_abnormal@,
            },
            {
                color:'#2549C8',
                name:'M3',
                data:@right_M3_data@,
                abnormal:@right_M3_abnormal@,
            },
            {
                color:'#A1373E',
                name:'M4',
                data:@right_M4_data@,
                abnormal:@right_M4_abnormal@,
            },
            {
                color:'#7E2488',
                name:'M5',
                data:@right_M5_data@,
                abnormal:@right_M5_abnormal@,
            },
            {
                color:'#543BA5',
                name:'M6',
                data:@right_M6_data@,
                abnormal:@right_M6_abnormal@,
            }
        ]

        // 左脑数据格式
        // @leftData@
        var leftData =
        [
            {
                color:'#E02B33',
                name:'C',
                data:@left_C_data@,
                abnormal:@left_C_abnormal@,
            },
            {
                color:'#09941A',
                name:'L',
                data:@left_L_data@,
                abnormal:@left_L_abnormal@,
            },
            {
                color:'#7FC299',
                name:'IC',
                data:@left_IC_data@,
                abnormal:@left_IC_abnormal@,
            },
            {
                color:'#D07F35',
                name:'I',
                data:@left_I_data@,
                abnormal:@left_I_abnormal@,
            },
            {
                color:'#C9CB58',
                name:'M1',
                data:@left_M1_data@,
                abnormal:@left_M1_abnormal@,
            },
            {
                color:'#1ECC33',
                name:'M2',
                data:@left_M2_data@,
                abnormal:@left_M2_abnormal@,
            },
            {
                color:'#2549C8',
                name:'M3',
                data:@left_M3_data@,
                abnormal:@left_M3_abnormal@,
            },
            {
                color:'#A1373E',
                name:'M4',
                data:@left_M4_data@,
                abnormal:@left_M4_abnormal@,
            },
            {
                color:'#7E2488',
                name:'M5',
                data:@left_M5_data@,
                abnormal:@left_M5_abnormal@,
            },
            {
                color:'#543BA5',
                name:'M6',
                data:@left_M6_data@,
                abnormal:@left_M6_abnormal@,
            }
        ]


        var rightHtmlStr = ''
        var leftHtmlStr = ''
        var rightDataBox = document.getElementById('rightDataBox')
        var leftDataBox = document.getElementById('leftDataBox')
        rightData.forEach(item => {
            rightHtmlStr += `<tr>
                            <td><span class="block" style="background-color: ${item.color};"></span>${item.name}</td>
                            <td><span class="num" style="background-color: ${item.abnormal ? '#E02B33':''};">${item.data}</span></td>
                        </tr>`
        })
        leftData.forEach(item => {
            leftHtmlStr += `<tr>
                            <td><span class="block" style="background-color: ${item.color};"></span>${item.name}</td>
                            <td><span class="num" style="background-color: ${item.abnormal ? '#E02B33':''};">${item.data}</span></td>
                        </tr>`
        })

        rightDataBox.innerHTML = rightHtmlStr
        leftDataBox.innerHTML = leftHtmlStr

        function clickEvent(type){
            console.log(type);
        }
        var rightmind = @rightmind@ // 控制右脑是否高亮
        var right = document.getElementById('right')
        var left = document.getElementById('left')
        rightmind? right.style.opacity = 1 :left.style.opacity = 1
        right.addEventListener('click',(e)=>{
            right.style.opacity = 1
            left.style.opacity = 0.4
        })
        left.addEventListener('click',(e)=>{
            left.style.opacity = 1
            right.style.opacity = 0.4
        })
    </script>
</body>
</html>