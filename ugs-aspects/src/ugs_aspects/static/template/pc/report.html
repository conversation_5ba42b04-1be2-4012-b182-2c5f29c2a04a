<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body{
            margin: 0;
            padding: 0;
        }
        .box{
            width: 1189px;
            height: 681px;
            background-color: #000;
            display: flex;
            color: #fff;
        }
        .left{
            width: 670px;
            height: 100%;
            /* display: grid;
            grid-template-columns: repeat(2,335px);
            grid-template-rows: repeat(3,194px); */
            padding-top: 36px;
           
        }
        .left div{
            display: flex;
        }
        .left img{
            width: 210px;
             margin: 0 auto;

        }
        table{
            width: 462px;
            text-align: center;
        }
        thead{
            height: 89px;
            background: #29313E;
            font-size: 21px;
            font-family: Noto Sans Mono CJK SC;
            font-weight: 500
        }
        tbody tr{
            font-size: 18px;
            font-family: Noto Sans Mono CJK SC;
            font-weight: 500;
            height: 65px;
            background: #171F2C;
        }
        td span{
            display: inline-block;
            width: 16px;
            height: 16px;
        }
        .title{
            padding-left: 25px;
            text-align: left;
        }
        .right{
            padding-top: 67px;
        }
    </style>
</head>
<body>
    <div class="box">
        <div class="left">
            <div>
                <img src="./image/1.png" alt="">
                <img src="./image/2.png" alt="">
            </div>
            <div>
                <img src="./image/3.png" alt="">
                <img src="./image/4.png" alt="">
            </div>
           <div>
                <img src="./image/5.png" alt="">
                <img src="./image/6.png" alt="">
           </div>
            
        </div>
        <div class="right">
            <table>
                <thead>
                    <tr>
                        <td style="width: 136px;"></td>
                        <td>右侧</td>
                        <td>左侧</td>
                    </tr>
                </thead>
                <tbody>
                    <tr style="height: 37px;background: #171F2C;color: #748994;font-size: 15px;">
                        <td>部位</td>
                        <td>平均CT值（HU）</td>
                        <td>平均CT值（HU）</td>
                    </tr>
                    <tr>
                        <td class="title"><span style="background-color: #F42831;"></span> 大脑后(O) </td>
                        <td id="rightO">rightO</td>
                        <td id="leftO">leftO</td>
                    </tr>
                    <tr>
                        <td class="title"><span style="background-color: #FF9636;"></span> 小脑(C) </td>
                        <td id="rightC">rightC</td>
                        <td id="leftC">leftC</td>
                    </tr>
                    
                    <tr>
                        <td class="title"><span style="background-color: #E5E84F"></span> 丘脑(T) </td>
                        <td id="rightT">rightT</td>
                        <td id="leftT">leftT</td>
                    </tr>
                    <tr>
                        <td class="title"><span style="background-color: #59EC92"></span> 中脑(M) </td>
                        <td id="MM" colspan="2">MM</td>
                    </tr>
                    <tr>
                        <td class="title"><span style="background-color: #359BFA"></span> 脑桥(P) </td>
                        <td id="PP" colspan="2">PP</td>
                    </tr>
                    <tr>
                        <td>总分</td>
                        <td id="NUM" colspan="2">NUM 分</td>
                    </tr>
                </tbody>
            </table>
            <p style="color: #FAFD53;">* 本结果为3D PC-ASPECT得分，仅作科研使用</p>
        </div>
    </div>
    <script>
        var showRed = ['PP','MM','NUM']
        if(showRed && showRed.length){
            showRed.forEach(item => {
                document.getElementById(item).style.backgroundColor = 'red'
            })
           
        }
    </script>
</body>
</html>