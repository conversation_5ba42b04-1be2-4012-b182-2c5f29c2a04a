<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body,p{
            margin: 0;
            padding: 0;
            background-color: black;
        }
        .box{
            width:890px;
            height: 631px;
            background-color: black;
            padding: 95px 0 0 200px;
        }
        .line1{
            display: flex;
        }
        canvas{
            width: 210px;
            height: 210px;
            margin-right: 78px;
        }
        .title{
            margin: 0;
            padding: 0;
            font-size: 18px;
            font-family: Noto Sans Mono CJK SC;
            font-weight: 500;
            color: #FFFFFF;
            margin-bottom: 28px;
        }
        .iphColor{
            display: inline-block;
            width: 18px;
            height: 12px;
        }
        .iph{
            display: inline-block;
            width: 160px;
            margin-left: 15px;
        }
        .right{
            font-size: 16px;
            font-family: Noto Sans Mono CJK SC;
            font-weight: 500;
            color: #FFFFFF;
        }
        .item{
            margin-bottom: 15px;
        }
        .text{
            margin-left: 27px;
        }
        .num{
            margin-left: 32px;
        }
        .line2{
            font-size: 14px;
            font-family: Noto Sans Mono CJK SC;
            font-weight: 500;
            color: #FFFFFF;
            margin-left: 133px;
            margin-top: 123px;
        }
        .line2 p {
            margin-bottom: 13px;
        }
    </style>
</head>
<body>
    <div class="box">
        <div class="line1">
            <canvas id="canvasImage"  width="210" height="210"></canvas>
            <div class="right">
                <p class="title">ICH Subtypes</p>
                <div class="item">
                    <span class="iphColor" style="background-color: #FF66A7;"></span>
                    <span class="iph">脑实质出血(IPH):</span>
                    <span class="text">@IPH@</span>
                    <span class="num" id="iphId"></span> ml
                </div>
                <div class="item">
                    <span class="iphColor" style="background-color: #FEDC7A;"></span>
                    <span class="iph">脑室内出血(IVH): </span>
                    <span class="text">@IVH@</span>
                    <span class="num"  id="ivhId"></span> ml
                </div>
                <div class="item">
                    <span class="iphColor" style="background-color: #65ECA0;"></span>
                    <span class="iph">蛛网膜下腔出血(SAH):</span>
                    <span class="text">@SAH@</span>
                    <span class="num"  id="sahId"></span> ml
                </div>
                <div class="item">
                    <span class="iphColor" style="background-color: #7ED7FF;"></span>
                    <span class="iph">硬膜下出血(SDH):</span>
                    <span class="text">@SDH@</span>
                    <span class="num"  id="sdhId"></span> ml
                </div>
                <div class="item">
                    <span class="iphColor" style="background-color: #6B609A;"></span>
                    <span class="iph">硬膜外出血(EDH):</span>
                    <span class="text">@EDH@</span>
                    <span class="num"  id="edhId"></span> ml
                </div>
            </div>
        </div>
        <!-- <div class="line2">
            <p>疑似出血类型：</p>
            <p>脑实质出血(IPH): <span>@iphNum@</span> ml</p>
            <p>脑室内出血(IVH): <span>@ivhNum@</span> ml</p>
            <p>蛛网膜下腔出血(SAH): <span>@sahNum@</span> ml</p>
            <p>硬膜下出血(SDH): <span>@sdhNum@</span> ml</p>
            <p>硬膜外出血(EDH): <span>@edhNum@</span> ml</p>
        </div> -->
    </div>
    <script>
        // var iphNumber = 20;
        // var ivhNumber = 43;
        // var sahNumber = 32;
        // var sdhNumber = 21;
        // var edhNumber = 44;

        var iphNumber = @iphNum@;
        var ivhNumber = @ivhNum@;
        var sahNumber = @sahNum@;
        var sdhNumber = @sdhNum@;
        var edhNumber = @edhNum@;

        document.getElementById('iphId').innerText = iphNumber;
        document.getElementById('ivhId').innerText = ivhNumber;
        document.getElementById('sahId').innerText = sahNumber;
        document.getElementById('sdhId').innerText = sdhNumber;
        document.getElementById('edhId').innerText = edhNumber;

        var totle = iphNumber + ivhNumber + sahNumber + sdhNumber + edhNumber;
        var iph = iphNumber / totle * Math.PI * 2;
        var ivh = ivhNumber / totle * Math.PI * 2;
        var sah = sahNumber / totle * Math.PI * 2;
        var sdh = sdhNumber / totle * Math.PI * 2;
        var edh = edhNumber / totle * Math.PI * 2;

        var canvas = document.getElementById('canvasImage');
        var context = canvas.getContext('2d');
        var width = canvas.clientWidth;
        var height = canvas.clientHeight;
        var startRadian = 0;
        if (iph != 0) {
            context.beginPath();
            context.moveTo(width/2, height/2);
            context.arc(width/2, height/2, width/2, startRadian, startRadian+iph);
            context.fillStyle = '#FF66A7'
            context.fill();
            startRadian = startRadian + iph;
        }
        if (ivh != 0) {
            context.beginPath();
            context.moveTo(width/2, height/2);
            context.arc(width/2, height/2, width/2, startRadian, startRadian+ivh);
            context.fillStyle = '#FEDC7A'
            context.fill();
            startRadian = startRadian + ivh;
        }
        if (sah != 0) {
            context.beginPath();
            context.moveTo(width/2, height/2);
            context.arc(width/2, height/2, width/2, startRadian, startRadian+sah);
            context.fillStyle = '#65ECA0'
            context.fill();
            startRadian = startRadian + sah;
        }
        if (sdh != 0) {
            context.beginPath();
            context.moveTo(width/2, height/2);
            context.arc(width/2, height/2, width/2, startRadian, startRadian+sdh);
            context.fillStyle = '#7ED7FF'
            context.fill();
            startRadian = startRadian + sdh;
        }
        if (edh != 0) {
            context.beginPath();
            context.moveTo(width/2, height/2);
            context.arc(width/2, height/2, width/2, startRadian, startRadian+edh);
            context.fillStyle = '#6B609A'
            context.fill();
            startRadian = startRadian + sdh;
        }
    </script>           
</body>
</html>