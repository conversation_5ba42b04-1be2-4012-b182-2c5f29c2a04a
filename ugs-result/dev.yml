version: "3"
services:
  ugs-result:
    container_name: "ugs-result"
    image: 172.16.30.132:808/ugs/backend:0.0.4
    volumes:
      - ./src/ugs_result:/ugs_result
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    working_dir: /ugs_result/
    env_file:
      - .env
    command: python3 -u consumer.py
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true