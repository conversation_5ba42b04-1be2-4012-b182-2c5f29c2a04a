version: "3"
services:
  ugs-result:
    container_name: "ugs-result"
    image: harbor.unionstrongtech.com/ugs/backend:0.0.4
    volumes:
      - ./dist:/ugs_result/dist
      - /data/ctpdata:/data/ctpdata
      - /usr/share/zoneinfo:/usr/share/zoneinfo
    working_dir: /ugs_result/
    env_file:
      - .env
    command: bash -c "pip install --force-reinstall dist/*.whl && ugsresult"
    restart: always
    networks:
      - uguardstroke_default
networks:
  uguardstroke_default:
    external: true
