#! /usr/bin/env python
"""
定义了一个回调函数callback,用于处理接收到的消息:
# 解析消息内容
# 更新算法版本信息
# 记录算法耗时
# 处理特征图和报告
# 更新算法任务进度
# 通知算法进度
# 回调GE接口(如果配置允许)
# 处理算法结果(CTP、CTA、ASPECTS)
# 将结果存储到MongoDB和MySQL
# 发送邮件通知
# 回传结果到PACS系统(如果配置允许)
"""
import datetime
import json
import os
import sys
import threading
import time
import traceback
import socket
import uuid
import pika
from bson.objectid import ObjectId

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(BASE_DIR)

from utils.code import Const, Env
from utils.mongodb import MongoDB
from utils.rabbitmq import RabbitMQProducer
from utils.logger import MyLogger
from utils.mysql import MySQLClient
from utils.service import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FeatureMap<PERSON>and<PERSON>, ReportHandler, BackReport, MailHandler, AlgorithmResultHandler
from utils.models import RecordTimeModel, AlgorithmTaskModel, SeriesModel, StudyModel, AlgorithmResultModel, \
    ConfigModel, SeriesSplitModel
from utils.thirdparty_ge import begin_request_GE, get_algorithm_textresult
import package_info as pi

log = MyLogger()


def callback(channel, method, properties, body):
    """
    :param channel:
    :param method:
    :param properties:
    :param body:
    :return:{
    'result':{},
    'error':None,
    'server':'************',
    'module':'mysql',
    'task_code':'uuid',
    'ip':'************',
    'start_time':'2018-2-11 12:30:00',
    'type':'once'
    }
    """
    log.info("result consumer > receive message: {}".format(body.decode("utf-8")))
    mysql_client = None
    try:
        mysql_client = MySQLClient()
        body = json.loads(body)
        toshiba = body.get("toshiba", False)
        algorithm_type = body.get("algorithmType", '')
        is_toshiba_ctp = (toshiba and algorithm_type == "ctp")
        log.info("result consumer > toshiba ctp series: {}, toshiba:{}, algorithm_type：{}".format(
            is_toshiba_ctp, toshiba, algorithm_type))
        study_instance_uid = body.get("studyInstanceUID", '')
        series_instance_uid = body.get('seriesInstanceUID', '')
        series_instance_uids = body.get('seriesInstanceUIDs', '')
        al_result = body.get('result', '')
        index = body.get('index', 0)
        percent = body.get('percent', 0)
        error_code = body.get("errorCode", 0)
        consumer_time = body.get("consumerTime")
        version = body.get("version", "")
        server_type = body.get("serverType", "")
        feature_map = body.get("featureMap", "")
        report_path = body.get("reportPath", "")
        # 回传相关参数
        back_report = body.get("BackReport")
        manual_return = body.get("manualReturn", False)
        # 更新版本信息
        if version and server_type:
            VersionHandler(mysql_client).create_update_version(server_type=server_type, version=version)
            log.info("result consumer > update version, server_type: {}, version:{}".format(server_type, version))

        # 记录算法耗时
        if consumer_time:
            record_time_obj = RecordTimeModel(uuid=uuid.uuid1(), category=algorithm_type, consume_time=consumer_time,
                                              series_instance_uid=study_instance_uid if is_toshiba_ctp else series_instance_uid,
                                              create_time=datetime.datetime.now())
            mysql_client.insert(record_time_obj)
            log.info("result consumer > save consume time:{}".format(consumer_time))

        if feature_map:
            log.info("feature map: {}".format(feature_map))
            FeatureMapHandler(mysql_client).handle(study_instance_uid, feature_map)

        if report_path:
            series_uid_list = series_instance_uids if is_toshiba_ctp else [series_instance_uid]
            ReportHandler(mysql_client, study_instance_uid, series_uid_list, algorithm_type, report_path).save_report()

        if percent:
            ProgressHandler(mysql_client, channel, algorithm_type, body).handle()

        if al_result:
            result_handler = AlgorithmResultHandler(
                mysql_client,
                study_instance_uid,
                series_instance_uid,
                series_instance_uids,
                algorithm_type,
                is_toshiba_ctp,
                toshiba,
                body
            )
            result_handler.handle()

        # 回传到pacs
        if back_report:
            # TODO 封装到class，简化服务主流程
            config_list = mysql_client.select(ConfigModel(code="canReportBack"))
            can_report_back = eval(config_list[0]["value"]) if config_list else False
            if manual_return or can_report_back:
                query_series_uid = series_instance_uids if is_toshiba_ctp else series_instance_uid
                if not study_instance_uid:
                    series_list = mysql_client.select(SeriesModel(id=None, series_instance_uid=query_series_uid))
                    log.info("BackReport > search study, series:{}".format(series_list))
                    study_list = mysql_client.select(StudyModel(id=series_list[0]["study_id"], study_instance_uid=None))
                    study_instance_uid = study_list[0]["study_instance_uid"]
                code = BackReport(mysql_client, study_instance_uid, series_instance_uid, algorithm_type, toshiba,
                                  manual_return).send()
                if code:
                    log.info(f"BackReport > post back code: {code}")
    except:
        log.error("result > error:{}".format(traceback.format_exc()))
    finally:
        if mysql_client:
            mysql_client.close()


def main():
    log.info("waiting for message To exit press CTRL+C, ugs-result:{}".format(pi.version))
    try:
        mysql_client = MySQLClient()
        VersionHandler(mysql_client).create_update_version(server_type="ugs-result", version=pi.version)
        mysql_client.close()
    except:
        log.info(f"update version error:{traceback.format_exc()}")
    while True:
        try:
            credentials = pika.PlainCredentials(Env.MQ_USERNAME, Env.MQ_PASSWORD)
            connection = pika.BlockingConnection(
                pika.ConnectionParameters(Env.MQ_HOST, Env.MQ_PORT, '/', credentials, heartbeat=0))
            conn_socket = connection._impl.socket
            conn_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            channel = connection.channel()
            channel.exchange_declare(exchange="ws_topic", exchange_type="topic", durable=True)
            queue_name = "algorithm_ctp_aspects_result"
            channel.queue_declare(queue=queue_name, durable=True)
            channel.basic_consume(callback, queue=queue_name, no_ack=True)
            channel.start_consuming()
        except:
            log.error(f"result consumer connection error:{traceback.format_exc()}")
        time.sleep(10)
        log.info(f"result consumer connection retry, please check connection")


if __name__ == '__main__':
    main()
