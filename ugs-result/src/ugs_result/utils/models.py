#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : models.py
@Project : ugs-backend
<AUTHOR> mingxing
@Date    : 2023/3/20 14:17
"""


class VersionModel:
    def __init__(self, uuid=None, server_type=None, version=None, timestamp=None, update_timestamp=None, is_delete=None):
        self.uuid = uuid
        self.server_type = server_type
        self.version = version
        self.timestamp = timestamp
        self.update_timestamp = update_timestamp
        self.is_delete = is_delete

    class Meta:
        db_name = "cloud"
        table_name = "version"
        id_name = "uuid"
        id_auto = False


class RecordTimeModel:
    def __init__(self, uuid=None, category=None, series_instance_uid=None, consume_time=None, create_time=None):
        self.uuid = uuid
        self.category = category
        self.series_instance_uid = series_instance_uid
        self.consume_time = consume_time
        self.create_time = create_time

    class Meta:
        db_name = "cloud"
        table_name = "record_time"
        id_name = "uuid"
        id_auto = False


class PacsServerModel:
    def __init__(self, id=None, aet=None, ip_address=None, port=None, alie_name=None):
        self.id = id
        self.aet = aet
        self.ip_address = ip_address
        self.port = port
        self.alie_name = alie_name

    class Meta:
        db_name = "cloud"
        table_name = "pacs_server"
        id_name = "id"
        id_auto = False


class MailConfigModel:
    def __init__(self, uuid=None, address=None, password=None, type=None, is_delete=None):
        self.uuid = uuid
        self.address = address
        self.password = password
        self.type = type
        self.is_delete = is_delete

    class Meta:
        db_name = "cloud"
        table_name = "mail_config"
        id_name = "uuid"
        id_auto = False


class MailAutoModel:
    def __init__(self, uuid=None, type=None, status=None, auto=None, method=None, is_delete=None):
        self.uuid = uuid
        self.type = type
        self.status = status
        self.auto = auto
        self.method = method
        self.is_delete = is_delete

    class Meta:
        db_name = "cloud"
        table_name = "mail_auto"
        id_name = "uuid"
        id_auto = False


class MailHistoryModel:
    def __init__(self, uuid=None, send_address=None, receive_address=None, send_time=None, send_status=None,
                 object_id=None, message=None, theme=None, is_delete=None, timestamp=None, update_timestamp=None):
        self.uuid = uuid
        self.send_address = send_address
        self.receive_address = receive_address
        self.send_time = send_time
        self.send_status = send_status
        self.object_id = object_id
        self.message = message
        self.theme = theme
        self.is_delete = is_delete
        self.timestamp = timestamp
        self.update_timestamp = update_timestamp

    class Meta:
        db_name = "cloud"
        table_name = "mail_history"
        id_name = "uuid"
        id_auto = False


class ConfigModel:
    def __init__(self, id=None, code=None, value=None, default_value=None, format=None, category=None, tag=None,
                 custom=None, description=None):
        self.id = id
        self.code = code
        self.value = value
        self.default_value = default_value
        self.format = format
        self.category = category
        self.tag = tag
        self.custom = custom
        self.description = description

    class Meta:
        db_name = "cloud"
        table_name = "t_config"
        id_name = "id"
        id_auto = False


class FeatureMapModel:
    def __init__(self, id=None, study_instance_uid=None, series_instance_uid=None, type=None, window_width=None,
                 window_level=None, path=None):
        self.id = id
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.type = type
        self.window_width = window_width
        self.window_level = window_level
        self.path = path

    class Meta:
        db_name = "cloud"
        table_name = "t_feature_map"
        id_name = "id"
        id_auto = False


class StudyModel:
    def __init__(self, id=None, study_instance_uid=None, patient_id=None):
        self.id = id
        self.study_instance_uid = study_instance_uid
        self.patient_id = patient_id

    class Meta:
        db_name = "cloud"
        table_name = "t_study"
        id_name = "id"
        id_auto = False


class SeriesModel:
    def __init__(self, id=None, series_instance_uid=None, modality=None, series_description=None, original_series=None, type=None,
                 study_id=None, orthanc_id=None, thumbnail_path=None, gmt_create=None, gmt_modified=None):
        self.id = id
        self.series_instance_uid = series_instance_uid
        self.thumbnail_path = thumbnail_path
        self.modality = modality
        self.series_description = series_description
        self.original_series = original_series
        self.type = type
        self.study_id = study_id
        self.orthanc_id = orthanc_id
        self.gmt_create = gmt_create
        self.gmt_modified = gmt_modified

    class Meta:
        db_name = "cloud"
        table_name = "t_series"
        id_name = "id"
        id_auto = False


class SeriesSplitModel:
    def __init__(self, id=None, series_instance_uid=None, original_series=None, image_number=None, type=None,
                 gmt_create=None, gmt_modified=None):
        self.id = id
        self.series_instance_uid = series_instance_uid
        self.original_series = original_series
        self.image_number = image_number
        self.type = type
        self.gmt_create = gmt_create
        self.gmt_modified = gmt_modified

    class Meta:
        db_name = "cloud"
        table_name = "t_series_split"
        id_name = "id"
        id_auto = False


class CallbackDicomModel:
    def __init__(self, uuid, sop_instance_uid, study_instance_uid=None, series_instance_uid=None, instance_number=None,
                 sop_orthanc_uuid=None, path=None, slice_thickness=None, protocol_name=None,
                 timestamp=None, updatetimestamp=None):
        self.uuid = uuid
        self.sop_instance_uid = sop_instance_uid
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.instance_number = instance_number
        self.sop_orthanc_uuid = sop_orthanc_uuid
        self.path = path
        self.slice_thickness = slice_thickness
        self.protocol_name = protocol_name
        self.timestamp = timestamp
        self.updatetimestamp = updatetimestamp

    class Meta:
        db_name = "cloud"
        table_name = "callback_dicom"
        id_name = "uuid"
        id_auto = False


class AlgorithmTaskModel:
    def __init__(self, uuid=None, series_uid=None, algorithm_type=None, finish_percent=None, error_code=None):
        self.uuid = uuid
        self.series_uid = series_uid
        self.algorithm_type = algorithm_type
        self.finish_percent = finish_percent
        self.error_code = error_code

    class Meta:
        db_name = "algorithm"
        table_name = "algorithm_task"
        id_name = "uuid"
        id_auto = False


class AlgorithmResultModel:
    def __init__(self, uuid=None, image_series=None, algorithm_type=None, algorithm_result=None, task_id=None,
                 index=None, create_time=None):
        self.uuid = uuid
        self.image_series = image_series
        self.algorithm_type = algorithm_type
        self.algorithm_result = algorithm_result
        self.task_id = task_id
        self.index = index
        self.create_time = create_time

    class Meta:
        db_name = "algorithm"
        table_name = "algorithm_result"
        id_name = "uuid"
        id_auto = False


