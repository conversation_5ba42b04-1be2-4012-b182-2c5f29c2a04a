#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : mongo
@Project : ugs-backend
<AUTHOR> mingxing
@Date    : 2023/3/21 12:49
"""

from bson.objectid import ObjectId
from pymongo import MongoClient

from utils.code import Env
from utils.logger import MyLogger

log = MyLogger()


class MongoDB(object):

    def __init__(self):

        self.client = MongoClient(Env.MONGODB_HOST, int(Env.MONGODB_PORT), username=Env.MONGODB_USER,
                                  password=Env.MONGODB_PASSWORD)
        self.db = self.client.erecord

    # Create方法
    def create(self, body, table):
        if not isinstance(table, str):
            table = str(table)
        if isinstance(body, dict):
            result = self.db[table].insert_one(body)
            return result
        if isinstance(body, list):
            result = self.db[table].insert_many(body)
            return result

    # Update方法,还需要添加对于条件的控制
    def update(self, body, table,
               condition, condition_action=None,
               is_many=False):
        if not isinstance(table, str):
            table = str(table)
        set = '$set'
        if condition_action:
            set = '${}'.format(condition_action)
        content = {set: body}
        if is_many:
            result = self.db[table].update_many(condition, content)
            return result
        result = self.db[table].update_one(condition, content)
        return result

    # Retrieve方法，进行查询操作
    def query(self, condition, table, condition_action=None):
        if not isinstance(table, str):
            table = str(table)
        if isinstance(condition, list):
            set = '{}'.format(condition_action)
            search = {set: {"$in": condition}}
            log.info("MongoDB[query] > {}".format(search))
            result = self.db[table].find(search)
            return result
        if condition_action:
            set = '{}'.format(condition_action)
            search = {set: condition}
            log.info("MongoDB[query] > {}".format(search))
            result = self.db[table].find(search)
            return result

    # Delete方法
    def delete(self, condition, table, is_many=False, count=0):
        if not isinstance(table, str):
            table = str(table)
        if isinstance(condition, dict):
            if not is_many:
                self.db[table].remove(condition)
            else:
                self.db[table].remove(condition, count)

    def bulk_delete(self, condition_list, table, condition_key):
        condition = {condition_key: {"$in": condition_list}}
        return self.db[table].remove(condition)

    def get(self, table, _id):
        return self.db[table].find_one({"_id": ObjectId(_id)})

    def find_one(self, query, collection):
        """
        查询单条记录

        Args:
            query (dict): 查询条件
            collection (str): 集合名称

        Returns:
            dict: 查询结果,如果没有找到返回 None
            
        Example:
            query = {
                "study_instance_uid": "1.2.3",
                "series_instance_uid": "4.5.6"
            }
            result = mongodb.find_one(query, "center_line")
        """
        try:
            collection = self.db[collection]
            return collection.find_one(query)
        except Exception as e:
            log.error(f"MongoDB find_one 查询失败: {str(e)}", exc_info=True)
            return None


if __name__ == '__main__':
    # a = MongoDB()
    # mongodb = MongoDB()
    # results = mongodb.query(ObjectId("5f647eba54e028001133ad5f"), 'record', '_id')
    # print(results)
    # if results:
    #    print("HHHHH")
    # for i in results:
    #     print(i.get("content"))
    mongodb = MongoDB()
    # mongodb.db=
    # record_object_id = query.record_detail_id
    results = mongodb.query(ObjectId("5f652fa65a4fee000136cfe2"), 'algorithm', '_id')
    for i in results:
        print(i.get("result"))