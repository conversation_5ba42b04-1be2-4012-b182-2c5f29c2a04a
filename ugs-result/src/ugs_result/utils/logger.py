#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import logging
import os
from concurrent_log_handler import ConcurrentRotatingFileHandler

_env = os.environ
LOG_DEBUG = _env.get("LOG_DEBUG", "1")
LOG_FILE_SIZE = _env.get("LOG_FILE_SIZE")

try:
    if LOG_FILE_SIZE and isinstance(LOG_FILE_SIZE, str):
        LOG_FILE_SIZE = int(LOG_FILE_SIZE)
    else:
        LOG_FILE_SIZE = 30
except:
    LOG_FILE_SIZE = 30


class MyLogger:
    """单例日志类"""
    _logger = None

    def __new__(cls, *args, **kwargs):
        if cls._logger is None:
            cls._logger = super().__new__(cls, *args, **kwargs)
            cls._logger = logging.getLogger("ugs-result")
            cls._logger.setLevel(logging.DEBUG if LOG_DEBUG == "1" else logging.INFO)
            formatter = logging.Formatter(
                "%(asctime)s[%(name)s][%(levelname)s][%(process)d-%(thread)d][%(filename)s-%(lineno)s]: %(message)s",
                "%Y-%m-%d %H:%M:%S")
            # console
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            cls._logger.addHandler(console_handler)
            # rolling file
            path = os.path.join("/data/ctpdata/log/ugs-result")
            if not os.path.exists(path):
                os.makedirs(path, exist_ok=True)
            log_path = F"{path}/ugs-result.log"
            rotating_file_handler = ConcurrentRotatingFileHandler(
                filename=log_path, maxBytes=1024 * 1024 * LOG_FILE_SIZE, backupCount=3, encoding='utf-8'
            )
            rotating_file_handler.setFormatter(formatter)
            cls._logger.addHandler(rotating_file_handler)
        return cls._logger
