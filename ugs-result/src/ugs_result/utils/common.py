# !/usr/bin/env python
# -*- coding: UTF-8 -*-
import os
import traceback

import requests

from utils.code import Env
from utils.logger import MyLogger

log = MyLogger()


class FileUtils:

    @staticmethod
    def get_one(dir_path):
        """
        获取目录下任意一个文件

        :param dir_path: 目录路径
        :return:
        """
        for root, dirs, files in os.walk(dir_path):
            if not files:
                return None
            for file in files:
                return os.path.join(dir_path, file)

    @staticmethod
    def get_filename_without_suffix(path):
        return os.path.splitext(path)[0]

    @staticmethod
    def get_all_file(path, suffix=""):
        """
        获取指定目录下的所有文件

        :param suffix:
        :param path: 文件路径
        :return: 文件列表
        """
        file_list = []
        sub_dir_list = []
        if os.path.isfile(path):
            file_list.append(path)
            return file_list
        sub_dir_list.append(path)
        while len(sub_dir_list):
            dir_path = sub_dir_list.pop()
            for name in sorted(os.listdir(dir_path)):
                file_path = os.path.join(dir_path, name)
                if os.path.isdir(file_path):
                    sub_dir_list.append(file_path)
                    continue
                if not suffix or (suffix and name.endswith(suffix)):
                    file_list.append(file_path)
        return file_list


class UgsApi:
    _BASE_URL = F"http://{Env.WEBAPI_HOST}:{Env.WEBAPI_PORT}"
    _TIMEOUT = 60

    @staticmethod
    def send_email(study_instance_uid, algorithm_type):
        url = F"{UgsApi._BASE_URL}/api/v1/mail/send/"
        data = dict(StudyUid=study_instance_uid, algorithm_type=algorithm_type, auto=True)
        try:
            response = requests.post(url=url, json=data, timeout=UgsApi._TIMEOUT)
            log.info("UgsApi[Post] > url:{}, data:{}, code:{}".format(url, data, response.status_code))
            return response.status_code == 200
        except:
            log.error("UgsApi[Post] > url:{}, data:{}, error:{}".format(url, data, traceback.format_exc()))
            return False
