#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : ge
@Project : ugs-backend
<AUTHOR> mingxing
@Date    : 2023/3/21 14:05
"""
import base64
import datetime
import json
import os
import threading
import traceback

import requests
from bson import ObjectId
from urllib.parse import quote

from utils.code import Const, AlgorithmErrorCodeMap
from utils.logger import MyLogger
from utils.models import AlgorithmTaskModel, StudyModel, AlgorithmResultModel
from utils.mongodb import MongoDB
from utils.mysql import MySQLClient

log = MyLogger()

ERROR_CODE = {
    50002: "未能完成计算，请检查数据格式",
    50003: "未能完成计算，请检查数据格式",
    50009: "未能完成计算，请检查数据格式",
}

_env = os.environ
# GE回调配置
GE_CALLBACK_HOST = _env.get('GE_CALLBACK_HOST', 'edison-ecm-service.default')
GE_CALLBACK_PORT = _env.get('GE_CALLBACK_PORT', '8020')
GE_CALLBACK_THIRD_PARTY_ORIGIN_ID = _env.get('GE_CALLBACK_THIRD_PARTY_ID', 2)
GE_CALLBACK_THIRD_PARTY_ID = int(GE_CALLBACK_THIRD_PARTY_ORIGIN_ID) if GE_CALLBACK_THIRD_PARTY_ORIGIN_ID else 2
LOCAL_WEB_SERVER_HOST = _env.get('LOCAL_WEB_SERVER_HOST', "***********")
LOCAL_WEB_SERVER_PORT = _env.get('LOCAL_WEB_SERVER_PORT', "4224")
LOCAL_API_SERVER_PORT = _env.get('CLOUD_PLATFORM_API_PORT', "4223")
STORAGE_DATA_URL = _env.get("STORAGE_DATA_URL", "http://ecm-backend.ecm:8020/third-party/v2/storageData")


def begin_request_GE(body, status, split_dicom_number):
    thread_ge = threading.Thread(target=ge_callback_ecm_result, args=(json.dumps(body), status, split_dicom_number))
    thread_ge.start()


def ge_callback_ecm_result(body, status, split_dicom_number):
    body = json.loads(body)
    log.info(f"GE结果通知 > body: {body}, status: {status}, split_dicom_number:{split_dicom_number}")
    if not isinstance(body, dict):
        log.info(f"body error: {body}")
        return 400

    toshiba = body.get("toshiba", False)
    algorithm_type = body.get("algorithmType", '')
    is_toshiba_ctp = (toshiba and algorithm_type == "ctp")
    log.info("result consumer > toshiba ctp series: {}".format(is_toshiba_ctp))
    study_instance_uid = body.get("studyInstanceUID", '')
    series_instance_uid = body.get('seriesInstanceUID', '')
    # series_instance_uids = body.get('seriesInstanceUIDs', '')
    al_result = body.get('result', '')
    # index = body.get('index', 0)
    # percent = body.get('percent', 0)
    error_code = body.get("errorCode", 0)
    # consumer_time = body.get("consumerTime")
    # version = body.get("version", "")
    # server_type = body.get("serverType", "")
    # feature_map = body.get("featureMap", "")
    if toshiba and algorithm_type == 'ctp':
        calcNo = study_instance_uid
        series_field = ""
    else:
        calcNo = f"{study_instance_uid},{series_instance_uid}"
        series_field = f";defaultSeriesUid={series_instance_uid}"
    data = {
        "thirdConfigId": GE_CALLBACK_THIRD_PARTY_ID,
        "calcNo": calcNo,
        "aiResultMsg": json.dumps({}),
        "processingStatus": status,
        "aiModel": algorithm_type
    }
    # 拆分序列请求信息
    if split_dicom_number > 0:
        data["aiSeriesUid"] = series_instance_uid
        data["dicomCount"] = split_dicom_number
        data["nominalPercentageOfCardiacPhase"] = ""

    if status == '1':
        mysql_client = MySQLClient()
        study_list = mysql_client.select(StudyModel(study_instance_uid=study_instance_uid))
        if not study_list:
            log.error(f"GE storageData study_instance_uid: {study_instance_uid} not esists")
            data["requestUrl"] = ""

        data["aiResultMsg"] = json.dumps(al_result, ensure_ascii=False)
        receiveTime = study_list[0]["gmt_modified"] or datetime.datetime.now()
        receiveTime_str = receiveTime.strftime('%Y-%m-%d %H:%M:%S')
        data_bytes = receiveTime_str.encode("utf-8")
        encode_str = quote(base64.b64encode(data_bytes).decode("utf-8"))
        data["requestUrl"] = f"http://{LOCAL_WEB_SERVER_HOST}:{LOCAL_WEB_SERVER_PORT}/read/{study_instance_uid}/v2/{encode_str}{series_field}"
    else:
        erro_result = {error_code: AlgorithmErrorCodeMap.get(error_code, "")}
        data["aiResultMsg"] = json.dumps(erro_result, ensure_ascii=False)
    try:
        ge_storage_data(data)
    except:
        log.error(f"GE storageData error： {traceback.format_exc()}")


def ge_storage_data(data):
    """
    更新魔盒计算状态:  计算结果序列状态变化时回调接口
    请求示例
    {
        "thirdConfigId": 2,
        "calcNo": "",
        "aiResultMsg": "{\"degree\": \"xxx\", \"level\": \"0\"}", //JSON.toJSONString(Map<String, Object>)
        "processingStatus": "-1",
        "processingStatusMsg": "图像脏数据",
        "requestUrl": "http://xxx/xxx",
        "aiModel": "冠脉"
    }
    """
    headers = {"Content-Type": "application/json"}
    log.info(f"GE storageData api url:{STORAGE_DATA_URL}, data: {data}")
    try:
        response = requests.post(url=STORAGE_DATA_URL, json=data, headers=headers, timeout=30)
        log.info(f"GE[storageData] response_code：{response.status_code}, message: {response.content}")
        return response.status_code
    except:
        log.info(f"GE[storageData] request error: {traceback.format_exc()}")
        return 400


def get_algorithm_textresult(mysql_client, study_uid, series_uids, res):
    """
    获取算法计算结果，    抽离代码复用
    """
    for series_uid in series_uids:
        task_list = mysql_client.select(AlgorithmTaskModel(series_uid=series_uid))
        if not task_list:
            continue
        task = task_list[0]
        algorithm_type = task["algorithm_type"]
        finish_percent = task["finish_percent"]
        error_code = task["error_code"]
        # error_message = Algorithm_ERROR.get(finish_percent, "")
        if finish_percent > 100:
            log.info("algorithm task failed, finishPercent:{}, errorCode:{}".format(finish_percent, error_code))
            error_message = ERROR_CODE.get(error_code, "未能完成计算")
            res[algorithm_type] = dict(status=False, value=error_message,
                                       algorithm_state=Const.ALGORITHM_STATE_FAILURE,
                                       error_code=task.error_code)
            continue
        # 对于cta， 目前没有result结果文本，那么对应的algorithm_result也没有，那么就使用finish_percent=100 erro_code=0 来判断状态为True
        if finish_percent == 100 and algorithm_type == "cta" and error_code == 0:
            res["cta"] = dict(status=True, value="", algorithm_state=Const.ALGORITHM_STATE_SUCCESS)
            continue
        study_list = mysql_client.select(StudyModel(study_instance_uid=study_uid))
        if study_list[0]["toshiba"] and algorithm_type == "ctp":
            # 东芝灌注数据通过studyUID获取算法结果（Algorithm_result.image_series保存的是studyUID）
            result_list = mysql_client.select(AlgorithmResultModel(image_series=study_uid))
        else:
            # 正常数据通过任务获取算法结果（Algorithm_result.image_series保存的是seriesUID）
            result_list = mysql_client.select(AlgorithmResultModel(task_id=task["uuid"]))
        if algorithm_type == "ctp" and res["ctp"]["status"]:
            continue
        if not result_list:
            res[algorithm_type] = dict(status=False, value="",
                                       algorithm_state=Const.ALGORITHM_STATE_CALCULATING)
            continue
        log.info("study[{}] > algorithm_type:{}, series:{}".format(study_uid, algorithm_type, series_uid))
        mongo_id = result_list[0]["algorithm_result"]
        mongodb = MongoDB()
        results = mongodb.query(ObjectId(mongo_id), 'algorithm', '_id')
        if results:
            for result in results:
                algorithm_content = result.get('result', '')
                log.info("study[{}] > algorithm_type:{}, algorithm result:{}"
                         .format(study_uid, algorithm_type, algorithm_content))
                dict_check = json.loads(algorithm_content)
                if algorithm_type == 'aspects':
                    aspects_result = {}
                    if isinstance(dict_check, dict):
                        aspects_result["infarct_result"] = dict_check.get('infarct_result', None)
                        aspects_result["blood_location"] = dict_check.get("blood_location", None)
                        aspects_result["blood_volume"] = dict_check.get("blood_volume", None)
                        # 获取双侧评分
                        if "scoreLeftModify" in dict_check:
                            aspects_result["scoreLeft"] = dict_check.get("scoreLeftModify", "")
                            aspects_result["scoreRight"] = dict_check.get("scoreRightModify", "")
                        elif "scoreLeft" in dict_check:
                            aspects_result["scoreLeft"] = dict_check.get("scoreLeft", "")
                            aspects_result["scoreRight"] = dict_check.get("scoreRight", "")
                    else:
                        aspects_result["infarct_result"] = None
                        aspects_result["blood_location"] = None
                        aspects_result["blood_volume"] = None
                    if not aspects_result.get("blood_location", None):
                        del aspects_result["blood_location"]
                        del aspects_result["blood_volume"]
                    res["aspects"] = dict(status=True, value=aspects_result,
                                          algorithm_state=Const.ALGORITHM_STATE_SUCCESS)
                elif algorithm_type == 'ctp':
                    ctp_result = {}
                    ctp_result["result_affectedSide"] = dict_check.get("affectedSide", "")
                    ctp_result["result_cbf"] = round(dict_check.get("v_cbf", '')[0], 2) if dict_check.get(
                        "v_cbf", '') else 0
                    # 低灌注体积Tmax>6s取Tmax第3值
                    ctp_result["result_TMax"] = round(dict_check.get("v_TMax", '')[2], 2) if dict_check.get(
                        "v_TMax", 0) else 0
                    # 低灌和核心梗死不匹配体积
                    ctp_result["result_TMaxcbf"] = round(ctp_result["result_TMax"] - ctp_result["result_cbf"], 2)
                    if ctp_result["result_TMax"] and ctp_result["result_cbf"]:
                        try:
                            ctp_result["result_ratio"] = round(ctp_result["result_TMax"] / ctp_result["result_cbf"],
                                                               2)
                        except Exception as e:
                            ctp_result["result_ratio"] = "Inf"
                    else:
                        ctp_result["result_ratio"] = "Inf"
                    res["ctp"] = dict(status=True, value=ctp_result,
                                      algorithm_state=Const.ALGORITHM_STATE_SUCCESS)
                elif algorithm_type == 'cta':
                    cta_result = {}
                    try:
                        cta_result["cta_fraction"] = dict_check.get('cta_fraction')
                    except:
                        cta_result["cta_fraction"] = None
                    res["cta"] = dict(status=True, value=cta_result,
                                      algorithm_state=Const.ALGORITHM_STATE_SUCCESS)
    return res

