#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : service
@Project : ugs-backend
<AUTHOR> mingxing
@Date    : 2023/3/20 14:23
"""
import datetime
import hashlib
import json
import os
import traceback
import uuid
import traceback
from ast import literal_eval
from bson.objectid import ObjectId

import pydicom
import requests
from pydicom.uid import (ExplicitVRLittleEndian, ImplicitVRLittleEndian, ExplicitVRBigEndian,
                         DeflatedExplicitVRLittleEndian, JPEGBaseline, JPEGExtended, JPEGLosslessP14,
                         JPEG<PERSON><PERSON>ossless, JPEGLSLossy, JPEG2000<PERSON>ossless,
                         JPEG2000, JPEG2000MultiComponentLossless, JPEG2000MultiComponent, RLELossless)
from pynetdicom import AE, StoragePresentationContexts
from requests.auth import HTTPBasicAuth

from utils.mongodb import MongoDB
from utils.rabbitmq import RabbitMQProducer
from utils.thirdparty_ge import begin_request_GE, get_algorithm_textresult
from utils.code import Const, ALGORITHM_RESULT_DESC
from utils.common import FileUtils, UgsApi
from utils.logger import MyLogger
from utils.models import (AlgorithmResultModel, VersionModel, FeatureMapModel, AlgorithmTaskModel, SeriesModel, CallbackDicomModel,
                          StudyModel, PacsServerModel, ConfigModel, MailAutoModel, SeriesSplitModel)

_env = os.environ
log = MyLogger()

_transfer_syntax_uid = [
    ImplicitVRLittleEndian,
    ExplicitVRLittleEndian,
    ExplicitVRBigEndian,
    DeflatedExplicitVRLittleEndian,
    JPEGBaseline,
    JPEGExtended,
    JPEGLosslessP14,
    JPEGLSLossless,
    JPEGLSLossy,
    JPEG2000Lossless,
    JPEG2000,
    JPEG2000MultiComponentLossless,
    JPEG2000MultiComponent,
    RLELossless,
]


class VersionHandler:

    def __init__(self, mysql_client):
        self.mysql_client = mysql_client

    def create_update_version(self, server_type, version):
        """
        更新版本信息
        """
        try:
            version_obj = VersionModel(uuid=None, server_type=server_type, version=None)
            result_list = self.mysql_client.select(version_obj)
            if result_list:
                obj = VersionModel(uuid=result_list[0]["uuid"], server_type=None, version=version,
                                   update_timestamp=datetime.datetime.now())
                self.mysql_client.update(obj)
                log.info("The version update success. server_type:{}, version:{}".format(server_type, version))
            else:
                obj = VersionModel(uuid=uuid.uuid1(), server_type=server_type, version=version, is_delete=0,
                                   timestamp=datetime.datetime.now(), update_timestamp=datetime.datetime.now())
                self.mysql_client.insert(obj)
                log.info("The version create success. server_type:{}, version:{}".format(server_type, version))
        except Exception as e:
            log.error("update or create version error: %s", str(e))


class FeatureMapHandler:
    def __init__(self, mysql_client):
        self.mysql_client = mysql_client

    def handle(self, study_instance_uid, feature_map_dict):
        for _type in feature_map_dict:
            info = feature_map_dict.get(_type)
            window_width = info.get("windowWidth")
            window_level = info.get("windowLevel")
            nii_path = info.get("path")
            series_uid = info.get("seriesInstanceUID")
            feature_map_obj = FeatureMapModel(id=str(uuid.uuid1()), study_instance_uid=study_instance_uid,
                                              series_instance_uid=series_uid, type=_type,
                                              window_width=window_width, window_level=window_level, path=nii_path)
            self.mysql_client.insert(feature_map_obj)
            log.info("feature map[{}] > study:{}, series:{}, ww:{}, wl:{}, path:{} created".format(
                _type, study_instance_uid, series_uid, window_width, window_level, nii_path))
        log.info("study:{} handle feature map success".format(study_instance_uid))


class ReportHandler:
    def __init__(self, mysql_client, study_instance_uid, series_instance_uids, algorithm_type, report_dir):
        self.mysql_client = mysql_client
        self.study_instance_uid = study_instance_uid
        self.series_instance_uids = series_instance_uids
        self.algorithm_type = algorithm_type
        self.report_dir = report_dir
        study_list = self.mysql_client.select(StudyModel(id=None, study_instance_uid=study_instance_uid))
        self.study = study_list[0]
        log.info("ReportHandler[study:{}, series:{}] > algorithm_type:{}, report_dir:{}".format(
            self.study_instance_uid, self.series_instance_uids, self.algorithm_type, self.report_dir))

    def save_report(self):
        try:
            switcher = {Const.ALGORITHM_TYPE_ASPECTS: self.handle_aspects, Const.ALGORITHM_TYPE_CTA: self.handle_cta,
                        Const.ALGORITHM_TYPE_CTP: self.handle_ctp}
            switcher.get(self.algorithm_type)()
        except Exception as e:
            log.error("failed to save report:{}".format(traceback.format_exc()))
            task_obj = AlgorithmTaskModel(uuid=None, series_uid=self.series_instance_uids, algorithm_type=None,
                                          finish_percent=500, error_code=48000)
            self.mysql_client.update_by_conditions(task_obj, ["series_uid"])

    def handle_aspects(self):
        # 保存报告路径等信息到数据库
        if self.report_dir.startswith("/data/ctpdata/aspects"):
            report_path = os.path.join(self.report_dir, "report.dcm")
            thumbnail_path = os.path.join(self.report_dir, "report.jpg")
            if not os.path.exists(report_path) or not os.path.exists(thumbnail_path):
                log.info("study[{}], series[{}], report not found: {}".format(
                    self.study_instance_uid, self.series_instance_uids[0], self.report_dir))
                return
            series_uid = self.__save_image(report_path)
            _path = thumbnail_path.replace("/data/ctpdata", "")
            self.mysql_client.update_by_conditions(SeriesModel(id=None, series_instance_uid=series_uid,
                                                               thumbnail_path=_path), ["series_instance_uid"])
            return
        # 保存报告目录下所有图片路径等信息到数据库
        for series_desc in os.listdir(self.report_dir):
            series_dir = os.path.join(self.report_dir, series_desc)
            log.info("Aspects > series:{}".format(series_dir))
            dcm_dir = os.path.join(series_dir, "dcm")
            pic_dir = os.path.join(series_dir, "pic")
            if not os.path.exists(dcm_dir) or not os.path.exists(pic_dir):
                log.info("Aspects > image not found in {}".format(series_dir))
            series_uid = ""
            for filename in sorted(os.listdir(dcm_dir)):
                file_path = os.path.join(dcm_dir, filename)
                series_uid = self.__save_image(file_path)
            thumbnail_path = self.__get_thumbnail(pic_dir)
            _path = thumbnail_path.replace("/data/ctpdata", "")
            self.mysql_client.update_by_conditions(SeriesModel(id=None, series_instance_uid=series_uid,
                                                               thumbnail_path=_path), ["series_instance_uid"])

    def handle_cta(self):
        # CS MIP
        if self.report_dir.startswith("/data/ctpdata/static"):
            for series_desc in os.listdir(self.report_dir):
                series_dir = os.path.join(self.report_dir, series_desc)
                log.info("CTA > series:{}".format(series_dir))
                dcm_dir = os.path.join(series_dir, "dcm")
                pic_dir = os.path.join(series_dir, "pic")
                if not os.path.exists(dcm_dir) or not os.path.exists(pic_dir):
                    log.info("CTA > image not found in {}".format(series_dir))
                series_uid = ""
                for filename in sorted(os.listdir(dcm_dir)):
                    file_path = os.path.join(dcm_dir, filename)
                    series_uid = self.__save_image(file_path)
                thumbnail_path = self.__get_thumbnail(pic_dir)
                _path = thumbnail_path.replace("/data/ctpdata", "")
                self.mysql_client.update_by_conditions(SeriesModel(id=None, series_instance_uid=series_uid,
                                                                   thumbnail_path=_path), ["series_instance_uid"])
            return
        # VR
        for sub_dir in sorted(os.listdir(self.report_dir)):
            series_dir = os.path.join(self.report_dir, sub_dir)
            dcm_path = FileUtils.get_one(series_dir)
            if sub_dir.startswith("VR") or sub_dir.startswith("MIP"):
                series_uid = self.__save_series(dcm_path)
                _path = "/upixel/jpg/{}/{}/{}_180.jpg".format(self.study_instance_uid, self.series_instance_uids[0], sub_dir)
                self.mysql_client.update_by_conditions(SeriesModel(id=None, series_instance_uid=series_uid,
                                                                   thumbnail_path=_path), ["series_instance_uid"])
            # 侧支单图（1.7.0）
            if sub_dir.startswith("cs"):
                series_uid = self.__save_image(dcm_path)
                _path = "/cta/{}/{}/cs_mip.jpg".format(self.study_instance_uid, self.series_instance_uids[0])
                self.mysql_client.update_by_conditions(SeriesModel(id=None, series_instance_uid=series_uid,
                                                                   thumbnail_path=_path), ["series_instance_uid"])

    def handle_ctp(self):
        for sub_dir in sorted(os.listdir(self.report_dir)):
            series_dir = os.path.join(self.report_dir, sub_dir)
            series_uid = None
            dcm_list = sorted(os.listdir(series_dir))
            for dcm_name in dcm_list:
                dcm_path = os.path.join(series_dir, dcm_name)
                series_uid = self.__save_image(dcm_path)
            image_name = "img_{}.png".format(int(len(dcm_list) / 2) - 1)
            _path = os.path.join(os.path.abspath(F"{self.report_dir}/../png"), sub_dir, image_name)
            if not os.path.exists(_path):
                log.info("thumbnail not found: {}".format(_path))
                continue
            _path = _path.replace("/data/ctpdata", "")
            self.mysql_client.update_by_conditions(SeriesModel(id=None, series_instance_uid=series_uid,
                                                               thumbnail_path=_path), ["series_instance_uid"])

    def __save_image(self, dcm_path):
        """
        保存图片路径等信息到数据库
        """
        if not os.path.exists(dcm_path):
            log.error("{} not found".format(dcm_path))
            return ""
        dataset = pydicom.read_file(dcm_path, force=True)
        series_uid = dataset.SeriesInstanceUID
        series_list = self.mysql_client.select(SeriesModel(id=None, series_instance_uid=series_uid))
        if not series_list:
            series_description = dataset.SeriesDescription
            original_series_uid = "" if (self.study["toshiba"] and self.algorithm_type == Const.ALGORITHM_TYPE_CTP) or \
                series_description == "USC-UGuard CTP Total Summary" else self.series_instance_uids[0]
            self.mysql_client.insert(SeriesModel(
                id=str(uuid.uuid1()), series_instance_uid=series_uid, modality=dataset.Modality,
                series_description=dataset.SeriesDescription, original_series=original_series_uid,
                type=F"{self.algorithm_type}_ar", study_id=self.study["id"],
                gmt_create=datetime.datetime.now(), gmt_modified=datetime.datetime.now()))
        sop_uid = dataset.SOPInstanceUID
        dicom_list = self.mysql_client.select(CallbackDicomModel(uuid=None, sop_instance_uid=sop_uid))
        if not dicom_list:
            instance_number = 0
            try:
                instance_number = int(dataset.InstanceNumber) if dataset.InstanceNumber else 0
            except Exception:
                pass
            _path = ""
            if self.algorithm_type == Const.ALGORITHM_TYPE_ASPECTS or self.algorithm_type == Const.ALGORITHM_TYPE_CTP or Const.ALGORITHM_TYPE_CTA:
                _path = dcm_path.replace("/data/ctpdata", "")
            slice_thickness = dataset.SliceThickness if hasattr(dataset, "SliceThickness") else ""
            self.mysql_client.insert(CallbackDicomModel(
                uuid=str(uuid.uuid1()), sop_instance_uid=sop_uid, study_instance_uid=self.study_instance_uid,
                series_instance_uid=series_uid, instance_number=instance_number, sop_orthanc_uuid="", path=_path,
                slice_thickness=slice_thickness, protocol_name="",
                timestamp=datetime.datetime.now(), updatetimestamp=datetime.datetime.now()))
        return series_uid

    def __save_series(self, dcm_path):
        if not os.path.exists(dcm_path):
            log.error("{} not found".format(dcm_path))
            return ""
        dataset = pydicom.read_file(dcm_path, force=True)
        series_uid = dataset.SeriesInstanceUID
        series_list = self.mysql_client.select(SeriesModel(id=None, series_instance_uid=series_uid))
        if not series_list:
            self.mysql_client.insert(SeriesModel(
                id=str(uuid.uuid1()), series_instance_uid=series_uid, modality=dataset.Modality,
                series_description=dataset.SeriesDescription, original_series=self.series_instance_uids[0],
                type=F"{self.algorithm_type}_ar", study_id=self.study["id"],
                gmt_create=datetime.datetime.now(), gmt_modified=datetime.datetime.now()))
        return series_uid

    @staticmethod
    def __get_thumbnail(thumbnail_dir):
        file_list = os.listdir(thumbnail_dir)
        file_list.sort(key=lambda x: int(x.split(".")[0]))
        file_size = len(file_list)
        if file_size == 0:
            log.info("thumbnail not found in {}".format(thumbnail_dir))
            return ""
        _index = int(file_size / 2) - 1
        if _index < 0:
            _index = 0
        return "{}/{}".format(thumbnail_dir, file_list[_index])


class BackReport:
    ORTHANC_HOST = _env.get("ORTHANC_HOST", "************")
    ORTHANC_WEB_PORT = int(_env.get("ORTHANC_WEB_PORT", 8044))
    ORTHANC_WADO_USERNAME = _env.get("ORTHANC_WADO_USERNAME", "unionstrong")
    ORTHANC_WADO_PASSWORD = _env.get("ORTHANC_WADO_PASSWORD", "UnionStrong@2020")
    LOCAL_AET = _env.get("LOCAL_AET", "UNIONSTRONG")

    _BASE_URL = F"http://{ORTHANC_HOST}:{ORTHANC_WEB_PORT}"
    _AUTH = HTTPBasicAuth(ORTHANC_WADO_USERNAME, ORTHANC_WADO_PASSWORD)
    _TIMEOUT = 30

    def __init__(self, mysql_client, study_instance_uid, series_instance_uid, algorithm_type, toshiba, manual_return):
        self.mysql_client = mysql_client
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.algorithm_type = algorithm_type
        self.toshiba = toshiba
        self.study = self.mysql_client.select(StudyModel(study_instance_uid=study_instance_uid))[0]
        self.manual_return = manual_return

    def get_target_aet(self):
        pacs_server_list = self.mysql_client.select(PacsServerModel(alie_name="target_pacs"))
        target_aet, target_host, target_port = "", "", ""
        if pacs_server_list:
            _server = pacs_server_list[0]
            target_aet = _server["aet"]
            target_host = _server["ip_address"]
            target_port = _server["port"]
        return target_aet, target_host, target_port

    def get_algorithm_report_value(self):
        code_map = {
            # cta
            "send_vr": "reportCtaVr",
            "send_mip": "reportCtaMip",
            "send_cs": "reportCtaCsMip",

            # ctp
            "can_send_summary": "reportCtpSummary",
            "can_send_colormap": "reportCtpColorMap",
            "can_send_mip": "reportCtpMip",

            # aspects
            "ncct_can_send_summary": "reportAspectsSummary",

        }
        value_map = {}
        for key, code in code_map.items():
            config_list = self.mysql_client.select(ConfigModel(code=code))
            value = False
            if config_list:
                value = config_list[0]["value"]
                try:
                    value = literal_eval(value)
                except:
                    log.info(f"BackReport > get system config code {code} error :{traceback.format_exc()}")
            value_map.setdefault(key, value)
        return value_map

    @staticmethod
    def get_orthanc_id(text: str):
        sha = hashlib.sha1(text.encode("utf-8"))
        encrypts = sha.hexdigest()
        chunks = [encrypts[i:i + 8] for i in range(0, len(encrypts), 8)]
        return "-".join(chunks)

    @staticmethod
    def move(data):
        try:
            url = F"{BackReport._BASE_URL}/modalities/local/move"
            response = requests.post(url=url, json=data, auth=BackReport._AUTH)
            log.info("Orthanc > url:{}, data:{}, code:{}".format(url, data, response.status_code))
            return response.status_code == 200
        except:
            log.error("cmove error: {}".format(traceback.format_exc()))
            return False

    @staticmethod
    def upload_image(file_path):
        # url = F"http://{settings.ORTHANC_HOST}:8042/instances/"
        url = F"{BackReport._BASE_URL}/instances/"
        try:
            with open(file_path, 'rb') as file:
                files = {'files': (FileUtils.get_filename_without_suffix(file_path), file,
                                   'application/octet-stream', {'Expires': '0'})}
                response = requests.post(url, files=files, auth=BackReport._AUTH)
                log.info("Orthanc > url:{}, file: {}, code:{}".format(url, file_path, response.status_code))
                return response.status_code == 200
        except Exception:
            log.error("Orthanc > failed to call orthanc restful, file:{}, {}".format(file_path, traceback.format_exc()))
            return False

    @staticmethod
    def send_cstore(files, host, port, aet):
        """
        上传图片到医院的pacs服务
        """
        log.info("BackReport > cstore {} files".format(len(files)))
        ae = AE(ae_title=BackReport.LOCAL_AET)
        ae.dimse_timeout = 60
        for _context in StoragePresentationContexts:
            ae.add_requested_context(_context.abstract_syntax, _transfer_syntax_uid)
        assoc = ae.associate(host, port, ae_title=aet)
        log.info(f"BackReport > assoc.is_established: {assoc.is_established}")
        if assoc.is_established:
            send_status = True
            for file in files:
                dataset = pydicom.dcmread(file)
                try:
                    send_status = assoc.send_c_store(dataset)
                    log.info("BackReport > {} first send {} ".format(file, (send_status and send_status.get("Status") == 0)))
                except:
                    log.error("failed to send dicom: {}".format(traceback.format_exc()))
                    log.info("BackReport > reassociate again")
                    assoc.release()
                    ae.add_requested_context(dataset.SOPClassUID, dataset.file_meta.TransferSyntaxUID)
                    assoc = ae.associate(host, port, ae_title=aet)
                    if assoc.is_established:
                        send_status = assoc.send_c_store(dataset)
                        log.info("BackReport > {} second send {}".format(file, (send_status and send_status.get("Status") == 0)))
                    else:
                        return False
            assoc.release()
            return send_status
        else:
            return False

    def cta_back_report(self, method, target_host, target_port, target_aet, value_map):
        send_vr = True if self.manual_return else value_map.get("send_vr")
        send_mip = True if self.manual_return else value_map.get("send_mip")
        send_cs = True if self.manual_return else value_map.get("send_cs")
        if not send_vr and not send_mip and not send_cs:
            log.info("BackReport > CTA vr/mip/cs not available")
            return
        # cta
        # 手动获取所有的StudyUid, SeriesaUid  及文件path， 然后进行自动/手动  c_move/c_store
        cta_dict = ALGORITHM_RESULT_DESC["cta"]
        log.info(f"BackReport > cta_dict: {cta_dict}")
        resources = []
        all_files = []
        study_list = self.mysql_client.select(StudyModel(study_instance_uid=self.study_instance_uid))
        study_id = study_list[0]["id"]
        for series_description, path in cta_dict.items():
            series_list = self.mysql_client.select(SeriesModel(
                study_id=study_id, series_description=series_description, original_series=self.series_instance_uid))
            if not series_list:
                log.info(f"BackReport > series not exists: series_description: {series_description}, path:{path}")
                continue
            series = series_list[0]
            series_uid = series["series_instance_uid"]
            base_dir = "/data/ctpdata" if os.path.exists("/data/ctpdata") else "/code/data"
            dcm_path = f"{base_dir}/cta/{self.study_instance_uid}/{self.series_instance_uid}/{path}"
            if (send_vr and "VR_" in dcm_path) or (send_mip and "/MIP_" in dcm_path) \
                    or (send_cs and "CS_MIP" in dcm_path):
                if series_description == "collateral circulation USC" and not os.path.exists(dcm_path):
                    dcm_path = os.path.join("/data/ctpdata/static", self.study_instance_uid, self.series_instance_uid,
                                            "COLLATERAL_CIRCULATION_USC/dcm")
                # c_move
                log.info(f"BackReport > dcm_path: {dcm_path}")
                files = FileUtils.get_all_file(dcm_path)
                all_files.extend(files)
                if method == "orthanc":
                    # 防止重复上传
                    if series["orthanc_id"] == "None":
                        for file in files:
                            upload_result = self.upload_image(file)
                            if not upload_result:
                                log.info("upload_image error")
                                return upload_result
                    resources.append({"StudyInstanceUID": self.study_instance_uid, "SeriesInstanceUID": series_uid})
                    concatenation = "{}|{}|{}".format(self.study["patient_id"], self.study_instance_uid, series_uid)
                    series_orthanc_id = self.get_orthanc_id(concatenation)
                    self.mysql_client.update(SeriesModel(id=series["id"], orthanc_id=series_orthanc_id))
        if method == "orthanc":
            data = {"Level": "SERIES", "TargetAet": target_aet, "Timeout": 60, "Resources": resources}
            return self.move(data)
        return self.send_cstore(all_files, target_host, target_port, target_aet)

    def ctp_back_report(self, method, target_host, target_port, target_aet, value_map):
        can_send_summary = True if self.manual_return else value_map.get("can_send_summary")
        can_send_colormap = True if self.manual_return else value_map.get("can_send_colormap")
        can_send_mip = True if self.manual_return else value_map.get("can_send_mip")
        if not can_send_summary and not can_send_colormap and not can_send_mip:
            log.info("BackReport > CTP summary/colormap/mip not available")
            return
        ctp_dict = ALGORITHM_RESULT_DESC["ctp"]
        log.info(f"BackReport > ctp_dict: {ctp_dict}")
        resources = []
        all_files = []
        study_list = self.mysql_client.select(StudyModel(study_instance_uid=self.study_instance_uid))
        study_id = study_list[0]["id"]
        for series_description, dir_name in ctp_dict.items():
            search_cloumns = dict(study_id=study_id, series_description=series_description)
            if not self.toshiba and series_description != Const.ALGORITHM_SERIES_CTP_TOTAL_SUMMARY:
                search_cloumns["original_series"] = self.series_instance_uid
            series_list = self.mysql_client.select(SeriesModel(**search_cloumns))
            if not series_list:
                log.info(f"BackReport > series not exists: series_description: {series_description}, path:{dir_name}")
                continue
            series = series_list[0]
            series_uid = series["series_instance_uid"]
            base_dir = "/data/ctpdata" if os.path.exists("/data/ctpdata") else "/code/data"
            dcm_path = f"{base_dir}/ctp/{self.study_instance_uid}/report/{dir_name}/" if self.toshiba else \
                f"{base_dir}/ctp/{self.study_instance_uid}/{self.series_instance_uid}/report/{dir_name}/"
            if not os.path.exists(dcm_path):
                log.info("{} not found".format(dcm_path))
                continue
            if ("Summary" in dir_name and can_send_summary) or ("Colored_Map" in dir_name and can_send_colormap) or (
                    ("MIP" in dir_name or "AIF_VOF" in dir_name) and can_send_mip):
                # c_move
                log.info(f"BackReport > dcm_path: {dcm_path}")
                files = FileUtils.get_all_file(dcm_path)
                all_files.extend(files)
                if method == "orthanc":
                    # 防止重复上传
                    if series["orthanc_id"] == "None":
                        for file in files:
                            upload_result = self.upload_image(file)
                            if not upload_result:
                                log.info("upload_image error")
                                return upload_result
                    resources.append({"StudyInstanceUID": self.study_instance_uid, "SeriesInstanceUID": series_uid})
                    concatenation = "{}|{}|{}".format(self.study["patient_id"], self.study_instance_uid, series_uid)
                    series_orthanc_id = self.get_orthanc_id(concatenation)
                    self.mysql_client.update(SeriesModel(id=series["id"], orthanc_id=series_orthanc_id))
        if method == "orthanc":
            data = {"Level": "SERIES", "TargetAet": target_aet, "Timeout": 60, "Resources": resources}
            return self.move(data)
        return self.send_cstore(all_files, target_host, target_port, target_aet)

    def aspects_back_report(self, method, target_host, target_port, target_aet, value_map):
        ncct_can_send_summary = True if self.manual_return else value_map.get("ncct_can_send_summary")
        if not ncct_can_send_summary:
            log.info("BackReport > ASPECTS summary not available")
            return True
        aspects_dict = ALGORITHM_RESULT_DESC["aspects"]
        log.info(f"aspects_dict: {aspects_dict}")
        resources = []
        all_files = []
        study_list = self.mysql_client.select(StudyModel(study_instance_uid=self.study_instance_uid))
        study_id = study_list[0]["id"]
        for series_description, dir_name in aspects_dict.items():
            series_list = self.mysql_client.select(SeriesModel(
                study_id=study_id, series_description=series_description, original_series=self.series_instance_uid))
            if not series_list:
                log.info(f"BackReport > series not exists: series_description: {series_description}, path:{dir_name}")
                continue
            series = series_list[0]
            series_uid = series["series_instance_uid"]
            base_dir = "/data/ctpdata" if os.path.exists("/data/ctpdata") else "/code/data"
            dcm_path = f"{base_dir}/static/{self.study_instance_uid}/{self.series_instance_uid}/{dir_name}/dcm/"
            log.info(f"BackReport > dcm_path: {dcm_path}")
            # c_move
            files = FileUtils.get_all_file(dcm_path)
            all_files.extend(files)
            if method == "orthanc":
                # 防止重复上传
                if series["orthanc_id"] == "None":
                    for file in files:
                        # TODO: 上传图片到ugs-pacs服务？下面的send_cstore却是到医院的pacs服务，为什么
                        upload_result = self.upload_image(file)
                        if not upload_result:
                            log.info("upload_image error")
                            return upload_result
                resources.append({"StudyInstanceUID": self.study_instance_uid, "SeriesInstanceUID": series_uid})
                concatenation = "{}|{}|{}".format(self.study["patient_id"], self.study_instance_uid, series_uid)
                series_orthanc_id = self.get_orthanc_id(concatenation)
                self.mysql_client.update(SeriesModel(id=series["id"], orthanc_id=series_orthanc_id))
        if method == "orthanc":
            # TODO: 这里为什么要move？
            data = {"Level": "SERIES", "TargetAet": target_aet, "Timeout": 60, "Resources": resources}
            return self.move(data)
        return self.send_cstore(all_files, target_host, target_port, target_aet)

    def send(self):
        code_map = {
            Const.ALGORITHM_TYPE_CTA: "47006",
            Const.ALGORITHM_TYPE_CTP: "45001",
            Const.ALGORITHM_TYPE_ASPECTS: "46001",
        }
        code = code_map.get(self.algorithm_type, "")
        config_list = self.mysql_client.select(ConfigModel(code="reportBackMethod"))
        if not config_list:
            log.info("BackReport > reportBackMethod not exists")
            return code
        method = config_list[0]["value"]
        target_aet, target_host, target_port = self.get_target_aet()
        if not all([target_aet, target_host, target_port]):
            log.info(f"BackReport > pacs server not found: {target_aet, target_host, target_port}")
            return code
        value_map = self.get_algorithm_report_value()
        result = True
        if self.algorithm_type in [Const.ALGORITHM_TYPE_CTA, Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_ASPECTS]:
            try:
                log.info("BackReport > send by {}".format(method))
                result = getattr(self, f"{self.algorithm_type}_back_report")(
                    method, target_host, target_port, target_aet, value_map)
            except:
                log.error(f"BackReport > error :{traceback.format_exc()}")
                result = False
        if not result:
            return code


class MailHandler:
    def __init__(self, mysql_client, study_instance_uid, algorithm_type):
        self.mysql_client = mysql_client
        self.study_instance_uid = study_instance_uid
        self.algorithm_type = algorithm_type

    def send(self):
        auto_list = self.mysql_client.select(MailAutoModel(is_delete=0))
        if auto_list:
            _queryset = auto_list[0]
            auto_result = _queryset["auto"]
            log.info("mail > auto: {}".format(auto_result))
            if not auto_result:
                return
        UgsApi.send_email(self.study_instance_uid, self.algorithm_type)


class ProgressHandler:
    def __init__(self, mysql_client, channel, algorithm_type, body=None):
        self.mysql_client = mysql_client
        self.channel = channel
        self.algorithm_type = algorithm_type
        self.body = body
        self.log = MyLogger()

    def handle(self):
        """处理算法进度相关逻辑"""
        if self.algorithm_type == Const.ALGORITHM_TYPE_CENTER_LINE:
            self.handle_center_line()
        elif self.algorithm_type in [Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_CTA, Const.ALGORITHM_TYPE_ASPECTS]:
            self.handle_main_algorithm()

    def handle_center_line(self):
        """处理中心线算法进度相关逻辑"""
        percent = self.body.get('percent', 0)
        is_toshiba_ctp = False
        algorithm_type = self.body.get("algorithmType", '')
        study_instance_uid = self.body.get("studyInstanceUID", '')
        series_instance_uid = self.body.get('seriesInstanceUID', '')
        series_instance_uids = self.body.get('seriesInstanceUIDs', '')
        query_series_uid = series_instance_uids if is_toshiba_ctp else series_instance_uid

        if not percent:
            return
        percent = int(percent)
        error_code = self.body.get("errorCode", 0)
        self.log.info("CenterLine Progress > update finish_percent: {}, error_code: {}".format(percent, error_code))
        
        # 获取study_instance_uid(如果没有的话)
        if not study_instance_uid:
            series_list = self.mysql_client.select(SeriesModel(id=None, series_instance_uid=query_series_uid))
            study_list = self.mysql_client.select(StudyModel(id=series_list[0]["study_id"], study_instance_uid=None))
            study_instance_uid = study_list[0]["study_instance_uid"]

        # 发送进度通知
        self._send_progress_notification_center_line(study_instance_uid, query_series_uid, algorithm_type, percent)

    def handle_main_algorithm(self):
        """处理算法进度相关逻辑"""
        percent = self.body.get('percent', 0)
        if not percent:
            return

        percent = int(percent)
        toshiba = self.body.get("toshiba", False)
        algorithm_type = self.body.get("algorithmType", '')
        is_toshiba_ctp = (toshiba and algorithm_type == "ctp")
        study_instance_uid = self.body.get("studyInstanceUID", '')
        series_instance_uid = self.body.get('seriesInstanceUID', '')
        series_instance_uids = self.body.get('seriesInstanceUIDs', '')
        error_code = self.body.get("errorCode", 0)
        al_result = self.body.get('result', '')

        query_series_uid = series_instance_uids if is_toshiba_ctp else series_instance_uid
        task_list = self.mysql_client.select(AlgorithmTaskModel(uuid=None, series_uid=query_series_uid))

        if not task_list:
            return

        self.log.info("Progress > update finish_percent: {}, error_code: {}".format(percent, error_code))
        
        # 获取study_instance_uid(如果没有的话)
        if not study_instance_uid:
            series_list = self.mysql_client.select(SeriesModel(id=None, series_instance_uid=query_series_uid))
            study_list = self.mysql_client.select(StudyModel(id=series_list[0]["study_id"], study_instance_uid=None))
            study_instance_uid = study_list[0]["study_instance_uid"]

        # 处理失败任务
        first_failed_task = next((task for task in task_list if task["finish_percent"] > 100), None)
        if first_failed_task:
            percent = first_failed_task["finish_percent"]
        else:
            task_id_list = [task["uuid"] for task in task_list]
            self.mysql_client.update_by_conditions(
                AlgorithmTaskModel(uuid=task_id_list, series_uid=None, finish_percent=percent, error_code=error_code), 
                ["uuid"]
            )

        # 发送进度通知
        self._send_progress_notification_main(study_instance_uid, query_series_uid, algorithm_type, percent)
        
        # 处理GE回调
        self._handle_ge_callback(percent, is_toshiba_ctp, algorithm_type, series_instance_uid, 
                                 series_instance_uids, study_instance_uid, al_result)

    def _send_progress_notification_main(self, study_instance_uid, query_series_uid, algorithm_type, percent):
        """发送主算法进度通知"""
        self._send_progress_notification(study_instance_uid, query_series_uid, algorithm_type, percent, "ws.notify.study_percent")

    def _send_progress_notification_center_line(self, study_instance_uid, query_series_uid, algorithm_type, percent):
        """发送中心线进度通知"""
        self._send_progress_notification(study_instance_uid, query_series_uid, algorithm_type, percent, "water_uptake_rate.notify.percent")

    def _send_progress_notification(self, study_instance_uid, query_series_uid, algorithm_type, percent, routing_key):
        """发送进度通知"""
        notify_data = dict(StudyInstanceUID=study_instance_uid, SeriesInstanceUID=query_series_uid)
        if 0 < percent <= 100:
            notify_data["percent"] = dict(type=algorithm_type, value=percent)
        # 如果是center_line，并且percent>100，则加上error_code和error_message
        if algorithm_type == Const.ALGORITHM_TYPE_CENTER_LINE and percent > 100:
            notify_data["percent"] = {
                "type": algorithm_type,
                "value": percent,
                "errorCode": self.body.get("errorCode", 0),
                "errorMsg": self.body.get("errorMsg", "")
            }
        # 记录日志
        self.log.info("Progress > send progress notification, routing_key: {}, notify_data: {}".format(routing_key, notify_data))
        RabbitMQProducer.ws_notify(routing_key, {"action": "update", "data": notify_data})

    def _handle_ge_callback(self, percent, is_toshiba_ctp, algorithm_type, series_instance_uid,
                          series_instance_uids, study_instance_uid, al_result):
        """处理GE回调"""
        try:
            config_list = self.mysql_client.select(ConfigModel(code="GE_CALLBACK_START"))
            ge_callback_start = config_list[0]["value"] if config_list else "0"
            if ge_callback_start != "1" or percent < 100:
                return

            self.log.info("Progress > callback GE[/third-party/storageData], percent: {}".format(percent))
            self.body["SeriesInstanceUID"] = series_instance_uids if is_toshiba_ctp else series_instance_uid

            # 获取序列拆分信息
            split_dicom_number = self._get_split_dicom_number(is_toshiba_ctp, algorithm_type, series_instance_uid)

            if percent == 100:
                self._handle_success_callback(study_instance_uid, algorithm_type, al_result, split_dicom_number)
            elif percent > 100:
                self.log.info("Progress > callback GE: algorithm fails")
                begin_request_GE(self.body, "2", split_dicom_number)
        except Exception:
            self.log.error(traceback.format_exc())

    def _get_split_dicom_number(self, is_toshiba_ctp, algorithm_type, series_instance_uid):
        """获取序列拆分信息"""
        if is_toshiba_ctp or algorithm_type == Const.ALGORITHM_TYPE_ASPECTS:
            return 0
        
        split_list = self.mysql_client.select(
            SeriesSplitModel(series_instance_uid=series_instance_uid),
            sorts={"gmt_create": 1}
        )
        return split_list[0].get("image_number", 0) if split_list else 0

    def _handle_success_callback(self, study_instance_uid, algorithm_type, al_result, split_dicom_number):
        """处理成功回调"""
        if not al_result:
            study_list = self.mysql_client.select(StudyModel(study_instance_uid=study_instance_uid))
            series_list = self.mysql_client.select(SeriesModel(study_id=study_list[0]["id"]))
            series_uids = [series["series_instance_uid"] for series in series_list]
            
            res = {
                'ctp': dict(status=False, value="", algorithm_state=Const.ALGORITHM_STATE_WAIT),
                'aspects': dict(status=False, value=""),
                'cta': dict(status=False, value="")
            }
            
            algorithm_text_result = get_algorithm_textresult(
                self.mysql_client, study_instance_uid, series_uids, res
            )
            algoritm_result = algorithm_text_result.get(algorithm_type, {})
            self.log.info(f"Progress > algorithm_text_result： {algorithm_text_result}")
            self.body["result"] = algoritm_result
            
        begin_request_GE(self.body, "1", split_dicom_number)
        self.log.info("Progress > callback GE: algorithm complete") 


class AlgorithmResultHandler:
    def __init__(self, mysql_client, study_instance_uid, series_instance_uid, series_instance_uids, algorithm_type, is_toshiba_ctp, toshiba=False, body=None):
        self.mysql_client = mysql_client
        self.study_instance_uid = study_instance_uid
        self.series_instance_uid = series_instance_uid
        self.series_instance_uids = series_instance_uids
        self.is_toshiba_ctp = is_toshiba_ctp
        self.algorithm_type = algorithm_type
        self.toshiba = toshiba
        self.log = MyLogger()
        self.mongodb = MongoDB()
        self.body = body

    def handle(self):
        """处理算法结果"""
        al_result = self.body.get('result', '')
        if self.algorithm_type == Const.ALGORITHM_TYPE_CENTER_LINE:
            self.handle_center_line(al_result)
        elif self.algorithm_type in [Const.ALGORITHM_TYPE_CTP, Const.ALGORITHM_TYPE_CTA, Const.ALGORITHM_TYPE_ASPECTS]:
            self.handle_main_algorithm(al_result)

    def handle_center_line(self, al_result):
        """处理中心线算法结果"""
        # 保存结果到MongoDB
        # lines: Centerlines of each slice. [[[x00,y00],[x01.y01]],[[x10,y10],[x11.y11]], ... ,[[xn0,yn0],[xn1.yn1]]]
        try:
            lines = json.loads(al_result)
            value_fronted = []
            for line in lines:
                start_point = line[0]
                end_point = line[1]
                value_fronted.append({
                    "visible": True,
                    "active": False,
                    "color": "yellow", 
                    "invalidated": True,
                    "handles": {
                        "start": {
                            "x": start_point[0],
                            "y": start_point[1],
                            "highlight": False,
                            "active": False
                        },
                        "end": {
                            "x": end_point[0],
                            "y": end_point[1],
                            "highlight": False,
                            "active": False,
                        },
                        "textBox": {
                            "active": False,
                            "hasMoved": True,
                            "drawnIndependently": True,
                            "x": (start_point[0] + end_point[0]) / 2,
                            "y": (start_point[1] + end_point[1]) / 2
                        }
                    },
                    "text": "对称线",
                    "unit": "mm"
                })
        except:
            self.log.error("AlgorithmResult CenterLine > error:{}".format(traceback.format_exc()))
            lines = []
            value_fronted = []
            
        result = {
            "study_instance_uid": self.study_instance_uid,
            "series_instance_uid": self.series_instance_uid,
            "value": lines,
            "value_fronted": value_fronted
        }
        # 查询是否存在相同study和series的数据
        query = {
            "study_instance_uid": self.study_instance_uid,
            "series_instance_uid": self.series_instance_uid
        }
        existing_result = self.mongodb.find_one(query, 'center_line')
        
        if existing_result:
            # 如果存在则更新
            # 更新update_time
            result["update_time"] = datetime.datetime.now()
            self.mongodb.update(result, 'center_line', query)
            algorithm_result_id = existing_result.get('_id')  # 使用现有记录的_id
        else:
            # 如果不存在则新建
            # 更新create_time
            result["update_time"] = result["create_time"] = datetime.datetime.now()
            result_create_set = self.mongodb.create(result, 'center_line')
            algorithm_result_id = result_create_set.inserted_id
            
        self.log.info("AlgorithmResult CenterLine > text result:{}".format(algorithm_result_id))
        

    def handle_main_algorithm(self, al_result):
        """处理主算法结果"""
        if not al_result or self.algorithm_type not in ['ctp', 'cta', 'aspects']:
            self.log.info("AlgorithmResult > unknown algorithm type:{}, study:{}, series:{}".format(
                self.algorithm_type, self.study_instance_uid, (self.series_instance_uids if self.is_toshiba_ctp else self.series_instance_uid)))
            return
        
        # 保存结果到MongoDB
        result = dict(result=al_result)
        result_create_set = self.mongodb.create(result, 'algorithm')
        algorithm_result_id = result_create_set.inserted_id
        self.log.info("AlgorithmResult > text result:{}".format(algorithm_result_id))

        # 查询现有结果
        query_image_series = self.study_instance_uid if self.is_toshiba_ctp else self.series_instance_uid
        algorithm_result_list = self.mysql_client.select(AlgorithmResultModel(
            image_series=query_image_series, algorithm_type=self.algorithm_type))
        self.log.info("AlgorithmResult > image_series:{}, result_id:{}, algorithm_result:{}".format(
            query_image_series, algorithm_result_id, algorithm_result_list))

        # 处理结果存储
        if not algorithm_result_list:
            self._create_new_result(algorithm_result_id, query_image_series, self.series_instance_uids, self.is_toshiba_ctp)
        else:
            self._update_existing_result(algorithm_result_id, algorithm_result_list, self.is_toshiba_ctp)

        # 发送邮件通知
        mail_handler = MailHandler(self.mysql_client, self.study_instance_uid, self.algorithm_type)
        mail_handler.send()

    def _create_new_result(self, algorithm_result_id, query_image_series, series_instance_uids, is_toshiba_ctp):
        """创建新的算法结果"""
        query_series_uid = series_instance_uids if is_toshiba_ctp else self.series_instance_uid
        algorithm_task_list = self.mysql_client.select(AlgorithmTaskModel(series_uid=query_series_uid))
        
        index = self.body.get('index', 0)

        for task in algorithm_task_list:
            self.mysql_client.insert(AlgorithmResultModel(
                uuid=uuid.uuid1(),
                image_series=query_image_series,
                algorithm_type=self.algorithm_type,
                algorithm_result=algorithm_result_id,
                task_id=task["uuid"],
                index=index,
                create_time=datetime.datetime.now()
            ))
        
        self.log.info("AlgorithmResult > create result, image_series:{},algorithm_type,{}, result_id:{}".format(
            query_image_series, self.algorithm_type, algorithm_result_id))

    def _update_existing_result(self, algorithm_result_id, algorithm_result_list, is_toshiba_ctp):
        """更新现有算法结果"""
        # 删除旧的MongoDB结果
        distinct_result_id = [ObjectId(result["algorithm_result"]) for result in algorithm_result_list]
        if is_toshiba_ctp:
            distinct_result_id = list(set(distinct_result_id))
        
        del_result = self.mongodb.bulk_delete(
            condition_list=distinct_result_id,
            condition_key="_id",
            table="algorithm"
        )
        self.log.info("AlgorithmResult > clear mongo, id:{}, result:{}".format(distinct_result_id, del_result))

        # 更新MySQL中的结果ID
        result_id_list = [result["uuid"] for result in algorithm_result_list]
        self.mysql_client.update_by_conditions(
            AlgorithmResultModel(uuid=result_id_list, algorithm_result=algorithm_result_id),
            ["uuid"]
        )
        self.log.info("AlgorithmResult > update result, result_id:{}".format(algorithm_result_id)) 
