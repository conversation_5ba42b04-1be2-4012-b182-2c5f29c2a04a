#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : mysql
@Project : ugs-backend
<AUTHOR> mingxing
@Date    : 2023/3/20 13:55
"""
import datetime
import traceback

import pymysql

from utils.code import Env
from utils.logger import MyLogger

log = MyLogger()


class MySQLClient:
    def __init__(self, auth_commit=True):
        log.info("MySQL > open connection")
        self.conn = pymysql.connect(host=Env.DB_HOST, port=Env.DB_PORT,
                                    user=Env.DB_USER, passwd=Env.DB_PASSWORD, charset="utf8",
                                    cursorclass=pymysql.cursors.DictCursor)
        self.auth_commit = auth_commit

    def select(self, obj, sorts: dict = None):
        cur = None
        sql = ""
        try:
            sql = "select * from " + self.get_table_name(obj)
            attr_dict = obj.__dict__
            condition = ""
            for key, value in attr_dict.items():
                if key is not None and value is not None:
                    if isinstance(value, str):
                        condition = F"{condition} `{key}` = '{str(value)}' and "
                        continue
                    if isinstance(value, int):
                        condition = F"{condition} `{key}` = {value} and "
                        continue
                    if isinstance(value, list):
                        in_list = "','".join(value)
                        condition = F"{condition} `{key}` in ('{in_list}') and "
            if condition:
                order = ""
                if sorts:
                    for key, value in sorts.items():
                        if key and str(value):
                            order = F"{order} {key} {'desc' if value else 'asc'}, "
                    if order:
                        order = " order by " + order[:-2]
                sql = sql + " where" + condition[:-4] + order
                log.debug("MySQL[select] > {}".format(sql))
                cur = self.conn.cursor()
                cur.execute(sql)
                return cur.fetchall()
            return []
        except:
            log.error("MySQL[select] > sql:{}, error:{}".format(sql, traceback.format_exc()))
            return []
        finally:
            if cur:
                cur.close()

    def delete(self, obj):
        cur = None
        sql = ""
        try:
            id_column = obj.Meta.id_name
            if id_column and hasattr(obj, id_column):
                sql = "delete from " + self.get_table_name(obj) + F" where `{id_column}` = '{getattr(obj, id_column)}'"
                log.debug("MySQL[delete] > {}".format(sql))
                cur = self.conn.cursor()
                cur.execute(sql)
                self.conn.commit()
        except:
            log.error("MySQL[delete] > sql:{}, error:{}".format(sql, traceback.format_exc()))
            self.conn.rollback()
        finally:
            if cur:
                cur.close()

    def insert(self, obj):
        cur = None
        sql = ""
        try:
            sql = "insert into " + self.get_table_name(obj)
            columns = ""
            values = ""
            attr_dict = obj.__dict__
            for key, value in attr_dict.items():
                if not obj.Meta.id_auto or key != obj.Meta.id_name:
                    columns = F"{columns} `{str(key)}`, "
                    values = F"{values} '{str(value)}', "
            if columns and values:
                sql = F"{sql} ({columns[:-2]} ) values ({values[:-2]} )"
                log.debug("MySQL[insert] > {}".format(sql))
                cur = self.conn.cursor()
                cur.execute(sql)
                self.conn.commit()
        except:
            log.error("MySQL[insert] > sql:{}, error:{}".format(sql, traceback.format_exc()))
        finally:
            if cur:
                cur.close()

    def update(self, obj):
        cur = None
        sql = ""
        try:
            sql = "update " + self.get_table_name(obj) + " set "
            attr_dict = obj.__dict__
            for key, value in attr_dict.items():
                if key is not None and value is not None and key != obj.Meta.id_name:
                    sql = F"{sql} `{key}` = '{str(value)}', "
            sql = F"{sql[:-2]} where `{obj.Meta.id_name}` = '{getattr(obj, obj.Meta.id_name)}'"
            log.debug("MySQL[update] > {}".format(sql))
            cur = self.conn.cursor()
            cur.execute(sql)
            self.conn.commit()
        except:
            log.error("MySQL[update] > sql:{}, error:{}".format(sql, traceback.format_exc()))
        finally:
            if cur:
                cur.close()

    def update_by_conditions(self, obj, conditions=None):
        if conditions is None:
            conditions = []
        cur = None
        sql = ""
        try:
            sql = "update " + self.get_table_name(obj) + " set"
            condition = ""
            attr_dict = obj.__dict__
            for key, value in attr_dict.items():
                if key is not None and value is not None:
                    if key in conditions:
                        if isinstance(value, str):
                            condition = F"{condition} `{key}` = '{str(value)}' and "
                        if isinstance(value, list):
                            in_values = "','".join(value)
                            condition = F"{condition} `{key}` in ('{in_values}') and "
                        continue
                    if key != obj.Meta.id_name:
                        sql = F"{sql} `{key}` = '{str(value)}', "
            sql = F"{sql[:-2]} where{condition[:-4]}"
            log.debug("SQL[update] > {}".format(sql))
            cur = self.conn.cursor()
            cur.execute(sql)
            self.conn.commit()
        except:
            log.error("MySQL[update] > sql:{}, error:{}".format(sql, traceback.format_exc()))
        finally:
            if cur:
                cur.close()

    @staticmethod
    def get_table_name(obj):
        if hasattr(obj, "Meta"):
            return F"`{obj.Meta.db_name}`.`{obj.Meta.table_name}`"
        lst = []
        for index, char in enumerate(obj.__class__.__name__):
            if char.isupper() and index != 0:
                lst.append("_")
            lst.append(char)
        return "".join(lst).lower()

    def close(self):
        log.info("MySQL > close connection")
        if self.conn:
            self.conn.close()


# if __name__ == "__main__":
#     from utils.models import VersionModel, MailAutoModel
#
#     client = MySQLClient()
#     result = client.select(MailAutoModel(is_delete=0))
#     print(result)

    # version = VersionModel(uuid=None, server_type="result_consumer", version=None)
    # client = MySQLClient()
    # result = client.select(version)
    # print(result)
    #
    # kk = VersionModel(uuid=result[0]["uuid"], server_type=None, version="1.7.1",
    #                    update_timestamp=datetime.datetime.now())
    # client.update(kk)
    #
    # kk = VersionModel(uuid=result[0]["uuid"], server_type=result[0]["server_type"], version="1.7.1", is_delete=0,
    #                    timestamp=datetime.datetime.now(), update_timestamp=datetime.datetime.now())
    # client.insert(kk)
