#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : code
@Project : ugs-backend
<AUTHOR> mingxing
@Date    : 2023/3/20 15:55
"""
import os

_env = os.environ


class Env:
    DB_HOST = _env.get('DB_HOST', '************')
    DB_PORT = int(_env.get('DB_PORT', "3310"))
    DB_USER = _env.get('DB_USER', 'root')
    DB_PASSWORD = _env.get('DB_PASSWORD', 'UnionStrong@2020')

    MQ_HOST = _env.get("MQ_HOST", "************")
    MQ_PORT = _env.get("MQ_PORT", 5673)
    MQ_USERNAME = _env.get("MQ_USERNAME", "unionstrong")
    MQ_PASSWORD = _env.get("MQ_PASSWORD", "UnionStrong@2020")

    ORTHANC_HOST = _env.get("ORTHANC_HOST", "************")
    ORTHANC_AET = _env.get("ORTHANC_AET", "DockerOrthanc")
    ORTHANC_PORT = int(_env.get("ORTHANC_PORT", 4242))
    ORTHANC_DOCKER_PORT = int(_env.get("ORTHANC_WADO_PORT", 8044))
    ORTHANC_WADO_USERNAME = _env.get("ORTHANC_WADO_USERNAME", "unionstrong")
    ORTHANC_WADO_PASSWORD = _env.get("ORTHANC_WADO_PASSWORD", "UnionStrong@2020")
    LOCAL_AET = _env.get("LOCAL_AET", "UNIONSTRONG")

    MONGODB_HOST = _env.get('MONGODB_HOST', '************')
    MONGODB_PORT = int(_env.get('MONGODB_PORT', '27018'))
    MONGODB_USER = _env.get('MONGO_INITDB_ROOT_USERNAME', "unionstrong")
    MONGODB_PASSWORD = _env.get('MONGO_INITDB_ROOT_PASSWORD', "UnionStrong@2020")

    # WebApi Configuration
    WEBAPI_HOST = _env.get("WEBAPI_HOST", "ugs-api")
    WEBAPI_PORT = _env.get("WEBAPI_PORT", "4201")


class Const:
    ALGORITHM_TYPE_ASPECTS = "aspects"
    ALGORITHM_TYPE_CTP = "ctp"
    ALGORITHM_TYPE_CTA = "cta"
    ALGORITHM_TYPE_CENTER_LINE = "center_line"
    ALGORITHM_SERIES_CTP_TOTAL_SUMMARY = "USC-UGuard CTP Total Summary"

    ALGORITHM_STATE_WAIT = 0  # 未计算
    ALGORITHM_STATE_CALCULATING = 1  # 计算中
    ALGORITHM_STATE_FAILURE = 2  # 计算失败
    ALGORITHM_STATE_SUCCESS = 3  # 计算成功


# UGS 算法集成错误码汇总
AlgorithmErrorCodeMap = {
    # platform
    # API
    30000: 'Other series type',
    30001: 'Single image slice thickness out of range',
    # ctp
    45000: 'Series not found',
    45001: 'Upload report error',
    # aspects
    46000: 'Series not found',
    46001: 'Upload report error',
    # cta
    47000: 'Series not found',
    47001: 'Vessel not found',
    47002: 'Load vessel error',
    47003: 'Generate VR error',
    47004: 'Generate MIP error',
    47005: 'Generate VR/MIP error',
    47006: 'Upload report error',
    47007: 'Generate/Upload CS MIP error',
    # ResultConsumer
    48000: 'Failed to save algorithm result',

    # algorithm
    # ctp
    50000: 'CTP Error',
    50001: 'Initialize environment failed',
    50002: 'Parse ctp series failed',
    50003: 'Load ctp series failed',
    50004: 'Motion correction failed',
    50005: 'Extract brain failed',
    50006: 'Preprocess failed',
    50007: 'Gamma fit failed',
    50008: 'Extract csf failed',
    50009: 'Detect aif and vof point failed',
    50010: 'Generate feature map failed',
    50011: 'Compare feature map failed',
    50012: 'Generate results failed',
    # ASPECT
    60000: 'ASPECTS error',
    # CTA
    70000: 'CTA error',
    70001: 'Load data failed',
    70100: 'Segmentation module failed',
    70101: 'Initialize failed',
    70102: 'Load config file failed',
    70103: 'Parse config failed',
    70104: 'Load model failed',
    70105: 'Preprocess data failed',
    70106: 'Inference failed',
    70108: 'Recovery data failed',
    70200: 'Key points server failed',
    70201: 'Key points module process failed',
    70300: 'CPR module failed',
    70301: 'CPR smooth surface failed',
    70302: 'CPR extract centerline failed',
    70303: 'CPR resample and optimize centerline failed',
    70304: 'CPR centerline radius file written failed',
    70305: 'CPR generation failed',
    70309: 'File not exist',
    79999: 'Unknown error',
}

ALGORITHM_RESULT_DESC = {
    "aspects": {
        "USC-UGuard ASPECTS Summary": "USC-UGuard_ASPECTS_Summary",
        "3D ASPECTS MASK USC": "3D_ASPECTS_MASK_USC",
        "3D PC-ASPECTS SUMMARY USC": "3D_PC-ASPECTS_SUMMARY_USC",
        "3D ASPECTS SUMMARY USC": "3D_ASPECTS_SUMMARY_USC",
        "HEMORRHAGE SUMMARY USC": "HEMORRHAGE_SUMMARY_USC",
        "HEMORRHAGE MASK USC": "HEMORRHAGE_MASK_USC",
    },
    "cta": {
        "collateral circulation USC": "USC_UGuard_CTA_Volume_CS_MIP/",
        "USC-UGuard CTA Volume Rendering AP": "dcm/VR_AP/",
        "USC-UGuard CTA Volume Rendering LR": "dcm/VR_LR/",
        "USC-UGuard CTA Volume MIP AP": "dcm/MIP_AP/",
        "USC-UGuard CTA Volume MIP LR": "dcm/MIP_LR/",
        "USC-UGuard CTA Volume Rendering Head AP": "dcm/VR_Head_AP/",
        "USC-UGuard CTA Volume Rendering Head LR": "dcm/VR_Head_LR/",
        "USC-UGuard CTA Volume MIP Head AP": "dcm/MIP_Head_AP/",
        "USC-UGuard CTA Volume MIP Head LR": "dcm/MIP_Head_LR/",
    },
    "ctp": {
        "USC-UGuard AIF-VOF Location": "USC_UGuard_CTP_AIF_VOF_CSF_Location",
        "USC-UGuard CTP Summary": "USC_UGuard_CTP_Summary",
        "USC-UGuard CTP Total Summary": "USC_UGuard_CTP_Total_Summary",
        "USC-UGuard Perfusion Parameter Maps Colored CBF": "USC_UGuard_CTP_Parameter_Colored_Map_CBF",
        "USC-UGuard Perfusion Parameter Maps Colored CBV": "USC_UGuard_CTP_Parameter_Colored_Map_CBV",
        "USC-UGuard Perfusion Parameter Maps Colored MTT": "USC_UGuard_CTP_Parameter_Colored_Map_MTT",
        "USC-UGuard Perfusion Parameter Maps Colored PS": "USC_UGuard_CTP_Parameter_Colored_Map_PS",
        "USC-UGuard Perfusion Parameter Maps Colored TTP": "USC_UGuard_CTP_Parameter_Colored_Map_TTP",
        "USC-UGuard Perfusion Parameter Maps Colored Tmax": "USC_UGuard_CTP_Parameter_Colored_Map_Tmax",
        "USC-UGuard CTP MIP": "USC_UGuard_CTP_MIP"
    }
}
