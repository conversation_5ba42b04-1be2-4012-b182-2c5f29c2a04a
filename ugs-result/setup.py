#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@File    : setup.py
@Project : ugs-result
<AUTHOR> mingxing
@Date    : 2023/3/29 14:05
"""
from setuptools import setup, Extension
from setuptools.command.build_py import build_py as build_py_orig
from Cython.Build import cythonize
import src.ugs_result.package_info as pi


ext_modules = [
    Extension("ugs_result.*", ["src/ugs_result/*.py"]),
    Extension("ugs_result.utils.*", ["src/ugs_result/utils/*.py"])
]


class BuildPy(build_py_orig):
    def build_packages(self):
        pass


setup(
    name=pi.name,
    author=pi.author,
    url=pi.url,
    cmdclass={"build_py": BuildPy},
    ext_modules=cythonize(ext_modules, language_level="3"),
    version=pi.version,
    packages=[pi.name],
    package_dir={"": "src"},
    platforms=pi.platforms,
    description=pi.description,
    long_description=pi.long_description,
    license="MIT",
    entry_points={
        "console_scripts": [
            "ugsresult = ugs_result.consumer:main"
        ]
    }
)
