version: '3'

services:
  ugs-ctp4d:
    container_name: "ugs-ctp4d"
    image: harbor.unionstrongtech.com/ugs/ctp4d:0.1.3
    ports:
      - "8085:8085"
      - "8006:8006"
    volumes:
      - ./dist/:/app/dist
      - /data/ctpdata:/data/ctpdata
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - /dev/dri:/dev/dri:rw
    working_dir: /app
    command: bash -c "pip install --force-reinstall dist/*.whl && python -m ugs_ctp4d.examples.CTP4D  --server --host 0.0.0.0 --port 8085"
    env_file:
      - .env
    restart: unless-stopped
    # GPU支持配置 (兼容性更好的方式)
    devices:
      - /dev/nvidia0:/dev/nvidia0
      - /dev/nvidiactl:/dev/nvidiactl
      - /dev/nvidia-uvm:/dev/nvidia-uvm
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - APP_HOST=0.0.0.0
      - APP_PORT=8085
      - DATA_PATH=/data/ctpdata
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=graphics,utility,compute
      - DISPLAY=${DISPLAY:-:0}
      - LIBGL_ALWAYS_INDIRECT=0
      - LIBGL_ALWAYS_SOFTWARE=0
      - __GLX_VENDOR_LIBRARY_NAME=nvidia
      - VTK_USE_OPENGL2=1
      - VTK_DEFAULT_RENDER_WINDOW_OFFSCREEN=1
      - MESA_GL_VERSION_OVERRIDE=4.5
      - MESA_GLSL_VERSION_OVERRIDE=450
      - __GL_SYNC_TO_VBLANK=0
      - __GL_YIELD=NOTHING
      - QT_QPA_PLATFORM=offscreen

