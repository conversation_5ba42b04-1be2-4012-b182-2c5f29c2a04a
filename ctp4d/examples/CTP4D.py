from trame.app import get_server
from trame.ui.vuetify import SinglePageLayout, VAppLayout
from trame.widgets import vuetify, html, trame, vtk as vtk_widgets


from mipf.core.render_window import *
from mipf.core.data import *
from mipf.core.utils import *
from mipf.ui.data import *
from mipf.ui.engine import *
from mipf.ui.app import AppBase
import threading
import asyncio
import os
import tkinter as tk
from tkinter import filedialog
import json
from urllib.parse import unquote

server = get_server(client_type="vue2")
state = server.state


class Workbench(AppBase):
    def __init__(self, server, app_name="Undefined"):
        super().__init__(server, app_name)
        self.state.update(
            {
                "active_node_type": None,
                "current_representation": Representation.Surface,
                "surface_color": "#FFFFFFFF",
                "current_opacity": 1.0,
                # picking controls
                "modes": [
                    {"value": "hover", "icon": "mdi-magnify"},
                    {"value": "click", "icon": "mdi-cursor-default-click-outline"},
                    {"value": "select", "icon": "mdi-select-drag"},
                ],
                "pickingMode": "hover",  # 默认交互模式
                # Picking feedback
                "pickData": None,
                "selectData": None,
                "tooltip": "",
                "pixel_ratio": 2,
                # 4D CTA controls
                "is_playing": False,
                "playback_progress": 0,
                "playback_speed": 1.0,
                "current_vessel": 0,
                "vessel_number": 0,
                # CTP parameters
                "active_tab": "playback",
                "cbv_value": 0,
                "cbf_value": 0,
                "mtt_value": 0,
                "ttp_value": 0,
            }
        )
        self.playback_task = None
        self._setup_playback_controls()
        self._setup_homepage_loading()

    def init_scene(self):
        # 计算血管数量
        number = 0
        for node in self.data_storage.nodes.values():
            print(node['name'])
            if "vessel" in node['name']:
                number += 1
        self.state.vessel_number = number-1
        self.state.current_vessel = 0
        self.state.playback_progress = 0

    def _setup_homepage_loading(self):
        """设置首页路径参数数据加载功能"""
        @self.server.controller.on_server_bind.add
        def setup_homepage_middleware(wslink_server):
            """在服务器绑定时添加首页数据加载中间件"""
            from aiohttp import web

            try:
                # 获取aiohttp应用实例
                app = wslink_server.app

                # 添加中间件来处理首页的路径参数
                @web.middleware
                async def path_loading_middleware(request, handler):
                    # 处理GET请求到根路径或index.html的情况
                    if (request.method == 'GET' and
                        request.path in ['/', '/index.html']):

                        # 检查是否有study_instance_uid和series_instance_uid参数
                        study_uid = request.query.get('study_instance_uid')
                        series_uid = request.query.get('series_instance_uid')

                        # 检查是否有传统的path参数（向后兼容）
                        legacy_path = request.query.get('path')

                        # 如果有UID参数，构建路径
                        if study_uid and series_uid:
                            # URL解码参数
                            study_uid = unquote(study_uid)
                            series_uid = unquote(series_uid)

                            # 构建路径
                            constructed_path = f"/data/ctpdata/ctp/{study_uid}/{series_uid}/4D_CTA"
                            print(f"首页检测到UID参数: study={study_uid}, series={series_uid}")
                            print(f"构建路径: {constructed_path}")

                            # 加载数据
                            try:
                                result = self.load_data_from_path_or_folder(constructed_path)
                                if result['success']:
                                    print(f"首页数据加载成功: {result['message']}")
                                    # 确保视角重置
                                    if self.server.protocol:
                                        self.ctrl.reset_camera()
                                        print("   - UID参数加载后自动重置视角")
                                else:
                                    print(f"首页数据加载失败: {result['message']}")
                            except Exception as e:
                                print(f"首页数据加载异常: {str(e)}")

                        # 处理传统的path参数（向后兼容）
                        elif 'path' in request.query:
                            path = legacy_path
                            # URL解码路径参数
                            if path:
                                path = unquote(path)

                            print(f"首页检测到路径参数: {path}")

                            # 如果path为空或空字符串，只清理数据不加载新数据
                            if not path or path.strip() == '':
                                print("路径参数为空，仅执行数据清理")
                                try:
                                    self.clear_all_data()
                                    print("数据清理完成")
                                except Exception as e:
                                    print(f"数据清理异常: {str(e)}")
                            else:
                                # 有有效路径时，清理并加载新数据
                                try:
                                    result = self.load_data_from_path_or_folder(path)
                                    if result['success']:
                                        print(f"首页数据加载成功: {result['message']}")
                                        # 确保视角重置
                                        if self.server.protocol:
                                            self.ctrl.reset_camera()
                                            print("   - 路径参数加载后自动重置视角")
                                    else:
                                        print(f"首页数据加载失败: {result['message']}")
                                except Exception as e:
                                    print(f"首页数据加载异常: {str(e)}")

                        # 如果只有study_uid但没有series_uid，仅清理数据
                        elif study_uid and not series_uid:
                            print("仅有study_instance_uid参数，缺少series_instance_uid，仅执行数据清理")
                            try:
                                self.clear_all_data()
                                print("数据清理完成")
                            except Exception as e:
                                print(f"数据清理异常: {str(e)}")

                    # 继续正常的请求处理，并添加防缓存头部
                    response = await handler(request)

                    # 添加防缓存的HTTP头部，确保浏览器不会缓存页面
                    if (request.method == 'GET' and
                        request.path in ['/', '/index.html']):
                        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                        response.headers['Pragma'] = 'no-cache'
                        response.headers['Expires'] = '0'
                        print("   - 已添加防缓存HTTP头部")

                    return response

                # 添加中间件
                app.middlewares.append(path_loading_middleware)

                print("首页参数功能已设置:")
                print("   - 支持UID参数: /?study_instance_uid=xxx&series_instance_uid=yyy")
                print("   - 支持UID参数: /index.html?study_instance_uid=xxx&series_instance_uid=yyy")
                print("   - 自动构建路径: /data/ctpdata/ctp/{study_uid}/{series_uid}/4D_CTA")
                print("   - 向后兼容: /?path=/your/data/path")
                print("   - 支持URL: /?path= (空参数仅清理数据)")
                print("   - 支持文件和文件夹路径")
                print("   - 自动清空并重新加载数据")
                print("   - 已启用防缓存机制，确保每次访问都重新请求服务器")

            except Exception as e:
                import traceback
                traceback.print_exc()

    def _setup_playback_controls(self):
        @self.state.change("is_playing")
        def toggle_playback(is_playing, **kwargs):
            if is_playing:
                self.start_playback()
            else:
                self.stop_playback()

        @self.state.change("playback_progress")
        def update_playback_progress(playback_progress, **kwargs):
            # 在这里更新4D CTA的显示状态
            print(f"update_playback_progress: {playback_progress}")
            self.state.current_vessel = int(playback_progress*self.state.vessel_number)
            for node in self.data_storage.nodes.values():
                if "vessel_" in node['name']:
                    print(f"current play node name: {node['name']}")
                    index = node["name"].split("_")[1]
                    index = index.split(".")[0]
                    if index.isdigit():
                        if self.state.current_vessel == int(index):
                            node['visible'] = True
                        else:
                            node['visible'] = False
            self.data_storage.modefied(0)
            render_window_manager.request_update_all()
            self.ctrl.view_update()

    async def _playback_loop(self):
        while self.state.is_playing:
            self.update_progress()
            await asyncio.sleep(0.1)

    def start_playback(self):
        if self.playback_task is None:
            self.playback_task = asyncio.create_task(self._playback_loop())

    def stop_playback(self):
        if self.playback_task is not None:
            self.playback_task.cancel()
            self.playback_task = None

    def update_progress(self):
        if self.state.is_playing:
            step = 0.01 * self.state.playback_speed
            self.state.playback_progress = (self.state.playback_progress + step) % 1.0
            self.state.flush()
            self.ctrl.view_update()

    def setupui(self):
        use_plotter = False
        self.render_window = RenderWindow(self.data_storage,
                                          ViewType.View3D, use_plotter=use_plotter)
        self.render_window.setup()



        initialize_binding(server, self.data_storage,
                           plotter=self.render_window.get_plotter())

        @state.change("vtk_files")
        def load_files(vtk_files, **kwargs):
            if vtk_files is None or len(vtk_files) == 0:
                return

            # 显示进度条
            self.state.loading = True
            self.state.loading_progress = 0
            self.state.loading_text = "正在加载文件..."

            # 计算总文件数
            total_files = len(vtk_files)

            # 加载所有文件
            nodes = load_client_files(files=vtk_files, data_storage=self.data_storage)

            for node in nodes:
                node["helper object"] = True

            self.data_storage.modefied(0)

            # 更新进度
            self.state.loading_progress = 100
            self.state.loading_text = "文件加载完成"

            self.init_scene()
            self.ctrl.reset_camera()

        @state.change("tf_files")
        def load_tf_files(tf_files, **kwargs):
            if tf_files is None or len(tf_files) == 0:
                return

            if tf_files and len(tf_files):
                if not tf_files[0].get("content"):
                    return
                for file in tf_files:
                    file = ClientFile(file)
                    print(f"Load {file.name}")
                    scalar_opacity, gradient_opacity, color = extract_tf(
                        file.content)

                    for node in self.data_storage.nodes.values():
                        if node.data.type == DataType.Image and node.get("activate"):
                            node["scalar_opacity"] = scalar_opacity
                            node["gradient_opacity"] = gradient_opacity
                            node["colors"] = color
                    render_window_manager.request_update_all()
                    self.ctrl.view_update()

        with SinglePageLayout(server) as layout:
            # Toolbar
            layout.title.set_text(self.app_name)
            layout.footer.hide()
            with layout.toolbar as toolbar:
                vuetify.VSpacer()
                vuetify.VBtn("重置视角", click=self.ctrl.reset_camera)
                vuetify.VSpacer()

            with layout.content:
                with vuetify.VContainer(
                    fluid=True,
                    classes="pa-0 fill-height",
                ):
                    with vuetify.VRow(classes="fill-height"):
                        # 左侧视图
                        with vuetify.VCol(cols=9):
                            with vtk_widgets.VtkRemoteView(self.render_window.get_vtk_render_window(),
                                                           picking_modes=(
                                "[pickingMode]",),
                                interactor_settings=(
                                "interactorSettings", VIEW_INTERACT),
                                click="pickData = $event",
                                on_remote_image_capture="utils.download('remote.png', $event)",
                                on_local_image_capture=(
                                    self.ctrl.captura_screen, "['local.png', $event]"),
                                on_ready=self.ctrl.on_ready2,
                            ) as html_view:
                                self.ctrl.on_server_ready.add(html_view.update)
                                self.ctrl.view_update = html_view.update
                                self.ctrl.reset_camera = html_view.reset_camera
                                self.ctrl.view_capture_image = html_view.capture_image

                                if use_plotter:
                                    self.ctrl.view_widgets_set = html_view.set_widgets
                                    html_view.set_widgets(
                                        [self.render_window.plotter.renderer.axes_widget])

                                vuetify.VProgressLinear(
                                    model=("loading_progress", 0),
                                    indeterminate=True,
                                    color="primary",
                                    width=2,
                                    height=2,
                                )

                        # 右侧播放控件
                        with vuetify.VCol(cols=3):
                            with vuetify.VCard(classes="mb-4"):
                                with vuetify.VCardTitle("4D CTA"):
                                    vuetify.VSpacer()
                                    with vuetify.VBtn(icon=True, click=(self.toggle_playback,)):
                                        vuetify.VIcon("{{ is_playing ? 'mdi-pause' : 'mdi-play' }}")

                                with vuetify.VCardText():
                                    vuetify.VSlider(
                                        v_model=("playback_progress", 0),
                                        min=0,
                                        max=1,
                                        step=0.01,
                                        hide_details=True,
                                        input=("is_playing = false; state.flush()",),
                                        change=("is_playing = false; state.flush()",)
                                    )
                                    vuetify.VSlider(
                                        v_model=("playback_speed", 1.0),
                                        min=0.1,
                                        max=2.0,
                                        step=0.1,
                                        label="速度",
                                        hide_details=True,
                                    )

    def toggle_playback(self):
        self.state.is_playing = not self.state.is_playing
        self.state.flush()


def main(server=None, **kwargs):
    # Get or create server
    if server is None:
        server = get_server()

    if isinstance(server, str):
        server = get_server(server)

    # Set client type
    server.client_type = "vue2"

    # Init application
    app = Workbench(server, "UnionStrong")
    app.setupui()

    #app.load(r'E:\test_data\CTA\cta.mha', "cta_image")
    # app.load(r'E:\test_data\CTA\vessel_smooth.vtp', "vessel_surface")
    # pointset = PointSetData()
    # pointset_node = DataNode("pointset")
    # pointset_node.set_data(pointset)
    # app.data_storage.add_node(pointset_node)

    # Start server
    server.start(**kwargs)


if __name__ == "__main__":
    main()
