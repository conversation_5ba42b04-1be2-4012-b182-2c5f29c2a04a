default:
  image: *************:808/ugs-ci/ubuntu18_py38:230330
  before_script:
    # 单独为ctp4d安装ninja-build
    - apt-get update && apt-get install -y ninja-build
    - pip3 install ninja
    # 原templates/ugs-ci.yml的before_script
    - DEST_DIR="${APP_DIR}/${CI_PROJECT_NAME}"
    - echo "host:${UGS_HOST_IP}, dir:${DEST_DIR}"
    - CUR_DIR=$(pwd)
    - ls -alrt
    - PACKAGE_VERSION=$(echo ${CI_COMMIT_BRANCH} | awk -F '-' '{print $2}')
    - echo "package version:${PACKAGE_VERSION}"
include:
  - project: "uguard-stroke/templates"
    ref: release-1.8.5
    file: "/gitlab/ugs-ci.yml"