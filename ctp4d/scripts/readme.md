## Build Docker Image

```bash
docker build --no-cache -t pymipf-app .
```

## Start Container

```bash
sudo docker run -d \
  --name pymipf_container \
  --security-opt seccomp=unconfined \
  --ulimit nproc=65535 \
  --ulimit nofile=65535 \
  --gpus all \
  -p 8080:8080 \
  -p 8081:8081 \
  -p 8082:8082 \
  -p 8083:8083 \
  harbor.unionstrongtech.com/pymipf/pymipf-app:latest
```

## 为你的本地镜像添加符合Harbor仓库规范的标签：

```bash
docker tag pymipf-app harbor.unionstrongtech.com/pymipf/pymipf-app:latest
```

## Push Image to Docker Hub

```bash
docker push harbor.unionstrongtech.com/pymipf/pymipf-app:latest
```

## Pull Image from Docker Hub

```bash
docker pull harbor.unionstrongtech.com/pymipf/pymipf-app:latest
```

## 启动web服务
```bash
python examples/MultiView.py --server --host 0.0.0.0 --port 8081
```
